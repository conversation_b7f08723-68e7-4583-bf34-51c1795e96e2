@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.profile-preview {
  display: flex;
  flex-direction: column;
  flex-grow: 1;

  .resume {
    margin: 0;
  }

  @include desktop {
    position: relative;
    max-width: 826px;
    display: grid;
    grid-template-columns: minmax(0, 1fr) minmax(0, 1fr);
    grid-auto-flow: column;
  }
}

.profile-preview-no-images {
  display: unset;
}

.profile-gallery {
  display: initial;
  background-color: $grey-10;
  border-radius: 0;
  overflow: hidden;
  height: 100%;
  width: 100%;

  @include desktop {
    border-radius: 10px 0 0 10px;
  }
}

.profile-info {
  background-color: $white;
  width: 100vw;

  @include desktop {
    border-radius: 0 10px 10px 0;
    flex-flow: column nowrap;
    flex: 0 0 50%;
    padding: $space-40 0 16px;
    display: flex;
    width: 100%;
    max-height: 550px;
  }
}

.profile-info-header {
  display: none;

  @include desktop {
    display: flex;
    flex-direction: column;
    padding: 0 $space-20;
    gap: $space-5;
  }
}

.profile-info-details {
  display: flex;
  justify-content: space-between;
  padding: $space-15 $space-20 $space-20;
  background-color: $violet-80;
  flex-wrap: wrap;
  gap: $space-10;

  @include desktop {
    padding: 0 $space-20 $space-15;
    background-color: $white;
  }
}

.slider-slide {
  width: 100%;
  height: 100%;
  display: block;
  overflow: hidden;
}

.slider-slide-item {
  position: relative;
  overflow: hidden;
  line-height: 0;

  img {
    width: 100%;
    height: auto;
    object-fit: contain;
    aspect-ratio: 600 / 800;
  }

  .placeholder {
    aspect-ratio: 600 / 800;
    background-repeat: no-repeat;
    background-position: bottom;
    background-size: 100% 90%;
    opacity: 0.6;

    &.male {
      background-image: url('#{$assetUrl}/assets/placeholders/male-height.svg');
    }

    &.female {
      background-image: url('#{$assetUrl}/assets/placeholders/female-height.svg');
    }
  }
}

.rating {
  display: flex;
  align-items: center;
  gap: 3px;
  color: $white;

  @include desktop {
    color: $black;
  }
}

.star {
  filter: invert(71%) sepia(97%) saturate(1417%) hue-rotate(359deg)
    brightness(101%) contrast(104%);
}

.count {
  display: inline-block;
  vertical-align: middle;
  font-weight: 300;
  font-size: 14px;
}

.name {
  display: inline-block;
  font-weight: 700;
  font-size: 30px;
  margin: 0;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 320px;
}

.id {
  font-size: 14px;
  font-weight: 300;
  text-transform: uppercase;
  opacity: 0.7;
  color: $white;

  @include desktop {
    color: $grey-100;
  }
}

.profile-overview {
  padding: 0 $space-20 $space-5;
}

.profile-overview-pro {
  padding: $space-10 $space-20 $space-5;
  display: flex;
  flex-direction: column;
  gap: $space-10;
}

.profile-actions {
  display: flex;
  align-items: center;
  padding: $space-20;
  border-top: 1px solid $grey-40;
  justify-content: center;
  flex-direction: column;

  &.center-content {
    justify-content: center;
  }

  @include desktop {
    padding: $space-20 $space-20 $space-5;
    justify-content: space-between;
    flex-direction: row;
  }
}

.profile-link {
  color: $blue-100;
  font-size: 16px;
  font-weight: 400;
  line-height: 1;
}

.profile-link-desktop {
  flex: 1;
  text-align: center;
  display: none;
  max-width: 180px;

  @include desktop {
    display: block;
  }
}

.close {
  background-color: transparent;
  border-style: none;
  cursor: pointer;
  outline: none;
  position: absolute;
  right: 14px;
  top: 14px;
  height: 28px;
  opacity: 0.5;
  transition: opacity 0.2s ease-in-out;
  width: 28px;
  z-index: 10;

  &:hover {
    opacity: 1;
    transition: opacity 0.2s ease-in-out;
  }

  &::before {
    background-color: $black;
    transform: rotate(45deg);
    content: '';
    height: 1px;
    left: 50%;
    margin-left: -$space-10;
    position: absolute;
    top: 50%;
    width: 20px;
  }

  &::after {
    background-color: $black;
    transform: rotate(-45deg);
    content: '';
    height: 1px;
    left: 50%;
    margin-left: -$space-10;
    position: absolute;
    top: 50%;
    width: 20px;
  }
}

.profile-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.profile-button {
  display: none;

  @include desktop {
    display: initial;
  }
}

.profile-button-mobile {
  color: $blue-100;
  font-size: 16px;
  font-weight: 400;
  line-height: 1;
  text-align: right;

  @include desktop {
    display: none;
  }
}

.contact-button {
  display: none;

  @include desktop {
    display: initial;
  }
}

.contact-btn-container-mobile {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 $space-20 $space-20;
  flex-direction: column;
  background-color: $violet-80;

  @include desktop {
    display: none;
  }
}

.separator {
  width: 100%;
  border-top: 1px solid $grey-100;
  padding: 0 $space-20 $space-20;

  @include desktop {
    border-color: $white;
  }
}

.accordion-container {
  max-height: 320px;
  overflow: scroll;
}

.btn-add-contact-container {
  padding-right: $space-10;
  margin-right: $space-10;
  border-right: 1px solid $grey-100;
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: $space-5;
  font-size: 14px;
  color: $blue-80;
  font-weight: 400;

  &:hover {
    text-decoration: underline;
  }

  @include desktop {
    border-color: $grey-20;
  }
}

.btn-container-mobile {
  display: flex;
  width: 100%;
  gap: $space-20;

  .button {
    flex: 1;
  }
}

.notes {
  display: flex;
  flex-direction: column;
  gap: $space-5;
  padding: $space-10 $space-20;
}

.role {
  display: flex;
  align-items: center;
  padding: $space-10 $space-20;
  gap: $space-5;
  justify-content: center;
  background-color: $violet-80;
  overflow: hidden;

  @include desktop {
    max-width: 400px;
    width: 100%;
    background-color: $white;
    justify-content: flex-start;
    border-top: 1px solid $grey-20;
  }
}

.role-title {
  font-weight: 700;
  color: $grey-40;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  @include desktop {
    color: $black;
  }
}

.match {
  display: flex;
  align-items: center;
  gap: $space-5;
  color: $red-80;
  white-space: nowrap;

  &.full {
    color: $green-80;
  }
}

.invited,
.declined,
.approved,
.applied {
  .accordion-container {
    max-height: 300px;
  }

  .profile-info-details {
    padding-bottom: $space-10;
  }
}

.dot-divider {
  &::before {
    content: '\2022';
    padding-left: 8px;
    padding-right: 8px;
    color: $grey-60;
    font-weight: 400;
  }
}

.contacts {
  display: flex;
  padding: $space-10 $space-20;
  font-size: 14px;
  flex-wrap: wrap;
}

.link {
  color: $blue-100;
  font-weight: 400;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.action-container {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  padding-left: 80px;
  flex-wrap: wrap;

  @include xlarge {
    padding-left: 0;
    flex-wrap: nowrap;
  }
}

.match-container {
  display: flex;
  align-items: center;
  justify-content: center;

  @include xlarge {
    width: 100%;
    padding-left: 0;
  }
}

.actions {
  display: flex;
  align-items: center;
  gap: 2px;
  flex: 1;
  width: 100%;
  padding-top: $space-20;

  @include desktop {
    padding-top: 0;
  }
}

.action {
  flex: 1;
  background-color: transparent;
  display: flex;
  align-items: center;
  height: 34px;
  cursor: pointer;
  justify-content: center;
  border: 2px solid $grey-40;
  padding: 0 $space-20;
  transition: all 0.2s ease-in-out;

  .icon-action {
    color: $grey-100;
    transition: all 0.2s ease-in-out;
    transform: scale(0.7);
  }

  &:last-of-type {
    border-top-right-radius: 38px;
    border-bottom-right-radius: 38px;
  }

  &:first-of-type {
    border-top-left-radius: 38px;
    border-bottom-left-radius: 38px;
  }

  &:hover {
    border-color: $violet-60;

    .icon-action {
      color: $violet-60;
    }
  }

  @include desktop {
    padding: 0 $space-50;

    .icon-action {
      transform: none;
    }
  }
}

/* stylelint-disable */

.approved {
  .action {
    &:first-of-type {
      cursor: default;
      border-color: $green-80;

      .icon-action {
        color: $green-80;
      }

      &:hover {
        border-color: $green-80;
      }
    }
  }
}

.declined {
  .action {
    &:last-of-type {
      cursor: default;
      border-color: $red-80;

      .icon-action {
        color: $red-80;
      }

      &:hover {
        border-color: $red-80;
      }
    }
  }
}

/* stylelint-enable */
