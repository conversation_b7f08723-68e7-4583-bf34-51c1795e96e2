'use client';
import styles from './UploadImage.module.scss';
import React, { memo } from 'react';
import cn from 'classnames';

const UploadImage = ({
  imageType = 'additional',
  title = '',
  selectFile,
  isMainImage = false,
}) => {
  const getDefaultTitlePhotoURL = () => {
    return !isMainImage
      ? '/assets/icons/icon-add-2.svg'
      : `/assets/placeholders/circle-casting-director.svg`;
  };

  const getDefaultMobileTitlePhotoURL = () => {
    return !isMainImage
      ? '/assets/icons/icon-add-2.svg'
      : `/assets/placeholders/circle-casting-director-mobile.svg`;
  };

  return (
    <div
      className={cn(styles['placeholder-image-container'], {
        [styles['placeholder-status-image']]: isMainImage,
      })}
    >
      {isMainImage && (
        <button className={styles['upload-button']} onClick={selectFile}>
          Upload photo
        </button>
      )}
      <div
        className={cn(
          styles['placeholder-image-box'],
          imageType === 'additional'
            ? styles['default-placeholder-image-box']
            : '',
        )}
        onClick={selectFile}
      >
        {isMainImage && (
          <div
            className={cn(styles['placeholder-image-title'], {
              [styles['placeholder-image-title-main']]: isMainImage,
            })}
          >
            {title}
          </div>
        )}

        <picture
          className={cn(styles['main-image'], styles['main-image-headshot'])}
        >
          <source
            srcSet={getDefaultTitlePhotoURL()}
            media="(min-width: 768px)"
          />
          <img
            src={getDefaultMobileTitlePhotoURL()}
            alt={`Default ${title} photo preview`}
          />
        </picture>
        <div className={styles['default-placeholder-image-info']}>
          {isMainImage && (
            <span className={styles['placeholder-image-link']}>Upload</span>
          )}
          Min resolution:&nbsp;
          <b>600x800 px</b>
        </div>
      </div>
    </div>
  );
};

export default memo(UploadImage);
