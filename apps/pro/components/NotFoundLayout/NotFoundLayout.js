'use client';
import React, { useEffect } from 'react';
import styles from './NotFoundLayout.module.scss';
import { Button } from '@components';
import cn from 'classnames';
import { MainLayout } from '@components/Layouts';
import Link from 'next/link';
import Image from 'next/image';
import { Amp } from '@services/amp';

const NotFoundLayout = () => {
  useEffect(() => {
    Amp.track(Amp.events.view404);
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isSaleHeaderVisible
      isUserMenuVisible
      isMobileMenuVisible
      isFooterVisible
    >
      <div className={styles['content']}>
        <header className={styles['header']}>
          <Link href="/" passHref className={styles['logo-link']}>
            <Image
              src="/assets/logo/logo-4.svg"
              alt="logo"
              width={94}
              height={21}
            />
          </Link>
        </header>
        <div className={styles['main-content']}>
          <section className={styles['main-block']}>
            <h1 className={styles['title']}>oops...</h1>
            <div className={styles['subtitle']}>Something went wrong</div>
            <div className={styles['main-text']}>Let&apos;s try again</div>
            <Link href={process.env.publicUrl}>
              <Button
                type="button"
                kind="primary"
                color="blue"
                label="go to homepage"
                minWidth={'220px'}
                shadow={false}
                className={styles['back-btn']}
              />
            </Link>
            <div className={cn(styles['main-text'], styles['text-or'])}>
              or...
            </div>
          </section>
        </div>
        <div className={styles['body']}>
          <div className={styles['to-do-section']}>
            <div className={styles['to-do-box']}>
              <div className={styles['to-do-title']}>Explore Casting Calls</div>
              <div className={styles['to-do-text']}>
                Browse through our diverse base of casting calls and apply now
                to land an audition.
              </div>
              <Link
                href={`${process.env.publicUrl}/castingcalls`}
                className={styles['to-do-btn']}
              >
                <Button
                  type="button"
                  kind="secondary"
                  color="blue"
                  label="Apply for Casting Calls"
                  shadow={false}
                />
              </Link>
            </div>
            <div className={styles['to-do-box']}>
              <div className={styles['to-do-title']}>Read Reviews</div>
              <div className={styles['to-do-text']}>
                Learn about our talent&apos;s success with allcasting and get
                inspired.
              </div>
              <Link
                href={`${process.env.publicUrl}/reviews`}
                className={styles['to-do-btn']}
              >
                <Button
                  type="button"
                  kind="secondary"
                  color="blue"
                  label="Read Now"
                  shadow={false}
                />
              </Link>
            </div>
            <div className={styles['to-do-box']}>
              <div className={styles['to-do-title']}>Learn at Academy</div>
              <div className={styles['to-do-text']}>
                Our blog provides industry knowledge by professionals and latest
                news.
              </div>
              <Link
                href={`${process.env.publicUrl}/academy`}
                className={styles['to-do-btn']}
              >
                <Button
                  type="button"
                  kind="secondary"
                  color="blue"
                  label="Learn More"
                  shadow={false}
                />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default NotFoundLayout;
