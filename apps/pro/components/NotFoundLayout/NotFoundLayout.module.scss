@use '@styles/variables' as *;
@use '@styles/mixins' as *;

.content {
  background-image: $gradient-port-gore;
  display: flex;
  flex-flow: column nowrap;
  position: relative;
  z-index: 1;
  justify-content: stretch;
  flex: 1 1 auto;

  @include tablet {
    padding-bottom: $space-60;
  }
}

.header {
  display: block;
  padding: 24px 0 $space-25;
  background-color: white;

  @include desktop {
    display: none;
  }
}

.logo-link {
  display: block;
}

.logo {
  display: block;
  width: 93px;
  height: 21px;
  color: $violet-100;
  cursor: pointer;
  margin: auto;
}

.main-content {
  display: flex;
  flex-flow: column nowrap;
  max-width: 1240px;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  position: relative;

  &::before {
    content: '';
    position: relative;
    top: -3px;
    right: 0;
    left: 0;
    width: 338px;
    height: 211px;
    margin-left: auto;
    margin-right: auto;
    background: url('#{$assetUrl}/assets/404/image-404.webp') 100% 100%
      no-repeat;
    background-size: contain;
    z-index: 2;
  }

  @include desktop {
    flex-flow: row nowrap;
    padding: 0 0 $space-30;
    min-height: 473px;
    overflow: hidden;

    &::before {
      position: absolute;
      width: 791px;
      height: 542px;
      left: auto;
      top: -40px;
      right: -18px;
    }
  }
}

.main-block {
  position: relative;
  display: flex;
  flex-flow: column nowrap;
  text-align: center;
  margin: (-$space-35) auto 0;
  align-items: center;
  padding-bottom: 26px;
  z-index: 2;

  @include desktop {
    order: 1;
    text-align: left;
    margin-left: 70px;
    padding-top: 122px;
    padding-bottom: 0;
    align-items: flex-start;
  }
}

.title {
  color: white;
  font-weight: 700;
  font-size: 64px;
  margin: 0 0 6px;
  line-height: 1.2;

  @include tablet {
    margin-bottom: $space-15;
  }
}

.subtitle {
  position: relative;
  font-size: 26px;
  font-weight: 800;
  color: white;
  margin-bottom: 7px;
}

.main-text {
  font-size: 18px;
  font-weight: 500;
  color: white;
  letter-spacing: 0.5px;
}

button.back-btn {
  box-shadow: $shadow-btn-back;
  margin-bottom: $space-20;
  margin-top: $space-40 - 2px;

  @include tablet {
    margin-bottom: 32px;
    margin-top: 42px;
  }
}

.text-or {
  color: $violet-80;
}

.images-block {
  order: 1;
  margin-bottom: 13px;

  @include desktop {
    order: 2;
    margin-bottom: 0;
  }
}

.body {
  width: 100%;
  max-width: 1280px;
  padding-left: $space-20;
  padding-right: $space-20;
  margin: 0 auto;
}

.to-do-section {
  position: relative;
  background-color: $violet-80;
  border-radius: 10px;
  display: grid;
  grid-gap: $space-40;
  margin: 0 auto $space-50;
  padding: 34px 33px;

  @include tablet {
    grid-template-columns: 1fr 1fr 1fr;
    padding: 33px 70px;
  }

  @include desktop {
    grid-gap: 100px;
  }
}

.to-do-box {
  position: relative;
  display: flex;
  flex-flow: column nowrap;
  align-items: center;
  text-align: center;

  @include tablet {
    align-items: flex-start;
    text-align: left;
  }
}

.to-do-title {
  font-size: 18px;
  font-weight: 700;
  color: white;
  margin-bottom: 9px;

  @include tablet {
    margin-bottom: $space-10;
  }
}

.to-do-text {
  font-size: 16px;
  line-height: 1.4;
  font-weight: 400;
  color: white;
  margin-bottom: 22px;
  min-height: 50px;
}

.to-do-btn {
  display: inline-block;
  min-width: 200px;
  margin-top: auto;

  button {
    display: block;
    width: 100%;
    box-shadow: none;
    text-transform: capitalize;
    padding-left: $space-5;
    padding-right: $space-5;
  }

  @include tablet {
    min-width: 100%;
  }

  @include desktop {
    min-width: 200px;
  }
}
