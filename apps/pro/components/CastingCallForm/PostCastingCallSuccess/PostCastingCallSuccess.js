'use client';
import React, { memo } from 'react';
import styles from './PostCastingCallSuccess.module.scss';
import { Button } from '@components';
import { useRouter } from 'next/navigation';
import { CONTACTS } from '@constants/contacts';
import Image from 'next/image';
import success from '../../../public/assets/casting-calls/success.png';

const PostCastingCallSuccess = ({ castingCallUrl }) => {
  const router = useRouter();

  const goToCastingCall = () => {
    router.push(castingCallUrl.replace('castingcalls', 'castingcall'));
  };

  return (
    <div className={styles['success-container']}>
      <div className={styles['success-block']}>
        <Image src={success} alt="" />
        <h1 className={styles['success-heading']}>Congratulations!</h1>
        <p>
          You&apos;ve successfully posted your casting call! Kindly be aware
          that Your casting notice may undergo a review process that could take
          up to 48 hours for approval.
        </p>
        <p>
          If you have any questions or need assistance, feel free to reach out
          to our Casting Concierge:
        </p>
        <div>
          Email: <a href={`mailto:${CONTACTS.Email}`}>{CONTACTS.Email}</a>
          <br />
          Phone: <a href={`tel:${CONTACTS.Phone}`}>{CONTACTS.PhonePreview}</a> (
          {CONTACTS.WorkingTime})
        </div>
        <p>
          Thank you for trusting us with your casting needs. We&apos;re here to
          make your experience exceptional!
        </p>
        <Button
          type="button"
          kind="primary"
          color="blue"
          label="View casting call"
          minWidth="220px"
          shadow={false}
          onClick={goToCastingCall}
          className={styles['success-button']}
        />
      </div>
    </div>
  );
};

export default memo(PostCastingCallSuccess);
