@use '@styles/mixins' as *;
@use '@styles/variables' as *;

.container {
  display: grid;
  grid-gap: $space-10;

  @include tablet {
    margin: 0 $space-20;
  }
}

.preview {
  @include tablet {
    grid-template-columns: 1fr 1fr;
    column-gap: $space-25;
  }
}

.not-found {
  display: flex;
  flex-direction: column;
  gap: $space-20;
  align-items: center;
  padding: $space-20;
  box-shadow: 0 5px 10px #0000001a;
  text-align: center;
}

.not-found-title {
  font-size: 24px;
  font-weight: 700;
}

.mobile-post-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: $space-20;

  @include desktop {
    display: none;
  }
}

.banner {
  width: 100%;
  cursor: pointer;
  height: auto;

  @include mobilexl {
    width: 100%;
  }
}

.assist-banner {
  display: flex;
  align-items: center;
  background-color: $grey-10;
  padding: $space-20;
  margin-bottom: $space-20;
  justify-content: center;
  gap: $space-10;
  font-size: 14px;
  border-radius: 4px;
}

.link {
  color: $blue-100;
  font-weight: 400;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}
