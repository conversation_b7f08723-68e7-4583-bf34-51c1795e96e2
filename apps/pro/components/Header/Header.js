'use client';
import React, { memo, useEffect, useState } from 'react';
import Logo from '/public/assets/logo/logo-2.svg';
import styles from './Header.module.scss';
import MenuItem from './MenuItem/MenuItem';
import Link from 'next/link';
import { desktopLinks } from '@constants/menuLinks/menu-links';
import { Button } from '@components';
import { Amp } from '@services/amp';
import { useModalContext } from '@contexts/ModalContext';
import { usePathname } from 'next/navigation';

const Header = () => {
  const [showPostButton, setShowPostButton] = useState(true);
  const { toggleShowPostCastingCallModal } = useModalContext();
  const path = usePathname();

  const onPostCastingCall = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'post casting call',
      scope: Amp.element.scope.global,
      section: Amp.element.section.header,
      type: Amp.element.type.button,
    });

    toggleShowPostCastingCallModal();
  };

  useEffect(() => {
    setShowPostButton(
      !['/create', '/edit'].some((allowedPath) => path.includes(allowedPath)),
    );
  }, [path]);

  return (
    <div className={styles.header}>
      <Link
        href="/castingcalls"
        passHref
        aria-label="allcasting"
        className={styles['logo-link']}
      >
        <Logo className={styles.logo} />
      </Link>
      <div className={styles.navbar}>
        {desktopLinks.map(({ label, href }) => (
          <MenuItem key={label} label={label} href={href} />
        ))}
        <div className={styles['button-container']}>
          <div className={styles['header-divider-dot']} />
          {showPostButton && (
            <Button
              label="Post casting call"
              color="green-gradient"
              minWidth={'200px'}
              onClick={onPostCastingCall}
            />
          )}
          <Button
            label="Contact concierge"
            kind="secondary"
            minWidth={'200px'}
            color="green-blue"
            type="link"
            href="/contact"
          />
        </div>
      </div>
    </div>
  );
};

export default memo(Header);
