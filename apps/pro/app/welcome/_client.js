'use client';
import React, { useEffect } from 'react';
import { MainLayout, PageLayout } from '@components';
import { useModalContext } from '@contexts/ModalContext';
import { track } from '@amplitude/analytics-browser';
import { GTM_ACTIONS, GTM_CATEGORIES, GTM_EVENTS } from '@constants/analytics';
import styles from '@styles/welcome.module.scss';
import Circle from '../../public/assets/welcome/circle.svg';
import Image from 'next/image';
import cn from 'classnames';
import Link from 'next/link';
import { CONTACTS } from '@constants/contacts';
import { Amp } from '@services/amp';

const Content = () => {
  const { toggleShowPostCastingCallModal } = useModalContext();

  useEffect(() => {
    track(GTM_EVENTS.interaction, {
      target: GTM_CATEGORIES.welcome,
      action: GTM_ACTIONS.show,
      label: 'welcome',
    });
  }, []);

  useEffect(() => {
    Amp.track(Amp.events.viewDirectorWelcome);
  }, []);

  return (
    <PageLayout className={styles['page-layout']}>
      <header className={styles.header}>
        <div className={styles['header-text']}>
          <h1 className={styles.title}>Your Account is Ready!</h1>
          <p className={styles['banner-text']}>
            Congratulations! Your account is now active, and you’re just steps
            away from discovering the perfect talent for your project.
          </p>
        </div>

        <div className={styles['image-container']}>
          <svg
            width={0}
            viewBox="0 0 266 373"
            xmlns="http://www.w3.org/2000/svg"
          >
            <clipPath id="shape">
              <rect width="100%" height="50%" />
              <circle cx="50%" cy="198" r="175" />
            </clipPath>
          </svg>
          <svg
            width={0}
            viewBox="0 0 160 224"
            xmlns="http://www.w3.org/2000/svg"
          >
            <clipPath id="shape-mob">
              <rect width="100%" height="50%" />
              <circle cx="50%" cy="124" r="100" />
            </clipPath>
          </svg>
          <Circle className={styles.ellipse} />
          <Image
            className={styles.image}
            src="/assets/welcome/woman.webp"
            width={160}
            height={224}
            alt="woman"
            priority
          />
        </div>
      </header>
      <div className={styles.content}>
        <h2>Next Steps to Get Started</h2>
        <div className={styles.cards}>
          <article className={styles.card}>
            <button
              className={cn(styles.button, styles.link)}
              onClick={toggleShowPostCastingCallModal}
            >
              Post Casting Call/Project
            </button>
            <p>
              Quickly create a free casting call to connect with the right
              talent. It&apos;s simple and takes just a few minutes.
            </p>
          </article>
          <article className={styles.card}>
            <Link className={styles.link} href="/talent">
              Search For Talent →
            </Link>
            <p>
              Explore our database of 1+ million diverse talent across the U.S.
              and Canada. Use advanced filters to find exactly what you&apos;re
              looking for.
            </p>
          </article>
          <article className={styles.card}>
            <Link className={styles.link} href="/director">
              Complete Your Profile →
            </Link>
            <p>
              Make the most of your account by adding details. A complete
              profile attracts better-matched talent and increases visibility.
            </p>
          </article>
        </div>
        <h2>Need Assistance?</h2>
        <p className={styles.assistance}>
          Our team is here to help! If you have any{' '}
          <span className={styles.nobreak}>
            questions or need support, contact us at
          </span>{' '}
          <a
            className={styles.blue}
            target="_blank"
            href={`mailto:${CONTACTS.Email}`}
          >
            {CONTACTS.Email}
          </a>
          .
        </p>
      </div>
    </PageLayout>
  );
};

const WelcomeClient = () => {
  return (
    <MainLayout isDefaultHeaderVisible isMobileMenuVisible isUserMenuVisible>
      <Content />
    </MainLayout>
  );
};

export default WelcomeClient;
