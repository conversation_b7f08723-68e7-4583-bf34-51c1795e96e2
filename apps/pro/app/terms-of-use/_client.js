'use client';
import React, { useEffect } from 'react';
import { MainLayout, PageLayout, TermsOfUse as Terms } from '@components';
import { Amp } from '@services/amp';

const TermsOfUseClient = () => {
  useEffect(() => {
    Amp.track(Amp.events.viewTermsOfUse);
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
      isFooterVisible
    >
      <PageLayout>
        <Terms isFixed />
      </PageLayout>
    </MainLayout>
  );
};

export default TermsOfUseClient;
