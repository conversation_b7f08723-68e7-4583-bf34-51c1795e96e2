'use client';
import React, { useEffect } from 'react';
import { Contacts, MainLayout } from '@components';
import { ContactsProvider } from '@contexts/ContactContext';
import { Amp } from '@services/amp';

const ContactsClient = ({
  groups,
  contacts,
  canContactTalent,
  contactsTotal,
  page,
}) => {
  useEffect(() => {
    Amp.track(Amp.events.viewContacts, {
      page,
    });
  }, [page]);

  return (
    <ContactsProvider
      defaultGroups={groups}
      defaultContacts={contacts}
      defaultContactsTotal={contactsTotal}
    >
      <MainLayout isDefaultHeaderVisible isMobileMenuVisible isUserMenuVisible>
        <Contacts
          canContactTalent={canContactTalent}
          defaultGroups={groups}
          page={page}
          paginationEnabled
        />
      </MainLayout>
    </ContactsProvider>
  );
};

export default ContactsClient;
