'use client';
import React, { useEffect } from 'react';
import { Contacts, MainLayout } from '@components';
import { ContactsProvider } from '@contexts/ContactContext';
import { formatContacts } from '@utils/contactHelpers';
import { Amp } from '@services/amp';

const ContactsClient = ({
  groups,
  group,
  canContactTalent,
  contactsTotal,
  page,
}) => {
  useEffect(() => {
    Amp.track(Amp.events.viewContactGroup, {
      page,
      contact_group_id: group?.id,
    });
  }, [group?.id, page]);

  return (
    <ContactsProvider
      defaultGroups={groups}
      defaultContacts={formatContacts(group.contacts?.items || [])}
      defaultContactsTotal={contactsTotal}
      defaultGroup={group}
    >
      <MainLayout isDefaultHeaderVisible isMobileMenuVisible isUserMenuVisible>
        <Contacts
          canContactTalent={canContactTalent}
          defaultGroups={groups}
          page={page}
          paginationEnabled
          isGroup
        />
      </MainLayout>
    </ContactsProvider>
  );
};

export default ContactsClient;
