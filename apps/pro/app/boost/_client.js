'use client';
import React, { useEffect, useState } from 'react';
import {
  Button,
  HeaderMobile,
  MainLayout,
  ModalBoost,
  PageLayout,
} from '@components';
import styles from '@styles/boost.module.scss';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Amp } from '@services/amp';

const BoostClient = () => {
  const [showBoostModal, setShowBoostModal] = useState(false);
  const router = useRouter();

  const toggleShowBoostModal = () => {
    setShowBoostModal(!showBoostModal);
  };

  const navigateToCastingCalls = () => {
    router.push('/castingcalls');
  };

  useEffect(() => {
    Amp.track(Amp.events.viewBoost);
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isFooterVisible
      isMobileMenuVisible
      isUserMenuVisible
    >
      <HeaderMobile title="About Boosting" onClose={navigateToCastingCalls} />
      <div className={styles['header-container']}>
        <div className={styles.header}>
          <div className={styles['header-content']}>
            <span className={styles.title}>Boost your casting call</span>
            <p className={styles.description}>
              Want to get more attention to your project? Boost your casting
              call on allcasting for free! Only 3 minutes of your time can give
              you so much more applications.
            </p>
            <Button
              onClick={toggleShowBoostModal}
              label="Start now"
              color="orange"
              minWidth="240px"
              shadow={false}
            />
          </div>
          <Image
            className={styles['header-image']}
            src="/assets/boost/applications.webp"
            alt=""
            width={346}
            height={395}
          />
        </div>
      </div>
      <PageLayout>
        <div className={styles['card-container']}>
          <Image
            className={styles['card-top-image']}
            src="/assets/boost/round-top.svg"
            alt=""
            width={816}
            height={25}
          />
          <div className={styles.card}>
            <span className={styles['card-title']}>What do you get?</span>
            <p className={styles['card-description']}>
              Scared of getting too many applications? When using the &quot;Easy
              Apply&quot; option, you tap into the allcasting AI. You&apos;ll
              see a level of precision you&apos;ve never seen before. How it
              works? We automatically sort and categorize the applications based
              on how they match your roles.
            </p>
            <div className={styles['benefits']}>
              <div className={styles.benefit}>
                <Image
                  src="/assets/icons/icon-talent-2.svg"
                  alt=""
                  width={55}
                  height={55}
                />
                <span>Reach thousands of talent in a snap!</span>
              </div>
              <div className={styles.benefit}>
                <Image
                  src="/assets/icons/icon-casting-calls-2.svg"
                  alt=""
                  width={55}
                  height={55}
                />
                <span>Get extra attention to your project!</span>
              </div>
              <div className={styles.benefit}>
                <Image
                  src="/assets/icons/icon-star-2.svg"
                  alt=""
                  width={55}
                  height={55}
                />
                <span>Have crazy results free of charge!</span>
              </div>
            </div>
          </div>
          <Image
            className={styles['card-bottom-image']}
            src="/assets/boost/round-top.svg"
            alt=""
            width={816}
            height={25}
          />
        </div>
        <div className={styles.footer}>
          <div className={styles['boost-container']}>
            <span className={styles['boost-title']}>Why not Start Now</span>
            <p className={styles['boost-description']}>
              Too good to be true? We do everything possible and impossible to
              make our casting directors happy. Otherwise, our casting network
              couldn&apos;t take over the industry, right?
            </p>
            <div className={styles['btn-container']}>
              <Button
                onClick={toggleShowBoostModal}
                label="Boost your casting call"
                color="green-gradient"
                minWidth="240px"
                shadow={false}
              />
            </div>
          </div>
        </div>
      </PageLayout>
      {showBoostModal && (
        <ModalBoost
          onClose={toggleShowBoostModal}
          isInBoostPage
          showWarningDefault
        />
      )}
    </MainLayout>
  );
};

export default BoostClient;
