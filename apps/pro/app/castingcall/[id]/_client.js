'use client';
import React, { useEffect, useState } from 'react';
import styles from '@styles/castingcall.module.scss';
import {
  Accordion,
  Attachment,
  Button,
  CastingCallList,
  CastingCallRole,
  CategoryList,
  ContactConcierge,
  HeaderMobile,
  MainLayout,
  ModalAttachmentGallery,
  ModalBoost,
  ModalEnableEasyApplication,
  PaymentLabel,
} from '@components';
import { useRouter } from 'next/navigation';
import { STATUS, TYPE } from '@constants/castingCalls';
import { PageLayout } from '@components/Layouts';
import Image from 'next/image';
import Link from 'next/link';
import cn from 'classnames';
import dayjs from 'dayjs';
import { Amp } from '@services/amp';

const CastingCallClient = ({
  relatedCastingCalls,
  castingCall,
  profileUrl,
  sideMenuItems,
  roles,
  isExpired,
  showPaymentLabel,
  isStopSubmissionsRequest,
}) => {
  const [type, setType] = useState(castingCall.type);
  const [showAttachmentGallery, setShowAttachmentGallery] = useState(false);
  const [clickedImageIndex, setClickedImageIndex] = useState(null);
  const [showBoostModal, setShowBoostModal] = useState(false);
  const [showEnableEasyApplication, setShowEnableEasyApplication] = useState(
    isStopSubmissionsRequest,
  );
  const [isBoosted, setIsBoosted] = useState(castingCall.is_boost_requested);
  const router = useRouter();

  useEffect(() => {
    Amp.track(Amp.events.viewCastingCall, {
      casting_call_id: castingCall.id,
      casting_call_category: castingCall.category?.name,
    });
  }, [castingCall]);

  const handleNavigateBack = () => {
    router.push('/castingcalls');
  };

  const onEdit = () => {
    router.push(`/castingcall/${castingCall.id}/edit`);
  };

  const toggleShowGallery = () => {
    setShowAttachmentGallery(!showAttachmentGallery);
  };

  const onImageClick = (imageId) => {
    if (imageId) {
      const index = castingCall.files
        .filter(({ content_type }) => content_type.split('/')[0] === 'image')
        .findIndex(({ id }) => id === imageId);

      setClickedImageIndex(index);
    }

    toggleShowGallery();
  };

  const onBoostSuccess = () => {
    setIsBoosted(true);
  };

  const toggleShowBoostModal = () => {
    setShowBoostModal(!showBoostModal);
  };

  const toggleEnableEasyApplication = () => {
    setShowEnableEasyApplication(!showEnableEasyApplication);
  };

  const onTypeChange = () => {
    setType(TYPE.Web);
  };

  return (
    <MainLayout isDefaultHeaderVisible isUserMenuVisible isMobileMenuVisible>
      <HeaderMobile
        onClose={handleNavigateBack}
        onClick={onEdit}
        title="Casting Calls"
        showButton
        buttonLabel="Edit"
      />
      <PageLayout>
        <section id="intro" className={styles['casting-call-section']}>
          <div className={styles['casting-call-container']}>
            <div className={styles['casting-call-content']}>
              <div className={styles['casting-call-content-header']}>
                <div className={styles.header} onClick={handleNavigateBack}>
                  <Image
                    src="/assets/icons/icon-arrow-2.svg"
                    alt="icon"
                    width={15}
                    height={15}
                  />
                  <span>View all casting calls</span>
                </div>
                <div className={styles['casting-call-content-menu']}>
                  <Link href="#description" scroll={false}>
                    <span className={styles['casting-call-content-menu-link']}>
                      Description
                    </span>
                  </Link>
                  <Link href="#roles" scroll={false}>
                    <span className={styles['casting-call-content-menu-link']}>
                      Roles
                    </span>
                  </Link>
                </div>
                <h1 id="description" className={styles.title}>
                  {castingCall.title || 'Casting Call'}
                </h1>
                <div className={styles['enable-easy-container']}>
                  {type === TYPE.Email && (
                    <span
                      className={cn(
                        styles['button-enable-easy'],
                        styles.mobile,
                      )}
                      onClick={toggleEnableEasyApplication}
                      role="button"
                    >
                      Enable easy application
                    </span>
                  )}
                  <span className={cn(styles.info, styles.mobile)}>
                    {castingCall.location}
                  </span>
                </div>
                <div className={styles['casting-call-contacts-container']}>
                  <div
                    className={styles['casting-call-contacts-inner-container']}
                  >
                    <div
                      className={
                        styles[
                          'casting-call-contacts-inner-container-detail-strip'
                        ]
                      }
                    >
                      <span className={styles.identifier}>
                        ID: {castingCall.id}
                      </span>
                      <span className={styles.info}>
                        {castingCall.location}
                      </span>
                      <span
                        className={cn(styles.date, {
                          [styles.expired]: isExpired,
                        })}
                      >
                        {isExpired ? (
                          <span className={styles.expired}>Expired</span>
                        ) : (
                          <span>
                            Expires on{' '}
                            {dayjs(castingCall.expiration).format('M/D/YYYY')}
                          </span>
                        )}
                      </span>
                    </div>
                    {type === TYPE.Email && (
                      <span
                        className={styles['button-enable-easy']}
                        onClick={toggleEnableEasyApplication}
                        role="button"
                      >
                        Enable easy application
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <div className={styles['casting-call-details-container']}>
                {showPaymentLabel && (
                  <span>
                    <PaymentLabel
                      paymentAmount={castingCall.payment_amount}
                      paymentPeriod={castingCall.payment_period}
                      paymentCurrency={castingCall.payment_currency}
                      inline
                    />
                  </span>
                )}
                {castingCall.hot && (
                  <>
                    {showPaymentLabel && (
                      <div className={styles['divider-dot']} />
                    )}
                    <span className={styles.hot}>HOT</span>
                  </>
                )}
                {type === TYPE.Web && (
                  <>
                    {(showPaymentLabel || castingCall.hot) && (
                      <div className={styles['divider-dot']} />
                    )}
                    <span
                      className={cn(styles.badge, styles['easy-apply-badge'])}
                    >
                      easy apply
                    </span>
                  </>
                )}
                {castingCall.online_audition && (
                  <>
                    {(showPaymentLabel ||
                      castingCall.hot ||
                      type === TYPE.Web) && (
                      <div className={styles['divider-dot']} />
                    )}
                    <span className={cn(styles.badge, styles['online-badge'])}>
                      online audition
                    </span>
                  </>
                )}
              </div>
              <div className={styles['category-list']}>
                <CategoryList
                  mainCategory={castingCall.category}
                  additionalCategories={castingCall.additional_categories}
                  collapse={false}
                />
              </div>
              <div
                id="description"
                className={styles['casting-call-description-container']}
              >
                <span className={styles['casting-call-description-title']}>
                  <strong>Description:</strong>
                </span>
                <p
                  style={{ whiteSpace: 'pre-wrap' }}
                  dangerouslySetInnerHTML={{ __html: castingCall.description }}
                ></p>
                {castingCall.agent_name && (
                  <div>
                    <span>— </span>
                    {profileUrl ? (
                      <Link href={profileUrl}>{castingCall.agent_name}</Link>
                    ) : (
                      <span>{castingCall.agent_name}</span>
                    )}
                    <span className={styles.star}></span>
                  </div>
                )}
                {castingCall.files?.length > 0 && (
                  <div className={styles['attachment-container']}>
                    {castingCall.files.map((attachment, index) => (
                      <div
                        className={styles.attachment}
                        key={`attachment-${index}`}
                      >
                        <Attachment
                          attachment={attachment}
                          openImageAttachment={onImageClick}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>
              <div
                id="roles"
                className={styles['casting-call-roles-container']}
              >
                <div className={styles['casting-call-roles-title-container']}>
                  <h2 className={styles['casting-call-roles-title']}>
                    {roles.length} role
                    {roles.length === 1 ? '' : 's'}
                  </h2>
                </div>
                {roles.length ? (
                  roles.map((role) => (
                    <CastingCallRole
                      key={role.id}
                      id={role.id}
                      title={role.title}
                      gender={role.gender?.name}
                      age={role.age_from + '-' + role.age_to}
                      ethnicities={role.ethnicities}
                      allEthnicitiesAccepted={role.all_ethnicities}
                      description={role.description}
                      castingCallId={castingCall.id}
                      candidates={role.candidates}
                      status={castingCall.status}
                    />
                  ))
                ) : (
                  <span>No matching roles</span>
                )}
              </div>
            </div>
          </div>
          <div className={styles['casting-call-side-menu-container']}>
            <ContactConcierge />
            {castingCall.status === STATUS.Approved && (
              <div className={styles['boost-btn']}>
                <Button
                  onClick={toggleShowBoostModal}
                  label={isBoosted ? 'Boost Requested' : 'Boost'}
                  kind="secondary"
                  minWidth="200px"
                  disabled={isBoosted}
                />
              </div>
            )}
            <Accordion accordionItems={sideMenuItems}>
              <div className={styles['link-container']}>
                <Link
                  href={`/castingcall/${castingCall.id}/edit`}
                  className={styles.link}
                >
                  Edit Casting Call
                </Link>
              </div>
            </Accordion>
          </div>
        </section>
        {relatedCastingCalls.length > 0 && (
          <div className={styles['casting-call-related-container']}>
            <h3>YOUR CASTING CALLS</h3>
            <CastingCallList items={relatedCastingCalls} isPreview />
          </div>
        )}
        {showAttachmentGallery && (
          <ModalAttachmentGallery
            onClose={toggleShowGallery}
            files={castingCall.files}
            startIndex={clickedImageIndex}
          />
        )}
        {showBoostModal && (
          <ModalBoost
            onClose={toggleShowBoostModal}
            showWarningDefault={type !== TYPE.Web}
            defaultSelectedOptionValue={castingCall.id}
            onBoostSuccess={onBoostSuccess}
          />
        )}
        {showEnableEasyApplication && (
          <ModalEnableEasyApplication
            onClose={toggleEnableEasyApplication}
            id={castingCall.id}
            onTypeChange={onTypeChange}
          />
        )}
      </PageLayout>
    </MainLayout>
  );
};

export default CastingCallClient;
