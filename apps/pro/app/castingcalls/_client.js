'use client';
import React, { useEffect, useState } from 'react';
import {
  CastingCallList,
  FilterMobileHeader,
  Loading,
  MainLayout,
  PageLayout,
  Paginator,
  SwitchGroupFilter,
} from '@components';
import { GTM_ACTIONS, GTM_EVENTS } from '@constants/analytics';
import styles from '@styles/castingcalls.module.scss';
import cn from 'classnames';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useAnalytics } from 'use-analytics';
import { useLiveChat } from '@contexts/LiveChatContext';
import { useAuth } from '@contexts/AuthContext';
import { LIVE_CHAT_IDLE_TIMEOUT } from '@constants/liveChat';
import { Amp } from '@services/amp';
import { CASTING_CALLS_LIMIT } from '@constants/pagination';

const CastingCallsClient = ({
  castingCalls,
  activeCastingCallsTotal,
  castingCallsTotal,
  canContactTalent,
  page,
  statusFilter,
  showReset,
}) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [isFilterFormOpen, setIsFilterFormOpen] = useState(false);
  const { track } = useAnalytics();
  const { initiateLiveChat } = useLiveChat();
  const { isAuthenticated } = useAuth();
  const path = usePathname();
  const searchParams = useSearchParams();
  const url = path + searchParams.toString();

  useEffect(() => {
    setLoading(false);
  }, [url]);

  useEffect(() => {
    const liveChatTimeout = setTimeout(
      () => initiateLiveChat(),
      LIVE_CHAT_IDLE_TIMEOUT.Short,
    );

    return () => clearTimeout(liveChatTimeout);
  }, [isAuthenticated]);

  useEffect(() => {
    Amp.track(Amp.events.viewCastingCallList, {
      page,
    });
  }, [page]);

  const executeScroll = () => {
    window.scrollTo(0, 0);
  };

  const onPaginationChange = async (event, nextPage) => {
    event.preventDefault();
    setLoading(true);
    if (nextPage !== page) {
      const status = searchParams.toString();
      const pageQuery = nextPage > 1 ? `/page-${nextPage}` : '';
      const statusQuery = status ? `?${status}` : '';

      router.push(`/castingcalls${pageQuery}${statusQuery}`);
      executeScroll();
    }
  };

  const toggleFilterForm = () => {
    if (!isFilterFormOpen) {
      Amp.track(Amp.events.elementClicked, {
        name: 'filter results',
        scope: Amp.element.scope.castingCallListPage,
        type: Amp.element.type.button,
      });
      track(GTM_EVENTS.interaction, {
        target: 'castingcalls',
        action: GTM_ACTIONS.filters,
        label: 'opened',
      });
    }
    setIsFilterFormOpen(!isFilterFormOpen);
  };

  const resetFilter = () => {
    setLoading(true);
    router.push('/castingcalls', { scroll: false });
    setIsFilterFormOpen(false);
  };

  const onApplyFilter = (options) => {
    setLoading(true);
    const query = options
      .flatMap((option) => (option.checked ? [option.value] : []))
      .join('-');

    router.push(`/castingcalls${query ? `?status=${query}` : ''}`, {
      scroll: false,
    });

    toggleFilterForm();
    executeScroll();
  };

  const onFieldClick = (value) => {
    Amp.track(Amp.events.elementClicked, {
      name: `'${value}' field clicked`,
      scope: Amp.element.scope.castingCallListPage,
      section: Amp.element.section.filter,
      type: Amp.element.type.checkbox,
    });

    track(GTM_EVENTS.interaction, {
      target: 'castingcalls',
      action: GTM_ACTIONS.filters,
      label: 'search',
    });
  };

  return (
    <MainLayout
      isDefaultHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
      isFooterVisible
    >
      <PageLayout>
        <FilterMobileHeader
          onMobileFilterToggle={toggleFilterForm}
          isFilterFormOpen={isFilterFormOpen}
          showReset={showReset}
          resetCallback={resetFilter}
        />
        <div className={styles.heading}>
          <div className={styles['title-container']}>
            <div className={styles.title}>
              <span>
                {activeCastingCallsTotal?.toLocaleString('en-US')} active
                Casting Calls
              </span>
              <span className={styles['sub-title']}>
                {castingCallsTotal} total Casting Calls
              </span>
            </div>
          </div>
          <div className={styles.control}>
            {showReset && (
              <div onClick={resetFilter} className={styles.reset}>
                reset filters
              </div>
            )}
          </div>
        </div>
        <div className={styles.content}>
          <div className={styles.left}>
            {loading && (
              <div className={styles.loading}>
                <Loading />
              </div>
            )}
            <CastingCallList
              items={castingCalls}
              canContactTalent={canContactTalent}
            />
            <Paginator
              page={page}
              perPage={CASTING_CALLS_LIMIT}
              total={castingCallsTotal}
              prefixUrl="/castingcalls/"
              prefixPage={'page-'}
              handleChange={onPaginationChange}
            />
          </div>
          <div
            className={cn(styles.right, {
              [styles.open]: isFilterFormOpen,
            })}
          >
            <SwitchGroupFilter
              options={statusFilter.options}
              title={statusFilter.title}
              onApply={onApplyFilter}
              onClick={onFieldClick}
            />
          </div>
        </div>
      </PageLayout>
    </MainLayout>
  );
};

export default CastingCallsClient;
