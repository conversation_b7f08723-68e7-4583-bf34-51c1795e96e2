'use client';
import { analytics } from '@utils/analytics';
import { usePathname } from 'next/navigation';
import { useEffect } from 'react';
import { parseQueryString, pixelTracking, xTracking } from '@services/tracking';
import { OneTrustService } from '@services/onetrust';
import { CookieService } from '@services/cookieService';
import { getRandomId } from '@utils/sessionHelpers';

export default function Template({ children }) {
  const path = usePathname();
  // const getLayout = Component.getLayout ?? ((page) => page);

  // Trigger on the initial load, refresh and path change
  useEffect(() => {
    xTracking(parseQueryString(window.location.search));
    pixelTracking(path);
    analytics.page();
    OneTrustService.saveVisitedPath(path);
    OneTrustService.checkTracking();

    const sessionId = CookieService.getSessionIdCookie();
    const redirectPath = CookieService.getRedirectCookie();

    if (!sessionId) {
      CookieService.setSessionIdCookie(getRandomId());
    }

    if (path === redirectPath) {
      CookieService.deleteRedirectCookie();
    }
  }, [path]);

  useEffect(() => {
    OneTrustService.listener();
  }, []);

  return (
    <>
      {/*{getLayout(<Component {...pageProps} />)}*/}
      {children}
    </>
  );
}
