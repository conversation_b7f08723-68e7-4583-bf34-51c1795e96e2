'use client';
import React, { useEffect } from 'react';
import { MainLayout, PageLayout, PrivacyPolicy as Policy } from '@components';
import { Amp } from '@services/amp';

const PrivacyPolicyClient = () => {
  useEffect(() => {
    Amp.track(Amp.events.viewPrivacyPolicy);
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
      isFooterVisible
    >
      <PageLayout>
        <Policy isFixed />
      </PageLayout>
    </MainLayout>
  );
};

export default PrivacyPolicyClient;
