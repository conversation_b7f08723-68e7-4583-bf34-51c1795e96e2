/* FONTS */
// Use css variables in typography.scss for responsiveness
$text-12: 0.75rem;
$text-14: 0.875rem;
$text-16: 1rem;
$text-20: 1.25rem;
$text-24: 1.5rem;
$text-28: 1.75rem;
$text-32: 2rem;
$text-38: 2.375rem;

/* BASIC */
$black: #1a1a1a;
$white: #fff;

/* BLUE */
$blue-100: #00a8e7;
$blue-80: #30bcf0;
$blue-60: #61cbf2;
$blue-40: #93daf5;
$blue-20: #c8ecfa;
$blue-10: #f5fcfe;

/* RED */
$red-100: #ed0000;
$red-80: #f83131;
$red-60: #f75d5d;
$red-40: #fa9696;
$red-20: #ffcbcb;
$red-10: #fff0f0;

/* YELLOW */
$yellow-100: #ea8916;
$yellow-80: #f09830;
$yellow-60: #f2b161;
$yellow-40: #f5c993;
$yellow-20: #fae3c8;
$yellow-10: #fff6eb;

/* GREEN */
$green-100: #389d52;
$green-80: #54d174;
$green-60: #5eeb82;
$green-40: #93f5ac;
$green-20: #c8fad5;
$green-10: #ebfff0;

/* VIOLET */
$violet-100: #120a29;
$violet-80: #281f47;
$violet-70: #372863;
$violet-60: #523d8f;
$violet-40: #8570c2;
$violet-20: #e1dbf0;
$violet-10: #f9f8fc;

/* GREY */
$grey-100: #505562;
$grey-80: #818798;
$grey-60: #abafba;
$grey-40: #d3d6de;
$grey-20: #e8e9ee;
$grey-10: #f5f6fa;

/* OPACITY COLORS */
$white-100-opacity-80: rgb($white, 80%);
$white-100-opacity-60: rgb($white, 60%);
$grey-100-opacity-20: #50556233;
$black-100-opacity-80: rgb($black, 80%);
$black-100-opacity-70: rgb($black, 70%);
$black-100-opacity-60: rgb($black, 60%);
$black-100-opacity-30: #1a1a1a4d;

/* BUTTON GRADIENTS */
$gradient-btn-blue: linear-gradient(
  to right,
  #00baff 0%,
  #3ddafe 50%,
  #00baff 100%
);
$gradient-btn-orange: linear-gradient(
  to right,
  #ea8916 0%,
  #f45f85 50%,
  #ea8916 100%
);
$gradient-btn-red-gradient: linear-gradient(
  to right,
  #f8314d 0%,
  #e06c6c 50%,
  #f8314d 100%
);
$gradient-btn-green-blue: linear-gradient(to right, #5fd568 20%, #1ee3d7 80%);
$gradient-btn-green-gradient: linear-gradient(
  to right,
  #39b54a 0%,
  #1ee3d7 50%,
  #39b54a 100%
);

/* CAROUSEL GRADIENTS */
$gradient-metallic-green-right: linear-gradient(
  to left,
  rgb(37, 115, 20, 40%) 0%,
  rgb(41, 115, 20, 0%) 100%
);
$gradient-metallic-green-left: linear-gradient(
  to right,
  rgb(37, 115, 20, 40%) 0%,
  rgb(41, 115, 20, 0%) 100%
);
$gradient-black-right: linear-gradient(
  to left,
  #0009 0%,
  rgb(0, 0, 0, 0%) 100%
);
$gradient-black-left: linear-gradient(
  to right,
  #0009 0%,
  rgb(0, 0, 0, 0%) 100%
);

/* ICON GRADIENTS */
$gradient-icon-orange: linear-gradient(to top, #f45f85 0%, #ea8916 100%);
$gradient-icon-acting: linear-gradient(135deg, #ea8916 0%, #f45f85 100%);
$gradient-icon-music-videos: linear-gradient(135deg, #e8198b 0%, #6d0986 100%);
$gradient-icon-dancing: linear-gradient(135deg, #b224ef 0%, #7579ff 100%);
$gradient-icon-modeling: linear-gradient(135deg, #cc165f 0%, #6d0b32 100%);
$gradient-icon-promotional-models: linear-gradient(
  135deg,
  #1ee3d7 0%,
  #5fd568 100%
);
$gradient-icon-reality-tv: linear-gradient(135deg, #eb7b4c 0%, #9e8df8 100%);
$gradient-icon-singing: linear-gradient(135deg, #2575fc 0%, #6a11cb 100%);
$gradient-icon-theater: linear-gradient(135deg, #fb8eaf 0%, #f30707 100%);
$gradient-icon-voiceover: linear-gradient(135deg, #3ddafe 0%, #00cfff 100%);
$gradient-icon-pageant: linear-gradient(135deg, #df4aa4 0%, #b54ce7 100%);
$gradient-icon-other: linear-gradient(135deg, #86a1ff 0%, #4d6fc8 100%);
$gradient-icon-extras: linear-gradient(135deg, #4689ee 0%, #4ff4cd 100%);
$gradient-icon-commercials: linear-gradient(135deg, #f7d14e 0%, #ef7734 100%);
$gradient-icon-content-creators: linear-gradient(
  135deg,
  #3be4da 0%,
  #f764c5 100%
);
$gradient-icon-influencers: linear-gradient(135deg, #eb7b4c 0%, #ff8ded 100%);

/* MISC GRADIENTS */
$gradient-green-blue: linear-gradient(to right, #5fd568 0%, #1ee3d7 100%);
$gradient-port-gore: linear-gradient(to left, #2d244a 0%, #20243d 100%);
$gradient-light-blue: linear-gradient(to right, #3ddafe 0%, #00baff 100%);
$gradient-ghost-white: linear-gradient(to top, #f9f9f9, #fff 33%);
$gradient-capri: linear-gradient(-14deg, #00baff 0%, #06c2ff 100%);
$gradient-image-inner-container: linear-gradient(
  180deg,
  rgb(0, 0, 0, 0%) 0,
  rgb(0, 0, 0, 8%) 13%,
  rgb(0, 0, 0, 50%) 80%,
  rgb(0, 0, 0, 50%) 100%
);
$gradient-mobile-background: linear-gradient(to left, #2d244a 0%, #20243d 100%);
$gradient-black-top: linear-gradient(to top, #000 0%, rgb(0, 0, 0, 0%) 100%);
$gradient-agent-image-backdrop: linear-gradient(
  135deg,
  #8281c1 0%,
  #7e5ba8 100%
);
$gradient-agent-image-overlay: linear-gradient(
  135deg,
  #6a69a5 0%,
  #5f01d1 100%
);
$gradient-boost: linear-gradient(-30deg, #0088e7 5%, #20008a 42%, #3d0267 100%);
$gradient-group: linear-gradient(to right, #4a00e0 0%, #8e2de2 100%);
$gradient-warning-1: linear-gradient(0deg, #fff0f0, #fff0f0);
$gradient-warning-2: linear-gradient(
  72.66deg,
  rgb(255, 180, 180, 20%) -10.73%,
  rgb(255, 180, 180) 30.59%
);

/* SHADOWS */
$shadow-button: 0 10px 20px #9ae9fc;
$shadow-box-dropdown:
  0 5px 5px -3px rgb(0, 0, 0, 20%),
  0 8px 10px 1px rgb(0, 0, 0, 14%),
  0 3px 14px 2px rgb(0, 0, 0, 12%);
$shadow-tea-green: 0 10px 20px #beeec6;
$shadow-pastel-pink: 0 10px 20px #e5a5a8;
$shadow-blue-bolt: inset 0 0 0 2px #00baff;
$shadow-beer: inset 0 0 0 2px #ea8916;
$shadow-midnight: inset 0 0 0 2px rgb(#641971, 0.4);
$shadow-tooltip: 0 0.6px 40px rgb(30, 41, 69, 10%);
$shadow-card-container: 0 1px 40px #0000004d;
$shadow-audio-player-progress: -100vw 0 0 100vw #000;
$shadow-btn-edit: 0 4px 20px rgb(57, 65, 82, 10%);
$shadow-profile-image: 0 4px 60px rgb(0, 0, 0, 10%);
$shadow-profile-image-main: 0 4.0772px 61.158px rgb(139, 150, 191, 24%);
$shadow-btn-back: 0 4px 10px rgb(20, 24, 67, 31%);
$shadow-switch-active: inset 0 0 0 2px #5fd568;
$shadow-switch: inset 0 0 0 2px #bbb;
$shadow-mobile-sidebar-menu-item: 0 1px 40px rgb(#000, 0.015);
$shadow-input-box: 0 -5px 30px #0000004d;
$shadow-card-container-light: 0 1px 40px #00000026;
$shadow-card-container-hover: 0 1px 40px rgb(0, 0, 0, 5%);
$shadow-box-dark-wide: 0 1px 40px #00000026;
$shadow-form-control: 0 5px 10px #0000001a;
$shadow-carousel-arrow-hover: 0 5px 10px #00000026;
$shadow-agent-card: 0 14px 40px #b6b6b626;
$shadow-image-inner-container: 0 16px 32px rgb(43, 36, 72, 20%);
$shadow-box-inset-light: inset 0 0 0 3px #eee;
$shadow-casting-call-text: 0 2px 10px #ddd;
$shadow-casting-call-hover: 0 3px 15px #00000026;
$shadow-casting-call: 0 3px 8px #00000026;
$shadow-article-preview: 0 4px 42px 0 #0000000d;
$shadow-article-preview-hover: 0 8px 42px 0 #0000001a;
$shadow-accordion: 0 30px 40px #00000026;
$shadow-post-casting-call-sidemenu: 0 30px 40px rgb($black, 0.15);
$shadow-contacts: 0 10px 10px #0000001a;
$shadow-add-contact: 0 5px 20px #0000001a;

/* SPACES */
$space-5: 5px;
$space-10: 10px;
$space-15: 15px;
$space-20: 20px;
$space-25: 25px;
$space-30: 30px;
$space-35: 35px;
$space-40: 40px;
$space-45: 45px;
$space-50: 50px;
$space-55: 55px;
$space-60: 60px;
$space-65: 65px;
$content-max-width: 1240px !default;

/* Z-INDEX */
$z-index-header: 9;
$z-index-sidebar: 9;
$z-index-footer-menu: 5;
$z-index-message-widget: 9;
$z-index-modal: 9;
$z-index-tooltip: 10;
$z-index-tooltip-overlay: 9;
$z-index-mobile-header: 9;
