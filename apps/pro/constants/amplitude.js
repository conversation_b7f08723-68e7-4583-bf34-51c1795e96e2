export const AMP_ELEMENTS = {
  type: {
    button: 'button',
    toggle: 'toggle',
    tab: 'tab',
    input: 'input',
    select: 'select',
    checkbox: 'checkbox',
    enterKey: 'enter_key',
  },
  scope: {
    global: 'global',
    burger: 'burger',
    modal: 'modal',
    messageCenter: 'message_center',
    submissionsPage: 'submissions_page',
    talentSearchPage: 'talent_search_page',
    editProfile: 'edit_profile',
    profilePage: 'profile_page',
    contactsPage: 'contacts_page',
    castingCallListPage: 'casting_calls_list_page',
    postCastingCallPage: 'post_casting_call_page',
    contactsPopup: 'contacts_popup',
    contactPage: 'contact_page',
    castingCallInfoPage: 'casting_call_info_page',
    settings: 'settings',
  },
  section: {
    modal: 'modal',
    messageForm: 'message_form',
    inviteForm: 'invite_form',
    appliedTab: 'applied_tab',
    shortlistTab: 'shortlist_tab',
    declinedTab: 'declined_tab',
    recommendedTab: 'recommended_tab',
    tabMenu: 'tab_menu',
    talentFinder: 'talent_finder',
    searchForm: 'search_form',
    nameForm: 'name_form',
    companyForm: 'company_form',
    categoriesForm: 'categories_form',
    socialNetworkForm: 'social_network_form',
    aboutForm: 'about_form',
    profileUrlForm: 'profile_url_form',
    photosForm: 'photos_form',
    photo: 'photo',
    contactsPopup: 'contacts_popup',
    sidemenu: 'sidemenu',
    navigation: 'navigation',
    head: 'head',
    header: 'header',
    filter: 'filter',
    interestedIn: 'interested_in',
    additionalPhotos: 'additional_photos',
    personalLink: 'personal_link',
    personalInformation: 'personal_info',
    promotions: 'promotions',
    roleInfo: 'role_info',
    inviteHeader: 'invite_header',
  },
  result: {
    success: 'success',
    fail: 'fail',
  },
};

export const AMP_EVENTS = {
  modalViewed: 'modal_viewed',
  elementClicked: 'element_clicked',
  formStarted: 'form_started',
  formSubmitted: 'form_submitted',
  invalidEventOccurred: 'invalid_event_occurred',
  topSliderBannerClicked: 'top_slider_banner_clicked',
  elementHovered: 'element_hovered',
  deactivateAccount: 'Deactivate Account',
  submitPhoneNumber: 'Submit Phone Number',
  signUp: 'Sign Up',
  viewCastingCallList: 'View Casting Call List',
  viewCastingCall: 'View Casting Call',
  viewCastingCallCreate: 'View Casting Call Create',
  viewCastingCallEdit: 'View Casting Call Edit',
  viewTalentList: 'View Talent List',
  viewAbout: 'View About',
  viewContactUs: 'View Contact Us',
  viewTalent: 'View Talent',
  viewProfile: 'View Profile',
  viewReviews: 'View Reviews',
  viewSettings: 'View Settings',
  viewSubmissions: 'View Submissions',
  viewRegister: 'View Register',
  view500: 'View 500',
  view404: 'View 404',
  viewError: 'View Error',
  viewMessages: 'View Messages',
  viewContacts: 'View Contacts',
  viewContactGroup: 'View Contact Group',
  viewMissingInfo: 'View Missing Info',
  viewPrivacyPolicy: 'View Privacy Policy',
  viewTermsOfUse: 'View Terms of Use',
  viewDirectorWelcome: 'View Director Welcome',
  viewBoost: 'View Boost',
};
