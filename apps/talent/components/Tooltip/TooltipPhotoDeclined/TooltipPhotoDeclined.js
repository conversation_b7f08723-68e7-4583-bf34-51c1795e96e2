'use client';
import React, { memo, useCallback } from 'react';
import styles from './TooltipPhotoDeclined.module.scss';
import { declineDescriptionList } from '@constants/images';
import { Button } from '@components';

const TooltipPhotoDeclined = ({ declineReason, label, onClick }) => {
  const getDeclineDescription = useCallback(
    () => declineDescriptionList[declineReason],
    [declineReason],
  );

  return (
    <div className={styles['declined-image-tooltip']}>
      <div className={styles['tooltip-title']}>
        <img
          className={styles['icon-blocked']}
          src="/assets/icons/icon-blocked.svg"
          alt="icon"
        />
        {declineReason}
      </div>
      <p className={styles['tooltip-description']}>{getDeclineDescription()}</p>
      <div className={styles['button-container']}>
        <Button
          type="button"
          kind="primary"
          color="solid-blue"
          label={label}
          minWidth={'80px'}
          shadow={false}
          className={styles['action-button']}
          onClick={onClick}
        />
      </div>
    </div>
  );
};

export default memo(TooltipPhotoDeclined);
