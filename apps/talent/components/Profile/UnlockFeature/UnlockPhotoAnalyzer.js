'use client';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Viewed,
  <PERSON><PERSON><PERSON>,
  TooltipPhotoPremium,
} from '@components';
import styles from '../../Image/ImageWrapper/ImageWrapper.module.scss';
import { Amp } from '@services/amp';
import React from 'react';
import { useRouter } from 'next/navigation';
import useTrackElementActions from '@utils/useTrackElementActions';

const UnlockPhotoAnalyzer = ({ genderTitle }) => {
  const router = useRouter();

  const { onTrackClick } = useTrackElementActions({
    name: 'Unlock photo analyzer block',
    context: Amp.element.context.ctaBlock,
    autoTrackEnabled: false,
  });

  const onClick = () => {
    onTrackClick();

    router.push('/upgrade');
  };

  return (
    <>
      <Tooltip
        content={<TooltipPhotoPremium genderTitle={genderTitle} />}
        clickable
        openOnHover
      >
        <div className={styles['locked-feature-container']}>
          <div className={styles['icon-locked-container']}>
            <img
              className={styles['icon-locked']}
              src="/assets/icons/icon-locked-feature.svg"
              alt="icon"
            />
          </div>
          <div className={styles['locked-feature-description-container']}>
            <span className={styles['locked-feature-title']}>
              My photo score
            </span>
            <p className={styles['locked-feature-description']}>
              Get cast more often with top-notch photos.
            </p>
          </div>
          <ElementViewed
            name="Unlock photo analyzer block"
            type={Amp.element.type.block}
            context={Amp.element.context.ctaBlock}
          >
            <Button
              className={styles['button-unblock']}
              label="Unlock"
              shadow={false}
              color="solid-blue"
              minWidth="62px"
              onClick={onClick}
            />
          </ElementViewed>
        </div>
      </Tooltip>
    </>
  );
};

export default UnlockPhotoAnalyzer;
