'use client';
import styles from '../ProfileInfo.module.scss';
import cn from 'classnames';
import {
  Button,
  ElementViewed,
  Modal,
  Tooltip,
  TooltipAdvancedAttributes,
} from '@components';
import { Amp } from '@services/amp';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import useTrackElementActions from '../../../utils/useTrackElementActions';

const UnlockSkills = ({ genderTitle }) => {
  const [showAdvancedSkillsPremiumModal, setShowAdvancedSkillsPremiumModal] =
    useState(false);
  const router = useRouter();

  const { onTrackClick } = useTrackElementActions({
    name: 'Unlock advanced skills block',
    context: Amp.element.context.ctaBlock,
    autoTrackEnabled: false,
  });

  const toggleAdvancedSkillsPremiumModal = () => {
    setShowAdvancedSkillsPremiumModal(!showAdvancedSkillsPremiumModal);
  };

  const onClick = () => {
    onTrackClick();

    router.push('/upgrade');
  };

  return (
    <>
      <section>
        <div className={styles['profile-section-title']}>ADVANCED SKILLS:</div>
        <div className={styles['locked-feature-container']}>
          <div
            className={cn(styles['icon-locked-container'], styles['desktop'])}
          >
            <Tooltip
              content={<TooltipAdvancedAttributes genderTitle={genderTitle} />}
              clickable
              openOnHover
              positions={['top', 'right', 'bottom', 'left']}
            >
              <img
                className={styles['icon-locked']}
                src="/assets/icons/icon-locked-feature.svg"
                alt="icon"
              />
            </Tooltip>
          </div>
          <div
            className={cn(styles['icon-locked-container'], styles['mobile'])}
            onClick={toggleAdvancedSkillsPremiumModal}
          >
            <img
              className={styles['icon-locked']}
              src="/assets/icons/icon-locked-feature.svg"
              alt="icon"
            />
          </div>
          <div className={styles['locked-feature-description-container']}>
            <span className={styles['locked-feature-title']}>
              Advanced Skills Feature
            </span>
            <p className={styles['locked-feature-description']}>
              Unlock this feature and stand out with a comprehensive profile.
            </p>
          </div>
          <ElementViewed
            name="Unlock advanced skills block"
            type={Amp.element.type.block}
            context={Amp.element.context.ctaBlock}
          >
            <Button
              className={styles['button-unblock']}
              label="Unlock Feature"
              shadow={false}
              color="solid-blue"
              minWidth="105px"
              onClick={onClick}
            />
          </ElementViewed>
        </div>
      </section>
      {showAdvancedSkillsPremiumModal && (
        <Modal
          onClose={toggleAdvancedSkillsPremiumModal}
          backdropClose
          classNameOverlay={styles['modal-overlay']}
          classNameContainer={styles['modal-container']}
          showDefaultLayout={false}
          disableBackgroundScroll
        >
          <TooltipAdvancedAttributes genderTitle={genderTitle} />
        </Modal>
      )}
    </>
  );
};

export default UnlockSkills;
