'use client';
import React, { useEffect } from 'react';
import styles from '@styles/wizard.module.scss';
import cn from 'classnames';
import { ProfileProvider } from '@contexts/ProfileContext';
import WizardForm from '@components/WizardForm/WizardForm';
import { Amp } from '@services/amp';
import IconCheckmark from '../../../public/assets/icons/icon-checkmark.svg';
import IconEdit from '../../../public/assets/icons/icon-edit.svg';
import IconPhoto from '../../../public/assets/icons/icon-photo-wizard.svg';
import WizardPhotoUpload from '@components/WizardPhotoUpload/WizardPhotoUpload';

const FIRST_STEP = 1;
const SECOND_STEP = 2;

const WizardClient = ({
  step,
  genderOptions,
  ethnicitiesOptions,
  profile,
  isEmailValid,
  isPasswordSet,
  redirectPath,
}) => {
  useEffect(() => {
    switch (step) {
      case FIRST_STEP:
        Amp.track(Amp.events.viewWizardStepOne);
        break;
      case SECOND_STEP:
        Amp.track(Amp.events.viewWizardStepTwo);
        break;
    }
  }, [step]);

  return (
    <main className={styles.main}>
      <article className={styles.container}>
        <div className={styles.steps}>
          <div
            className={cn(styles.step, {
              [styles.active]: step === FIRST_STEP,
            })}
          >
            <span className={styles.icon}>
              {step > FIRST_STEP && <IconCheckmark />}
              {step == FIRST_STEP && <IconEdit width={15} />}
            </span>{' '}
            Step 1
          </div>
          <div
            className={cn(styles.step, styles.second, {
              [styles.active]: step === SECOND_STEP,
            })}
          >
            <span className={styles.icon}>
              <IconPhoto />
            </span>{' '}
            Step 2
          </div>
        </div>
        {step === FIRST_STEP && (
          <WizardForm
            profile={profile}
            isEmailValid={isEmailValid}
            genderOptions={genderOptions}
            ethnicitiesOptions={ethnicitiesOptions}
            isPasswordSet={isPasswordSet}
          />
        )}
        {step === SECOND_STEP && (
          <ProfileProvider value={profile}>
            <WizardPhotoUpload
              gender={profile.gender.title}
              redirectPath={redirectPath}
            />
          </ProfileProvider>
        )}
      </article>
    </main>
  );
};

export default WizardClient;
