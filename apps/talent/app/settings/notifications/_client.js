'use client';
import React, { useEffect } from 'react';
import styles from '@styles/notifications.module.scss';
import { MainLayout, NotificationsForm, Settings } from '@components';
import { Amp } from '@services/amp';

const NotificationsClient = ({ allowEmailNotifications, identifier }) => {
  useEffect(() => {
    Amp.track(Amp.events.viewSettings, {
      name: 'notifications',
    });
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isSaleHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
    >
      <Settings title="Email Notifications">
        <div className={styles['notifications-form-container']}>
          <h1 className={styles['notifications-form-title']}>
            Email Notifications
          </h1>
          <NotificationsForm
            identifier={identifier}
            allowEmailNotifications={allowEmailNotifications}
          />
        </div>
      </Settings>
    </MainLayout>
  );
};

export default NotificationsClient;
