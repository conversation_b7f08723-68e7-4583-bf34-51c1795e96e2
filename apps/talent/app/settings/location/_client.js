'use client';
import React, { useEffect, useRef, useState } from 'react';
import styles from '@styles/location.module.scss';
import { LocationForm, MainLayout, Settings } from '@components';
import { Amp } from '@services/amp';

const LocationClient = ({ zip, location }) => {
  const [saveButtonDisabled, setSaveButtonDisabled] = useState(false);
  const contentRef = useRef(null);

  const toggleSaveButtonDisabled = (disabled) => {
    setSaveButtonDisabled(disabled);
  };

  const triggerSubmit = async () => {
    await contentRef.current?.triggerSubmit();
  };

  useEffect(() => {
    Amp.track(Amp.events.viewSettings, {
      name: 'location',
    });
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isSaleHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
    >
      <Settings
        title="Location"
        buttonDisabled={saveButtonDisabled}
        onClick={triggerSubmit}
        showButton
      >
        <div className={styles['change-location-form-container']}>
          <h1 className={styles['change-location-form-title']}>Location</h1>
          <div className={styles['change-location-form-description']}>
            By default, available casting calls will be filtered based around
            your location.
          </div>
          <LocationForm
            zip={zip}
            initialLocation={location}
            ref={contentRef}
            toggleSaveButtonDisabled={toggleSaveButtonDisabled}
          />
        </div>
      </Settings>
    </MainLayout>
  );
};

export default LocationClient;
