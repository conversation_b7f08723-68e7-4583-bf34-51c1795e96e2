'use client';
import React, { useEffect, useRef, useState } from 'react';
import { ChangePasswordForm, MainLayout, Settings } from '@components';
import { Amp } from '@services/amp';

const ChangePasswordClient = ({
  isPasswordSet,
  accountId,
  showSidebar = false,
}) => {
  const [saveButtonDisabled, setSaveButtonDisabled] = useState(false);
  const contentRef = useRef(null);

  const toggleSaveButtonDisabled = (disabled) => {
    setSaveButtonDisabled(disabled);
  };

  const triggerSubmit = async () => {
    await contentRef.current?.triggerSubmit();
  };

  useEffect(() => {
    Amp.track(Amp.events.viewSettings, {
      name: 'change password',
    });
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isSaleHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
    >
      <Settings
        title="Password"
        buttonDisabled={saveButtonDisabled}
        onClick={triggerSubmit}
        showSidebar={showSidebar}
        showButton
      >
        <ChangePasswordForm
          ref={contentRef}
          toggleSaveButtonDisabled={toggleSaveButtonDisabled}
          isPasswordSet={isPasswordSet}
          accountId={accountId}
        />
      </Settings>
    </MainLayout>
  );
};

export default ChangePasswordClient;
