'use client';
import React, { useEffect, useRef, useState } from 'react';
import styles from '@styles/contacts.module.scss';
import { ContactsForm, MainLayout, Settings } from '@components';
import { Amp } from '@services/amp';

const ContactsClient = ({ email, phone, allowNotifications }) => {
  const [saveButtonDisabled, setSaveButtonDisabled] = useState(false);
  const contentRef = useRef(null);

  const toggleSaveButtonDisabled = (disabled) => {
    setSaveButtonDisabled(disabled);
  };

  const triggerSubmit = async () => {
    await contentRef.current?.triggerSubmit();
  };

  useEffect(() => {
    Amp.track(Amp.events.viewSettings, {
      name: 'contacts',
    });
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isSaleHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
    >
      <Settings
        title="Contact Information"
        buttonDisabled={saveButtonDisabled}
        onClick={triggerSubmit}
        showButton
      >
        <div className={styles['contacts-form-container']}>
          <h1 className={styles['contacts-form-title']}>Contact Information</h1>
          <div className={styles['contacts-form-description']}>
            This will only be visible to you and in your Comp Card.
          </div>
          <ContactsForm
            ref={contentRef}
            toggleSaveButtonDisabled={toggleSaveButtonDisabled}
            email={email}
            phone={phone}
            allowNotifications={allowNotifications}
          />
        </div>
      </Settings>
    </MainLayout>
  );
};

export default ContactsClient;
