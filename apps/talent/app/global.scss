@use '@styles/mixins' as *;
@use '@styles/variables' as *;
@import '@styles/typography';

* {
  box-sizing: border-box;
}

html {
  background: $white;
  text-size-adjust: 100%;
  tab-size: 4;
}

body {
  margin: unset;
}

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
}

a {
  color: inherit;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

p {
  margin: 0;
}

fieldset,
legend {
  padding: 0;
  margin: 0;
  border: none;
}

#onetrust-consent-sdk {
  display: none;
}

#chat-widget-container {
  /* stylelint-disable */
  z-index: 90 !important;
  /* stylelint-enable */
}

.global-error {
  background-image: $gradient-port-gore;
  display: flex;
  flex-flow: column nowrap;
  position: relative;
  z-index: 1;
  justify-content: stretch;
  flex: 1 1 auto;
  overflow: hidden;
  padding: 0 $space-10 $space-40;

  @include xsmall {
    padding: 0 28px $space-40;
  }

  .body {
    position: relative;
    max-width: 1240px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    display: grid;
  }

  .cloud-bg {
    position: absolute;
    inset: 0;

    &::before {
      content: '';
      position: absolute;
      top: -50px;
      left: -29%;
      width: 348px;
      height: 348px;
      background: url('#{$assetUrl}/assets/500/ellipse-top-left.svg') 0 0
        no-repeat;
      background-size: contain;
    }

    &::after {
      content: '';
      position: absolute;
      top: 105px;
      right: -27%;
      left: auto;
      width: 348px;
      height: 348px;
      background: url('#{$assetUrl}/assets/500/ellipse-top-right.svg') 0 0
        no-repeat;
      background-size: contain;
    }

    @include desktop {
      &::before {
        top: -400px;
        left: 34%;
        margin-left: -503px;
        width: 753px;
        height: 753px;
      }

      &::after {
        top: -220px;
        right: 34%;
        margin-right: -395px;
        width: 753px;
        height: 753px;
      }
    }
  }

  .cloud-bg-center {
    position: absolute;
    top: 72px;
    left: 50%;
    margin-left: -156px;
    width: 348px;
    height: 348px;
    background: url('#{$assetUrl}/assets/500/ellipse-top-center.svg') 0 0
      no-repeat;
    background-size: contain;

    @include desktop {
      top: -258px;
      left: 50%;
      margin-left: -395px;
      width: 753px;
      height: 753px;
    }
  }

  .main-block {
    position: relative;
    display: flex;
    flex-flow: column nowrap;
    align-items: center;
    padding: 48px 0 17px;

    @include desktop {
      align-items: flex-start;
      margin-left: 70px;
      padding: 148px 0 $space-65;
    }
  }

  .logo-wrap {
    display: inline-block;

    @include desktop {
      margin-bottom: 34px;
    }
  }

  .logo {
    width: 94px;
    height: 35px;

    @include desktop {
      width: 160px;
      height: 58px;
    }
  }

  .title {
    position: relative;
    max-width: 391px;
    color: $white;
    font-weight: 400;
    font-size: 16px;
    line-height: 1.6;
    margin: 0 11px;
    z-index: 1;

    @include desktop {
      margin: 0;
    }

    p {
      margin: 0 0 17px;
    }
  }

  .image-500 {
    width: 260px;
    height: 130px;
    position: relative;
    margin: 0 auto 3px;
    background: url('#{$assetUrl}/assets/500/image-500.svg') 50% 50% no-repeat;
    background-size: contain;
    z-index: 1;

    @include xsmall {
      width: 320px;
      height: 160px;
    }

    @include desktop {
      position: absolute;
      left: auto;
      top: 120px;
      right: 6%;
      width: 620px;
      height: 320px;
      margin: 0;
    }

    @include desktop {
      margin-right: 0;
    }
  }

  .info-section {
    position: relative;
    background-color: $violet-80;
    border-radius: 10px;
    display: grid;
    grid-gap: 46px;
    padding: $space-15;

    @include xsmall {
      padding: 32px 33px;
    }

    @include tablet {
      margin: 0 0 130px;
      grid-template-columns: 1fr 1fr;
      padding: 37px 71px 44px;
    }

    @include desktop {
      grid-gap: 32px;
    }
  }

  .info-box {
    position: relative;
    display: flex;
    flex-flow: column nowrap;

    &:nth-child(2)::before {
      content: '';
      position: absolute;
      top: -25px;
      left: 0;
      right: 0;
      height: 1px;
      background-color: white;
    }

    @include tablet {
      grid-template-columns: 1fr 1fr;

      &:nth-child(2)::before {
        top: 0;
        right: auto;
        left: -17px;
        width: 1px;
        height: 100%;
      }
    }
  }

  .info-title {
    font-size: 18px;
    font-weight: 700;
    color: $white;
    margin-bottom: 6px;

    @include tablet {
      margin-bottom: 8px;
    }
  }

  .info-text {
    font-size: 16px;
    line-height: 1.4;
    font-weight: 400;
    color: $white;

    p {
      margin: 0;
    }

    a {
      color: $blue-100;
    }
  }
}
