'use client';
import React, { useEffect, useState } from 'react';
import styles from '@styles/upgrade.module.scss';
import Image from 'next/image';
import Link from 'next/link';
import cn from 'classnames';
import {
  Button,
  Countdown,
  MainLayout,
  Promo,
  SubscriptionPlanDescription,
  SubscriptionPlans,
} from '@components';
import { useSale } from '@contexts/SaleContext';
import { getPromotedPlan } from '@utils/planSelectHelper';
import { Amp } from '@services/amp';
import { profileFeatures } from '@constants/payment';
import Testimonials from '@components/Testimonials/Testimonials';

const UpgradeClient = ({
  initialPlans,
  promos,
  referer,
  isPreview,
  initialDiscount,
  hidePlanSelectPromotions = false,
}) => {
  const [settings, setSettings] = useState({
    plans: initialPlans,
    discount: initialDiscount,
  });

  const { plans, discount } = settings;

  const { saleExpirationTime, showSale, stopSale } = useSale();

  const {
    is_daily_price,
    is_weekly_price,
    plan_select_design,
    id: discount_id,
    ab_experiment,
  } = discount;

  const displayPeriod = is_daily_price
    ? 'day'
    : `${is_weekly_price ? 'week' : 'month'}`;

  const {
    promoted_period,
    text_color,
    background_image,
    background_mobile_image,
    background_tablet_image,
    background_color,
  } = plan_select_design || {};

  const initialSelectedMemberLevelId = getPromotedPlan(
    plans,
    promoted_period,
  )?.member_level_id;

  const designStyles =
    (plan_select_design && {
      '--text-color': text_color,
      '--background': `url(${background_image})`,
      '--background-mobile': `url(${background_mobile_image})`,
      '--background-tablet': `url(${background_tablet_image})`,
      '--background-color': background_color,
    }) ||
    {};

  useEffect(() => {
    if (!isPreview) {
      Amp.track(Amp.events.viewPlanSelect, {
        discount_id: discount_id,
        ab_experiment: ab_experiment,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const scrollToTop = (name, section) => {
    onButtonClick(name, section);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const onButtonClick = (name, section) => {
    if (!isPreview) {
      Amp.track(Amp.events.elementClicked, {
        name,
        scope: Amp.element.scope.upgrade,
        section,
        type: Amp.element.type.button,
      });
    }
  };

  const onButtonHover = (name, section) => {
    if (!isPreview) {
      Amp.track(Amp.events.elementHovered, {
        name,
        scope: Amp.element.scope.upgrade,
        section,
        type: Amp.element.type.button,
      });
    }
  };

  const updateSettings = (value) => {
    setSettings(value);
  };

  const resetSettings = () => {
    setSettings({
      plans: initialPlans,
      discount: initialDiscount,
    });
  };

  const promotionsParts = (
    <>
      {profileFeatures && (
        <SubscriptionPlanDescription features={profileFeatures} />
      )}
      <div className={styles['featured-profile-container']}>
        <div className={styles['featured-profile-card']}>
          <div className={styles['featured-profile-image-container']}>
            <Image
              className={styles['featured-profile-image']}
              src={'/assets/upgrade/featured-profile-image.jpg'}
              alt="profile image"
              width={76}
              height={76}
            />
          </div>
          <div>
            <div className={styles['featured-profile-name']}>Tyler Rivera</div>
            <div className={styles['featured-profile-location']}>
              San Francisco, CA
            </div>
          </div>
          <div className={styles['featured-profile-description']}>
            He has started his{' '}
            <Link
              passHref
              className={styles.link}
              href={`${process.env.publicUrl}/blog/success-stories/tyler-rivera-success-story`}
              onMouseEnter={() =>
                onButtonHover(
                  'success story',
                  Amp.element.section.featuredProfile,
                )
              }
              onClick={() =>
                onButtonClick(
                  'success story',
                  Amp.element.section.featuredProfile,
                )
              }
            >
              success story
            </Link>
            . So have hundreds of other performers.
          </div>
          <div>
            <Button
              label="It's your turn now"
              color="green-gradient"
              minWidth="220px"
              onClick={() =>
                scrollToTop(
                  "It's your turn now",
                  Amp.element.section.featuredProfile,
                )
              }
              onMouseEnter={() =>
                onButtonHover(
                  "It's your turn now",
                  Amp.element.section.featuredProfile,
                )
              }
            />
          </div>
        </div>
      </div>
      <div className={styles['site-description-container']}>
        <div className={styles['site-description-inner-container']}>
          <div className={styles['site-description']}>
            <h1 className={styles['site-description-title']}>
              About allcasting
            </h1>
            <p>
              We&apos;re here to connect you to 1,000s of acting, modeling &
              other projects nationwide. With everything from beginner roles for
              movie extras to professional modeling photoshoots, allcasting is
              the best place to start and continue your journey into the world
              of entertainment.
            </p>
            <p>
              Get booked for movies/photoshoots, connect with casting
              professionals, stay up to date with the industry, read & watch
              lessons for performers, be part of a 500,000 people community, and
              so much more - all with allcasting.
            </p>
          </div>
        </div>
      </div>
      <div className={styles['promotions-container']}>
        <Promo promos={promos} />
      </div>
      <div className={styles['upgrade-section-footer']}>
        <div className={styles['upgrade-section-footer-text-container']}>
          <span className={styles['upgrade-section-footer-text-success']}>
            Success is just one step away
          </span>
          <span className={styles['upgrade-section-footer-text']}>
            Join today and find the gig you&apos;ve been dreaming of
          </span>
        </div>
        <Button
          label="SUBSCRIBE NOW"
          color="green-gradient"
          minWidth="220px"
          onClick={() =>
            scrollToTop('Subscribe now', Amp.element.section.footer)
          }
          onMouseEnter={() =>
            onButtonHover('Subscribe now', Amp.element.section.footer)
          }
          shadow={false}
        />
      </div>
    </>
  );

  return (
    <MainLayout
      referer={referer}
      isSaleHeaderVisible={!isPreview}
      isMobileSaleHeaderHidden
      isPlanSelectHeaderVisible={!isPreview}
    >
      <section
        className={cn(styles['upgrade-section'], {
          [styles['hide-plan-select-promotions']]: hidePlanSelectPromotions,
        })}
      >
        <div className={styles['upgrade-header']} style={designStyles}>
          <h1 className={styles['upgrade-header-title']}>
            <span>
              Select a Plan To Apply for{' '}
              <span style={{ whiteSpace: 'nowrap' }}>Casting Calls</span>
            </span>
          </h1>
          <span className={styles['upgrade-header-description']}>
            Trusted by users and top brands.
          </span>
          <Testimonials />
          {showSale && (
            <div className={styles['upgrade-header-sale-countdown']}>
              <Countdown
                expirationTime={saleExpirationTime}
                onCountdownEnd={stopSale}
              />
            </div>
          )}
        </div>
        <SubscriptionPlans
          amplitudeScope={Amp.element.scope.upgrade}
          plans={plans}
          showSale={showSale}
          styleTheme="light"
          displayPeriod={displayPeriod}
          initialSelectedMemberLevelId={initialSelectedMemberLevelId}
          design={plan_select_design}
          isPreview={isPreview}
          updateSettings={updateSettings}
          resetSettings={resetSettings}
        />
        {!hidePlanSelectPromotions && promotionsParts}
      </section>
    </MainLayout>
  );
};

export default UpgradeClient;
