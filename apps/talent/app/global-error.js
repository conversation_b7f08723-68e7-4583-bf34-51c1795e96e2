'use client';

import * as Sentry from '@sentry/nextjs';
import React, { useEffect } from 'react';
import Link from 'next/link';
import Logo from '../public/assets/500/logo.svg';
import { Amp } from '@services/amp';

// Error boundaries must be client components.
// Application crash due to a fatal error in the root layout or template files.
// If we are here, move to Mexico and sell bananas.
// Can only use global styles.
// Link to the home page or force a page reload with window.location.reload().
// Must include html and body tags.

export default function GlobalError({ error }) {
  useEffect(() => {
    Sentry.captureException(error);
  }, [error]);

  const reset = () => {
    window.location.reload();
  };

  useEffect(() => {
    Amp.track(Amp.events.view500);
  }, []);

  return (
    <html lang="en">
      <body>
        <div className="global-error">
          <div className="cloud-bg">
            <div className="cloud-bg-center"></div>
          </div>
          <div className="body">
            <div className="main-block">
              <Link href="/" className="logo-wrap" passHref>
                <Logo className="logo" />
              </Link>
              <div className="image-500"></div>
              <h1 className="title">
                <p>
                  We apologize for the inconvenience, but an unexpected error
                  has occurred on our server. Our technical team has been
                  notified and is working to resolve the issue as quickly as
                  possible.
                </p>
                <p>
                  Please try again later or contact our support team for further
                  assistance.
                </p>
                <button onClick={reset}>Try again</button>
              </h1>
            </div>
            <div className="info-section">
              <div className="info-box">
                <div className="info-title">Talent Support</div>
                <div className="info-text">
                  <p>
                    <Link href="tel:18006058590">****************</Link>
                  </p>
                  <p>Monday-Friday 8am-5pm PST</p>
                  <p>
                    <Link href="mailto:<EMAIL>">
                      <EMAIL>
                    </Link>
                  </p>
                </div>
              </div>
              <div className="info-box">
                <div className="info-title">Casting Director Support</div>
                <div className="info-text">
                  <p>
                    <Link href="tel:18009135480">****************</Link>
                  </p>
                  <p>Monday-Friday 8am-5pm PST</p>
                  <p>
                    <Link href="mailto:<EMAIL>">
                      <EMAIL>
                    </Link>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
