'use client';

import { useEffect } from 'react';
import * as Sentry from '@sentry/nextjs';
import { ErrorLayout } from '@components';
import { Amp } from '@services/amp';

// Error boundaries must be client components.
export default function ErrorPage({ error, reset }) {
  useEffect(() => {
    Sentry.captureException(error);
  }, [error]);

  useEffect(() => {
    Amp.track(Amp.events.viewError);
  }, []);

  return <ErrorLayout reset={reset} />;
}
