'use client';
import { useEffect } from 'react';
import styles from '@styles/checkout.module.scss';
import { useSale } from '@contexts/SaleContext';
import { useAuth } from '@contexts/AuthContext';
import { Countdown, MainLayout, PaymentForm } from '@components';
import { Amp } from '@services/amp';
import Link from 'next/link';
import Logo from '../../public/assets/logo/logo-4.svg';
import { isMobile } from '@utils/isMobile';

const Checkout12Client = ({
  pricePlan,
  stripePK,
  billing,
  monthOptions,
  yearOptions,
  email,
  campaign,
  campaignGroup,
  tracking,
  discountId,
  discountAbExperiment,
}) => {
  const { saleExpirationTime, showSale, stopSale } = useSale();
  const {
    accountId,
    userProfiles,
    accountLevelVerify,
    getIsProfileOutsideUSA,
  } = useAuth();
  const isProfileOutsideUSA = getIsProfileOutsideUSA();

  useEffect(() => {
    if (accountId) {
      Amp.track(Amp.events.viewCheckout, {
        discount_id: discountId,
        ab_experiment: discountAbExperiment,
        sale_interval: pricePlan
          ? `${pricePlan.period} ${pricePlan.period_length}`
          : null,
      });
    }
  });

  return (
    <MainLayout isDefaultHeaderVisible isUserMenuVisible>
      <section className={styles['checkout-section']}>
        <div className={styles['checkout-section-mobile-header']}>
          <Link
            href={`${process.env.publicUrl}/castingcalls`}
            passHref
            aria-label="allcasting"
            className={styles['logo-link']}
          >
            <Logo className={styles.logo} />
          </Link>
        </div>
        <div className={styles['checkout-section-header']}>
          <h1 className={styles['checkout-section-header-title']}>
            Complete Payment Info
          </h1>
          <span className={styles['checkout-section-header-description']}>
            Instant Access | Proven Value | Real Results
          </span>
          {showSale && (
            <Countdown
              expirationTime={saleExpirationTime}
              onCountdownEnd={stopSale}
            />
          )}
        </div>
        <div className={styles['payment-form-container']}>
          {pricePlan && (
            <PaymentForm
              period={pricePlan.period}
              price={pricePlan.price}
              baseMonthPrice={pricePlan.base_month_price}
              nameOnCard={billing.billingName}
              zip={billing.zip}
              expMonth={billing.exp_month}
              expYear={billing.exp_year}
              paymentMethods={pricePlan.methods}
              memberLevelId={pricePlan.member_level_id}
              accountId={accountId}
              monthOptions={monthOptions}
              yearOptions={yearOptions}
              clientId={userProfiles[0]?.clientId}
              email={email}
              campaign={campaign}
              campaignGroup={campaignGroup}
              tracking={tracking}
              stripePK={stripePK}
              accountLevelVerify={accountLevelVerify}
              isMobile={isMobile}
              isUpgradeForm
              isProfileOutsideUSA={isProfileOutsideUSA}
            />
          )}
        </div>
      </section>
    </MainLayout>
  );
};

export default Checkout12Client;
