# ---- Base Stage ----
ARG IMAGES_PROXY=docker.io
FROM ${IMAGES_PROXY}/node:20.12-alpine AS base

RUN apk add --no-cache curl
WORKDIR /app
RUN addgroup -g 1001 app && adduser -D -G app -u 1001 app
RUN chown -R app:app /app
COPY --chown=app:app package*.json .npmrc ./
USER app

# ---- Dependencies for runtime ----
FROM base AS dependencies
RUN npm ci --omit=dev

# ---- devdependencies and full source ----
FROM base AS devdependencies-source
RUN npm ci
COPY --chown=app:app . .

# ---- Build for production ----
FROM devdependencies-source AS build-prod
ENV NODE_ENV=production
ARG SENTRY_AUTH_TOKEN
ARG HEALTH_CHECK_TOKEN
ARG NEXT_PUBLIC_VERSION
ENV NEXT_PUBLIC_VERSION=$NEXT_PUBLIC_VERSION
RUN \
echo "Setting dynamic environment variables..." && \
sed -i "s|^SENTRY_AUTH_TOKEN=.*|SENTRY_AUTH_TOKEN=${SENTRY_AUTH_TOKEN}|" .env.production && \
sed -i "s|^HEALTH_CHECK_TOKEN=.*|HEALTH_CHECK_TOKEN=${HEALTH_CHECK_TOKEN}|" .env.production

RUN npm run build

# ---- Build for staging ----
FROM devdependencies-source AS build-staging
ENV NODE_ENV=test
ARG HEALTH_CHECK_TOKEN
ARG NEXT_PUBLIC_VERSION
ENV NEXT_PUBLIC_VERSION=$NEXT_PUBLIC_VERSION
RUN \
echo "Setting dynamic environment variables..." && \
sed -i "s|^HEALTH_CHECK_TOKEN=.*|HEALTH_CHECK_TOKEN=${HEALTH_CHECK_TOKEN}|" .env.test

RUN npm run build


# ---- Build for review env ----
FROM devdependencies-source AS build-review
ARG BRANCH_NAME
ARG SENTRY_AUTH_TOKEN
ARG HEALTH_CHECK_TOKEN
ENV NODE_ENV=test
ARG NEXT_PUBLIC_VERSION
ENV NEXT_PUBLIC_VERSION=$NEXT_PUBLIC_VERSION
RUN \
echo "Setting dynamic environment variables..." && \
BRANCH_NAME_LOWER=$(echo "$BRANCH_NAME" | tr '[:upper:]' '[:lower:]') && \
PUBLIC_URL="https://${BRANCH_NAME_LOWER}.ac.stg.entertech.art" && \
COOKIE_DOMAIN=".${BRANCH_NAME_LOWER}.ac.stg.entertech.art" && \
BASE_URL="https://talent.${BRANCH_NAME_LOWER}.ac.stg.entertech.art" && \
sed -i "s|^PUBLIC_URL=.*|PUBLIC_URL=${PUBLIC_URL}|" .env.test && \
sed -i "s|^COOKIE_DOMAIN=.*|COOKIE_DOMAIN=${COOKIE_DOMAIN}|" .env.test && \
sed -i "s|^BASE_URL=.*|BASE_URL=${BASE_URL}|" .env.test && \
sed -i "s|^HEALTH_CHECK_TOKEN=.*|HEALTH_CHECK_TOKEN=${HEALTH_CHECK_TOKEN}|" .env.test

RUN npm run build

# ---- Runtime for production ----
FROM base AS runtime-prod
WORKDIR /app

COPY --from=dependencies --chown=app:app /app/node_modules ./node_modules
COPY --from=build-prod --chown=app:app /app/.next ./.next
COPY --from=build-prod --chown=app:app /app/public ./public
COPY --from=build-prod --chown=app:app /app/loaders ./loaders
COPY --from=build-prod --chown=app:app /app/next.config.js ./next.config.js
COPY --from=build-prod --chown=app:app /app/.env.production ./.env.production
COPY --from=build-prod --chown=app:app /app/deployment/upload-to-cdn.js ./upload-to-cdn.js
ENV NODE_ENV=production
ARG NEXT_PUBLIC_VERSION
ENV NEXT_PUBLIC_VERSION=$NEXT_PUBLIC_VERSION
USER app

# ---- Runtime for staging ----
FROM base AS runtime-staging
WORKDIR /app
COPY --from=dependencies --chown=app:app /app/node_modules ./node_modules
COPY --from=build-staging --chown=app:app /app/.next ./.next
COPY --from=build-staging --chown=app:app /app/public ./public
COPY --from=build-staging --chown=app:app /app/loaders ./loaders
COPY --from=build-staging --chown=app:app /app/next.config.js ./next.config.js
COPY --from=build-staging --chown=app:app /app/.env.test ./.env.test
COPY --from=build-staging --chown=app:app /app/deployment/upload-to-cdn.js ./upload-to-cdn.js
ENV NODE_ENV=test
ARG NEXT_PUBLIC_VERSION
ENV NEXT_PUBLIC_VERSION=$NEXT_PUBLIC_VERSION
USER app

# ---- Runtime for review ----
FROM base AS runtime-review
WORKDIR /app
COPY --from=dependencies --chown=app:app /app/node_modules ./node_modules
COPY --from=build-review --chown=app:app /app/.next ./.next
COPY --from=build-review --chown=app:app /app/public ./public
COPY --from=build-review --chown=app:app /app/loaders ./loaders
COPY --from=build-review --chown=app:app /app/next.config.js ./next.config.js
COPY --from=build-review --chown=app:app /app/.env.test ./.env.test
COPY --from=build-review --chown=app:app /app/deployment/upload-to-cdn.js ./upload-to-cdn.js
ENV NODE_ENV=test
ARG NEXT_PUBLIC_VERSION
ENV NEXT_PUBLIC_VERSION=$NEXT_PUBLIC_VERSION
USER app

# ---- Build-local stage (for local dev with custom CA) ----
FROM devdependencies-source AS build-local
ENV NEXT_PUBLIC_VERSION=dev
USER root
RUN apk add --no-cache ca-certificates

COPY ./docker/node/rootCA.pem /usr/local/share/ca-certificates/rootCA.pem
RUN chmod 644 /usr/local/share/ca-certificates/rootCA.pem && \
update-ca-certificates --fresh
ENV NODE_EXTRA_CA_CERTS=/etc/ssl/certs/ca-certificates.crt

WORKDIR /app
  