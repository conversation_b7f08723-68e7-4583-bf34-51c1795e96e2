@use '@styles/mixins' as *;
@use '@styles/variables' as *;

html {
  --font-family: var(--font-jakarta);
  --text-h1: #{$text-24};
  --text-h2: #{$text-20};
  --text-h3: #{$text-16};
  --text-p: #{$text-16};
  --text-14: #{$text-14};
  --text-12: #{$text-12};

  @include tablet {
    --text-h1: #{$text-32};
    --text-h2: #{$text-24};
  }

  @include desktop {
    --text-h1: #{$text-38};
    --text-h2: #{$text-24};
  }

  --line-height-h1: 1.3em;
  --line-height-h2: 1.4;
  --margin-h1: 1.125rem;
  --margin-h2: 1.5rem;
  --margin-h3: 1.75rem;
  --margin-p: 2rem;

  @include tablet {
    --line-height-h1: 1.2em;
    --margin-h1: 1.25rem;
    --margin-h2: 1.75rem;
    --margin-h3: 1.75rem;
  }

  @include desktop {
    --margin-h1: 1.5rem;
    --margin-h2: 2rem;
    --margin-h3: 2rem;
  }

  --font-h1: 800 var(--text-h1) / var(--line-height-h1) var(--font-family);
  --font-h2: 700 var(--text-h2) / var(--line-height-h2) var(--font-family);
  --font-h3: 700 var(--text-h3) / 1.3em var(--font-family);
  --font-p: 400 var(--text-p) / 1.6em var(--font-family);
  --font-caption: 400 var(--text-12) / 1.6em var(--font-family);
  --font-disclaimer: 300 var(--text-14) / 1.6em var(--font-family);

  font-family: var(--font-family);
  font-size: #{$text-16};
  line-height: 1.6;
  color: $black;
}

.h1,
h1 {
  font: var(--font-h1);
  margin-block: var(--margin-h1);
}

.h2,
h2 {
  font: var(--font-h2);
  margin-block: var(--margin-h2);
}

.h3,
h3 {
  font: var(--font-h3);
  margin-block: var(--margin-h3);
}

.p,
p {
  margin-block: var(--margin-p);
}

.p {
  font: var(--font-p);
}

.caption {
  font: var(--font-caption);
}

.disclaimer {
  font: var(--font-disclaimer);
}
