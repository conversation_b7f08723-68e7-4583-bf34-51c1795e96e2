import { CookieService } from './cookieService';
import * as Sentry from '@sentry/nextjs';

export const CookieServiceForMiddleware = {
  /**
   * Setters
   */
  setAuthenticationCookie(data, cookies) {
    const isDecoded = data.includes('Account');

    if (isDecoded) {
      cookies.set({
        name: CookieService.cookie.authentication,
        value: btoa(JSON.stringify(data)),
        ...CookieService.options,
      });
    } else {
      cookies.set({
        name: CookieService.cookie.authentication,
        value: data,
        ...CookieService.options,
      });
    }
  },
  setVolatileCookie(data, cookies) {
    cookies.set({
      name: CookieService.cookie.volatile,
      value: data,
      ...CookieService.options,
    });
  },
  setExpiresCookie(data, cookies) {
    cookies.set({
      name: CookieService.cookie.expires,
      value: data,
      ...CookieService.options,
    });
  },
  setAccountCookie(data, cookies) {
    cookies.set({
      name: CookieService.cookie.account,
      value: data,
      ...CookieService.options,
    });
  },
  setUserTypeCookie(data, cookies) {
    cookies.set({
      name: CookieService.cookie.userType,
      value: data,
      ...CookieService.options,
    });
  },
  setProfileCookie(data, cookies) {
    cookies.set({
      name: CookieService.cookie.profile,
      value: data,
      ...CookieService.options,
    });
  },
  setAccountLevelCookie(data, cookies) {
    if (typeof data === 'string') {
      cookies.set({
        name: CookieService.cookie.accountLevel,
        value: data,
        ...CookieService.options,
      });
    } else {
      cookies.set({
        name: CookieService.cookie.accountLevel,
        value: JSON.stringify(data),
        ...CookieService.options,
      });
    }
  },
  setUserProfilesCookie(data, cookies) {
    if (typeof data === 'string') {
      cookies.set({
        name: CookieService.cookie.userProfiles,
        value: data,
        ...CookieService.options,
      });
    } else {
      cookies.set({
        name: CookieService.cookie.userProfiles,
        value: JSON.stringify(data),
        ...CookieService.options,
      });
    }
  },
  setUpgradeCookie(data, cookies) {
    if (typeof data === 'string') {
      cookies.set({
        name: CookieService.cookie.upgrade,
        value: data,
        ...CookieService.options,
      });
    } else {
      cookies.set({
        name: CookieService.cookie.upgrade,
        value: JSON.stringify(data),
        ...CookieService.options,
      });
    }
  },

  /**
   * Getters
   */
  getAuthenticationCookie(cookies) {
    const value = CookieService.exportString(
      cookies.get(CookieService.cookie.authentication)?.value,
    );

    if (!value) return '';

    const isDecoded = value.includes('Account');

    if (isDecoded) {
      return value.replaceAll('"', '');
    } else {
      try {
        return JSON.parse(atob(value)).replaceAll('"', '');
      } catch (e) {
        return '';
      }
    }
  },
  getVolatileCookie(cookies) {
    return CookieService.exportString(
      cookies.get(CookieService.cookie.volatile)?.value,
    );
  },
  getExpiresCookie(cookies) {
    return CookieService.exportInteger(
      cookies.get(CookieService.cookie.expires)?.value,
    );
  },
  getAccountCookie(cookies) {
    return CookieService.exportInteger(
      cookies.get(CookieService.cookie.account)?.value,
    );
  },
  getUserTypeCookie(cookies) {
    return CookieService.exportString(
      cookies.get(CookieService.cookie.userType)?.value,
    );
  },
  getProfileCookie(cookies) {
    return CookieService.exportInteger(
      cookies.get(CookieService.cookie.profile)?.value,
    );
  },
  getAccountLevelCookie(cookies) {
    const value = CookieService.exportString(
      cookies.get(CookieService.cookie.accountLevel)?.value,
    );

    try {
      return JSON.parse(value);
    } catch (e) {
      return null;
    }
  },
  getUserProfilesCookie(cookies) {
    const value = CookieService.exportString(
      cookies.get(CookieService.cookie.userProfiles)?.value,
    );

    try {
      return JSON.parse(value);
    } catch (e) {
      return null;
    }
  },

  /**
   * Delete
   */
  deleteAuthenticationCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.authentication,
      value: '',
      expires: new Date(0),
      ...CookieService.options,
    });
  },
  deleteVolatileCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.volatile,
      value: '',
      expires: new Date(0),
      ...CookieService.options,
    });
  },
  deleteExpiresCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.expires,
      value: '',
      expires: new Date(0),
      ...CookieService.options,
    });
  },
  deleteAccountCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.account,
      value: '',
      expires: new Date(0),
      ...CookieService.options,
    });
  },
  deleteUserTypeCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.userType,
      value: '',
      expires: new Date(0),
      ...CookieService.options,
    });
  },
  deleteProfileCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.profile,
      value: '',
      expires: new Date(0),
      ...CookieService.options,
    });
  },
  deleteAccountLevelCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.accountLevel,
      value: '',
      expires: new Date(0),
      ...CookieService.options,
    });
  },
  deleteUserProfilesCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.userProfiles,
      value: '',
      expires: new Date(0),
      ...CookieService.options,
    });
  },
  deleteFirstTimeVisitorCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.firstTimeVisitor,
      value: '',
      expires: new Date(0),
      ...CookieService.options,
      sameSite: 'Strict',
    });
  },
  deleteShowCheckoutSuccessCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.showCheckoutSuccess,
      value: '',
      expires: new Date(0),
      ...CookieService.options,
      sameSite: 'Strict',
    });
  },
  deleteUpgradeCookie(response) {
    response.cookies.set({
      name: CookieService.cookie.upgrade,
      value: '',
      expires: new Date(0),
      ...CookieService.options,
    });
  },

  /**
   * Custom
   */
  passCookiesFromRequestToResponse(request, response) {
    request.cookies.getAll().forEach((key) => {
      const cookie = request.cookies.get(key);

      if (cookie) {
        switch (cookie.name) {
          case CookieService.cookie.authentication:
            this.setAuthenticationCookie(cookie.value, response.cookies);
            break;
          case CookieService.cookie.volatile:
            this.setVolatileCookie(cookie.value, response.cookies);
            break;
          case CookieService.cookie.expires:
            this.setExpiresCookie(cookie.value, response.cookies);
            break;
          case CookieService.cookie.account:
            this.setAccountCookie(cookie.value, response.cookies);
            break;
          case CookieService.cookie.userType:
            this.setUserTypeCookie(cookie.value, response.cookies);
            break;
          case CookieService.cookie.profile:
            this.setProfileCookie(cookie.value, response.cookies);
            break;
          case CookieService.cookie.accountLevel:
            this.setAccountLevelCookie(cookie.value, response.cookies);
            break;
          case CookieService.cookie.userProfiles:
            this.setUserProfilesCookie(cookie.value, response.cookies);
            break;
          case CookieService.cookieLegacy.reviewReminded:
          case CookieService.cookie.features:
          case CookieService.cookie.guideSlotsData:
          case CookieService.cookie.upgrade:
            // intentionally ignored
            break;
          default:
            if (CookieService.isInternal(cookie.name)) {
              response.cookies.set({
                name: cookie.name,
                value: cookie.value,
                ...CookieService.options,
                sameSite: 'Strict',
              });
            }
            break;
        }
      }
    });

    return response;
  },
  cleanAuthenticationCookies(response) {
    this.deleteAuthenticationCookie(response);
    this.deleteVolatileCookie(response);
    this.deleteExpiresCookie(response);
    this.deleteAccountCookie(response);
    this.deleteUserTypeCookie(response);
    this.deleteProfileCookie(response);
    this.deleteUserProfilesCookie(response);
    this.deleteAccountLevelCookie(response);
    this.deleteFirstTimeVisitorCookie(response);
    this.deleteShowCheckoutSuccessCookie(response);
    this.deleteUpgradeCookie(response);
    Sentry.setUser(null);

    return response;
  },
};
