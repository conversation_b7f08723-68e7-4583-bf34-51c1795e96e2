import Api from '../api';

const mapPromotion = (resource: PromotionResource) => ({
  name: resource.name,
  bannerType: resource.banner_type,
  targets: resource.targets,
  abTag: resource.ab_tag,
  link: resource.link,
  url: resource.url,
  expired: resource.expired,
  images: resource.images.map(mapImage),
  priority: resource.priority,
});

const mapImage = (image: PromotionImageResource) => ({
  href: image.href,
  imageType: image.image_type,
  midColor: image.mid_color,
});

type GetPromotionsOptions = {
  page?: number;
  limit?: number;
  types?: BannerType[];
  targets?: string[];
};
async function getPromotions(options: GetPromotionsOptions = {}) {
  const { page = 1, limit = 20, types = [], targets = [] } = options;

  const params = new URLSearchParams([
    ['limit', limit.toString()],
    ['page', page.toString()],
    ...types.map((type) => ['types[]', type]),
    ...targets.map((target) => ['targets[]', target]),
  ]);

  const url = `/promotions?${params.toString()}`;

  const response: PromotionsResponse | ErrorResponse =
    await Api.serverGateway(url);

  if (response.status === 'error') {
    // ToDo: generic sentry error logging

    return {
      status: 'ok',
      data: [],
    } as PromotionsResponse;
  }

  return response;

  // const promotions = response.data.map(mapPromotion);

  // return {
  //   status: response.status,
  //   data: promotions,
  // };
}

// type Promotion = ReturnType<typeof mapPromotion>;
// type PromotionImage = ReturnType<typeof mapImage>;
// type PromotionsResult = Awaited<ReturnType<typeof getPromotions>>;
// type PromotionsSuccessResult = {
//   status: 'ok';
//   data: Promotion[];
// };

export { getPromotions };
export type { PromotionsResponse, PromotionResource };

// generated with cursor from
// https://gitlab.dyninno.net/entertech/services/back-office/-/blob/main/src/Api/Service/ApiBannerManager.php
// modified, tested and verified

type PromotionsResponse = {
  status: 'ok';
  data: PromotionResource[];
};

type ErrorResponse = {
  status: 'error';
  message: string;
  code?: number;
  messageKey?: string;
};

type PromotionResource = {
  name: string;
  banner_type: BannerType;
  targets: string[];
  ab_tag: string | null;
  link: string;
  url: string;
  expired: string; // ISO 8601 datetime string
  images: PromotionImageResource[];
  priority: number;
};

type PromotionImageResource = {
  href: string;
  image_type: ImageType;
  mid_color: string | null;
};

const BANNER_TYPE = {
  CastingCalls: 'Castingcalls',
  Compcards: 'Compcards',
  Cta: 'CTA',
  System: 'System',
  Announcements: 'Announcements',
} as const;

const IMAGE_TYPE = {
  Desktop: 'desktop',
  Mobile: 'mobile',
  Thumbnail: 'thumbnail',
  Story: 'story',
} as const;

type BannerType = (typeof BANNER_TYPE)[keyof typeof BANNER_TYPE];
type ImageType = (typeof IMAGE_TYPE)[keyof typeof IMAGE_TYPE];
