// Basic resource boilerplate types

type RelHref = {
  rel: string;
  href: string;
};

type IdTitleName = RelHref & {
  id: number;
  title: string;
  name: string;
};

type IdSlugTitle = RelHref & {
  id: number;
  slug: string;
  title: string;
};

type IdSlugTitleName = RelHref & {
  id: number;
  slug: string;
  title: string;
  name: string;
};

/**
 * Represents a PHP API object that can be either a value or an empty list.
 *
 * @example
 * ```ts
 * type Example = PhpOrEmptyList<{ foo: string }>;
 * // Example can be { foo: string } or []
 * ```
 */
type PhpOrEmptyList<T> = T | [];

/**
 * Utility function to remove the empty list case from a PhpOrEmptyList<T> value.
 */
function removeEmptyList<T>(value: PhpOrEmptyList<T>): T | null;
function removeEmptyList<T>(value: PhpOrEmptyList<T>, defaultValue: T): T;
function removeEmptyList<T>(value: PhpOrEmptyList<T>, defaultValue?: T) {
  if (Array.isArray(value)) {
    if (defaultValue) {
      return defaultValue;
    }

    return null;
  }

  return value;
}

export type {
  RelHref,
  IdTitleName,
  IdSlugTitle,
  IdSlugTitleName,
  PhpOrEmptyList,
};
export { removeEmptyList };
