export const ARTICLE_DEFAULT_REDIRECT_URL = '/blog';
export const ARTICLE_PAGE_LIMIT = 12;

export const ARTICLE_STATUS = {
  Draft: 0,
  Published: 1,
  Archived: 2,
  Deleted: 3,
} as const;

export const ARTICLE_CATEGORY = {
  news: 'news',
  lessons: 'lessons',
  info: 'info',
  auditionTips: 'audition-tips',
  successStories: 'success-stories',
  skillDevelopment: 'skill-development',
  careerPaths: 'career-paths',
  industryNews: 'industry-news',
  beginnerGuide: 'beginner-s-guide',
  castingFaqs: 'casting-faqs',
  awards: 'awards',
} as const;

export const ARTICLE_NEWS_CATEGORIES: string[] = [
  ARTICLE_CATEGORY.news,
  ARTICLE_CATEGORY.industryNews,
];

export const ARTICLE_LESSONS_CATEGORIES: string[] = [
  ARTICLE_CATEGORY.lessons,
  ARTICLE_CATEGORY.auditionTips,
];

export const ARTICLE_FILTER_CATEGORIES: string[] = [
  ARTICLE_CATEGORY.auditionTips,
  ARTICLE_CATEGORY.beginnerGuide,
  ARTICLE_CATEGORY.skillDevelopment,
  ARTICLE_CATEGORY.careerPaths,
  ARTICLE_CATEGORY.successStories,
  ARTICLE_CATEGORY.industryNews,
  ARTICLE_CATEGORY.awards,
  ARTICLE_CATEGORY.castingFaqs,
];

// dynamically defined in the backend, not exhaustive
export const ARTICLE_CATEGORIES: string[] = [
  ARTICLE_CATEGORY.news,
  ARTICLE_CATEGORY.lessons,
  ...ARTICLE_FILTER_CATEGORIES,
];

export const ARTICLE_CATEGORIES_WITH_INFO: string[] = [
  ARTICLE_CATEGORY.info,
  ...ARTICLE_FILTER_CATEGORIES,
];

export const mapCategorySlug = (category: string) => {
  if (ARTICLE_LESSONS_CATEGORIES.includes(category)) {
    return ARTICLE_CATEGORY.auditionTips;
  }
  if (ARTICLE_NEWS_CATEGORIES.includes(category)) {
    return ARTICLE_CATEGORY.industryNews;
  }

  return category;
};

export const ARTICLE_CATEGORIES_LABELS: Record<string, string> = {
  [ARTICLE_CATEGORY.news]: 'News',
  [ARTICLE_CATEGORY.lessons]: 'Lessons',
  [ARTICLE_CATEGORY.info]: 'Info',
  [ARTICLE_CATEGORY.auditionTips]: 'Audition Tips',
  [ARTICLE_CATEGORY.successStories]: 'Success Stories',
  [ARTICLE_CATEGORY.skillDevelopment]: 'Skill Development',
  [ARTICLE_CATEGORY.careerPaths]: 'Career Paths',
  [ARTICLE_CATEGORY.industryNews]: 'Industry News',
  [ARTICLE_CATEGORY.beginnerGuide]: "Beginner's Guide",
  [ARTICLE_CATEGORY.castingFaqs]: 'Casting FAQs',
  awards: 'Awards',
};

export const ARTICLE_CATEGORIES_CHIP_LABELS: Record<string, string> = {
  [ARTICLE_CATEGORY.successStories]: 'Success Stories',
  [ARTICLE_CATEGORY.skillDevelopment]: 'Skill Development',
  [ARTICLE_CATEGORY.careerPaths]: 'Career Paths',
  [ARTICLE_CATEGORY.industryNews]: 'Industry News',
  [ARTICLE_CATEGORY.beginnerGuide]: 'Beginner',
  [ARTICLE_CATEGORY.castingFaqs]: 'FAQs',
};

export const HOW_TO_MOCK = {
  name: 'New to the industry or just signed up to AllCasting?',
  description:
    'This guide will walk you through the exact steps to find and apply for your first casting call — even if you have zero experience.',
  cta: {
    text: 'Ready to get cast?',
    link: '/register',
  },
  items: [
    {
      title: 'Complete Your Profile',
      description:
        'Casting directors are more likely to choose talent with complete profiles. Add a headshot, bio, experience, and contact details.',
    },
    {
      title: 'Browse Casting Calls',
      pro_tip:
        'Apply to multiple casting calls to increase your chances of getting cast.',
      description:
        'Navigate to the Casting Calls section and apply filters by location, category, or compensation.',
    },
    {
      title: 'Apply Early and Often',
      description:
        'Applying to new listings as soon as they are posted increases your chances of getting noticed.',
    },
  ],
};
