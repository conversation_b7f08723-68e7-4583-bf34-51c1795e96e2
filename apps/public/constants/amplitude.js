export const AMP_ELEMENTS = {
  type: {
    banner: 'banner',
    button: 'button',
    link: 'link',
    tab: 'tab',
    input: 'input',
    enterKey: 'enter_key',
    select: 'select',
    checkbox: 'checkbox',
    popup: 'popup',
    block: 'block',
  },
  context: {
    ctaBlock: 'cta_block',
    ctaPopup: 'cta_popup',
  },
  position: {
    top: 'top',
    list: 'list',
    bottomSlideIn: 'bottom_slide_in',
  },
  scope: {
    global: 'global',
    homepage: 'homepage',
    modal: 'modal_window',
    burger: 'burger',
    talentSearchPage: 'talent_search_page',
    castingCallListPage: 'casting_calls_list_page',
    castingCallItemPage: 'casting_calls_item_page',
    premiumPage: 'premium_page',
    lifetimeMembershipPage: 'lifetime_membership_page',
    castingDirectorsPage: 'castingDirectorsPage',
  },
  section: {
    mainBanner: 'main_banner',
    spotlight: 'spotlight',
    header: 'header',
    classroom: 'classroom',
    reviews: 'reviews',
    showcase: 'showcase',
    navigation: 'navigation',
    castingCall: 'casting_call',
    role: 'role',
    premiumActions: 'premium_actions',
    filter: 'filter',
    bottomCta: 'bottom_cta',
    signup: 'signup',
  },
  result: {
    success: 'success',
    fail: 'fail',
  },
};

export const AMP_EVENTS = {
  modalViewed: 'modal_viewed',
  elementClicked: 'element_clicked',
  elementViewed: 'element_viewed',
  elementDismissed: 'element_dismissed',
  formStarted: 'form_started',
  modalSubscribeClicked: 'modal_subscribe_clicked',
  topSliderBannerClicked: 'top_slider_banner_clicked',
  topCtaBlockClicked: 'top_cta_block_clicked',
  topCtaBlockShown: 'top_cta_block_shown',
  invalidEventOccurred: 'invalid_event_occurred',
  formSubmitted: 'form_submitted',
  formAbandoned: 'form_abandoned',
  phoneNotificationDisplayed: 'phone_notification_displayed',
  phoneNotificationDismissed: 'phone_notification_dismissed',
  submitPhoneNumber: 'Submit Phone Number',
  viewLogin: 'View Login',
  viewRegister: 'View Register',
  signUp: 'Sign Up',
  viewHome: 'View Home',
  viewCastingCallList: 'View Casting Call List',
  viewCastingCall: 'View Casting Call',
  viewTalentList: 'View Talent List',
  viewReviews: 'View Reviews',
  viewBlog: 'View Blog',
  viewBlogArticle: 'View Blog Article',
  view500: 'View 500',
  view404: 'View 404',
  viewForgotPassword: 'View Forgot Password',
  viewDirector: 'View Director',
  viewDirectorLanding: 'View Director Landing',
  viewTalent: 'View Talent',
  viewContactUs: 'View Contact Us',
  viewAbout: 'View About',
  viewPasswordRecover: 'View Password Recover',
  viewOnboarding: 'View Onboarding',
  viewSafetyGo: 'View Safety Go',
  viewAffiliates: 'View Affiliates',
  viewDNSMPI: 'View DNSMPI',
  viewFacebookDeletionStatus: 'View Facebook Deletion Status',
  viewHowToCancelSubscription: 'View How To Cancel Subscription',
  viewIsAllcastingLegit: 'View Is Allcasting Legit',
  viewLifetime: 'View Lifetime',
  viewOpenCastingCalls: 'View Open Casting Calls',
  viewPremium: 'View Premium',
  viewPrices: 'View Prices',
  viewPrivacyPolicy: 'View Privacy Policy',
  viewRefundPolicy: 'View Refund Policy',
  viewStopEmailSubmissions: 'View Stop Email Submissions',
  viewTermsOfUse: 'View Terms of Use',
  viewUnsubscribe: 'View Unsubscribe',
};
