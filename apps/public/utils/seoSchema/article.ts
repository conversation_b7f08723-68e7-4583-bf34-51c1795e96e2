// https://developers.google.com/search/docs/appearance/structured-data/article

export const formatArticleSchema = ({
  headline,
  description,
  author,
  datePublished,
  image,
}: SchemaArticleProps) => ({
  '@context': 'https://schema.org',
  '@type': 'Article',
  headline,
  description,
  image: image && [image],
  datePublished, // 2025-08-01T09:46:30-07:00
  author: author && [
    {
      '@type': 'Person',
      name: author.name,
    },
  ],
  publisher: {
    '@type': 'Organization',
    name: 'allcasting',
    logo: {
      '@type': 'ImageObject',
      url: 'https://allcasting.com/favicon.svg',
    },
  },
});

type SchemaArticleProps = {
  headline: string;
  description: string;
  author?: { name: string };
  datePublished: string;
  image?: string;
};
