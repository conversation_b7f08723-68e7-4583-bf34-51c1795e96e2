// https://developers.google.com/search/docs/appearance/structured-data/job-posting
export const formatJobPostData = (castingCall = {}) => {
  return {
    '@context': 'https://schema.org/',
    '@type': 'JobPosting',
    title: castingCall.title || '',
    description: `<p>${castingCall.description || ''}</p>`,
    identifier: {
      '@type': 'PropertyValue',
      name: castingCall.agent_name || 'Unknown',
    },
    datePosted: castingCall.created_date || '',
    validThrough: castingCall.expiration_date || castingCall.expiration || '',
    jobLocation: {
      '@type': 'Place',
      address: {
        '@type': 'PostalAddress',
        addressLocality: castingCall.address?.city || '',
        addressRegion: castingCall.address?.state || '',
        postalCode: castingCall.address?.zip || '',
        addressCountry: castingCall.address?.country?.code || '',
      },
    },
    employmentType: 'OTHER',
    hiringOrganization: {
      '@type': 'Organization',
      name: castingCall.agent_name || 'Unknown',
    },
  };
};
