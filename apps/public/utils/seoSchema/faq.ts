// https://developers.google.com/search/docs/appearance/structured-data/faqpage

import type { Article } from '../../services/endpoints/articles';
type SchemaFaqProps = Pick<Article, 'questionsAndAnswers'>;

export const formatFaqSchema = ({ questionsAndAnswers }: SchemaFaqProps) => ({
  '@context': 'https://schema.org',
  '@type': 'FAQPage',
  mainEntity: questionsAndAnswers.map((qa) => ({
    '@type': 'Question',
    name: qa.question,
    acceptedAnswer: {
      '@type': 'Answer',
      text: qa.answer,
    },
  })),
});
