/**
 * Generated by cursor. Generates a deterministic pseudo-random integer
 * Useful for consistent rendering.
 *
 * @param input - The input number to hash (e.g., an article ID)
 * @param min - The minimum value of the range (inclusive, default: 0)
 * @param max - The maximum value of the range (exclusive, default: 100)
 * @returns A deterministic pseudo-random integer in the range [min, max)
 *
 */
export function deterministicRandom(input: number, min = 0, max = 100) {
  // FNV-1a hash function
  let hash = 0x811c9dc5;
  const prime = 0x01000193;

  const str = input.toString();

  for (let i = 0; i < str.length; i++) {
    hash = hash ^ str.charCodeAt(i);
    hash = hash * prime;
    hash = hash & 0xffffffff; // Keep 32-bit
  }

  const randomValue = Math.abs(hash) % (max - min);

  return randomValue + min;
}
