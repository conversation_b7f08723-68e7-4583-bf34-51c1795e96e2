import { Ab } from '../services/ab';
import { CookieService } from '../services/cookieService';
import type { ParsedUrlQuery } from 'querystring';
import type {
  GetServerSidePropsContext,
  NextApiRequest,
  NextApiResponse,
} from 'next';

async function sharedServerSideProps(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  const defaultABGroupValues = Ab.getDefaultABGroupValues(req, res);
  const defaultAuthValues = {
    userType: CookieService.getUserTypeCookie(req, res),
  };

  return {
    defaultABGroupValues: defaultABGroupValues,
    defaultAuthValue: defaultAuthValues,
  };
}

function param(key: string | string[] | undefined): string | undefined;
function param(key: string | string[] | undefined, defaultVal: string): string;
function param(key: string | string[] | undefined, defaultVal?: string) {
  if (typeof key === 'string') {
    return key;
  }

  if (!defaultVal) {
    return key?.[0];
  }

  return defaultVal;
}

function getPageParam(query: ParsedUrlQuery) {
  const pageParam = param(query.page);

  if (pageParam == null) {
    return undefined;
  }

  const match = pageParam.match(/^(\d+)$/);

  return match ? Number.parseInt(match[1]) : 1;
}

type Req = GetServerSidePropsContext['req'];
type Res = GetServerSidePropsContext['res'];

export { param, sharedServerSideProps, getPageParam };
export type { Req, Res };
