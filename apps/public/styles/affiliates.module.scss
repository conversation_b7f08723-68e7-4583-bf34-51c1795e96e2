@import '../styles/mixins';
@import '../styles/variables';

.container {
  display: flex;
  flex-direction: column;
  margin-left: auto;
  margin-right: auto;
  max-width: $content-max-width;
  width: 100%;
  justify-content: center;
  padding-left: $space-20;
  padding-right: $space-20;

  @include desktop {
    padding-left: $space-60;
    padding-right: $space-60;
  }
}

.main-banner {
  position: relative;
  display: flex;
  width: 100%;
  padding: 42px 0 $space-40;
  background: $gradient-violet-supper;
  overflow: hidden;
  min-height: 400px;

  &::before {
    z-index: 0;
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 150px;
    background: url('#{$assetUrl}/assets/affiliates/header-before.svg') 0 0
      no-repeat;
    background-size: contain;
  }

  &::after {
    z-index: 0;
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 180px;
    height: 180px;
    background: url('#{$assetUrl}/assets/affiliates/header-after.svg') 0 0
      no-repeat;
    background-size: contain;
  }

  @include desktop {
    padding: 0;
    min-height: unset;

    &::before {
      display: none;
    }

    &::after {
      display: none;
    }
  }
}

.main-banner-body {
  text-align: center;
  flex-direction: column-reverse;
  padding: 0 $space-20;
  width: 100%;
  max-width: $content-max-width;

  @include desktop {
    padding: 0 $space-60;
    min-height: 510px;
    text-align: left;
    flex-direction: row;
    justify-content: flex-start;
    background: url('#{$assetUrl}/assets/affiliates/hero-image.webp') right
      no-repeat;
    background-size: contain;
  }
}

.main-banner-welcome {
  z-index: 1;
  margin: 0 auto 7px;

  @include desktop {
    margin: 0;
    width: 50%;
    min-width: 555px;
    padding: 107px 0;
    order: 1;
  }
}

.main-banner-title {
  font-style: normal;
  font-size: 28px;
  line-height: 1.3;
  color: $violet-70;
  font-weight: 800;
  margin: 0 0 $space-50;

  @include desktop {
    font-size: 48px;
    margin: $space-30 0 $space-60;
  }
}

.main-banner-text {
  font-style: normal;
  font-size: 22px;
  line-height: 1.6;
  color: $violet-70;
  font-weight: 700;
  margin: 0 auto $space-30;
  max-width: 462px;

  @include desktop {
    font-size: 26px;
    max-width: none;
    margin: 0 0 22px;
  }
}

.intro-wrapper {
  display: flex;
  flex-direction: column;
  row-gap: $space-30;
  margin-top: 80px;
  margin-bottom: $space-60;

  h2 {
    font-weight: 800;
    font-size: 36px;
    text-align: center;
    line-height: normal;

    @include desktop {
      font-size: 48px;
    }
  }

  @include tablet {
    margin-bottom: 100px;
  }
}

.intro-block {
  display: flex;
  flex-direction: column;
  padding: $space-30;
  align-items: center;
  gap: $space-30;

  @include tablet {
    flex-direction: row;
    padding: 0 $space-30;
  }
}

.intro-image {
  width: 100%;
  text-align: center;

  img {
    width: 100%;
    max-width: 400px;
    height: auto;

    @include desktop {
      width: auto;
    }
  }

  @include tablet {
    text-align: left;
    width: 50%;
  }
}

.intro-description {
  font-weight: 300;
  width: 100%;
  text-align: center;

  h2 {
    font-size: 28px;
    text-align: center;
    margin-bottom: $space-30;
    margin-top: 0;

    @include tablet {
      text-align: left;
    }
  }

  p {
    margin-bottom: $space-40;
  }

  @include tablet {
    width: 50%;
    text-align: left;
  }
}

.more-block {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: $space-65 70px;
  margin-bottom: 90px;

  @include tablet {
    grid-template-columns: repeat(2, 1fr);
    row-gap: $space-50;
  }
}

.more-title {
  font-size: 28px;
  text-align: center;
  margin-bottom: $space-60;
  margin-top: 0;
  font-weight: 800;

  @include tablet {
    text-align: left;
  }
}

.more-item {
  display: flex;
  flex-direction: column;
  text-align: center;
  gap: $space-20;
  padding: 0 $space-35;

  @include tablet {
    flex-direction: row;
    text-align: left;
    gap: $space-25;
    padding: 0;
  }
}

.more-description {
  span {
    font-weight: 600;
    font-size: 18px;
  }

  p {
    font-weight: 300;
    font-size: 16px;
    margin-top: 7px;
  }
}

.how-it-works-block {
  display: flex;
  flex-direction: column;
  row-gap: $space-30;
  margin-bottom: 100px;

  h2 {
    font-weight: 800;
    font-size: 36px;
    text-align: center;
    line-height: normal;

    @include desktop {
      font-size: 48px;
    }
  }
}

.how-it-works-left {
  display: flex;
  flex-direction: column;
  padding: $space-30;
  align-items: center;

  @include mobilexl {
    flex-direction: row;
    padding: 0 $space-30;
  }
}

.how-it-works-right {
  display: flex;
  flex-direction: column;
  padding: $space-30;
  align-items: center;

  & div.how-it-works-image {
    display: flex;
    justify-content: center;

    @include mobilexl {
      justify-content: flex-end;
    }
  }

  @include mobilexl {
    flex-direction: row-reverse;
    padding: 0 $space-30;
  }
}

.how-it-works-image {
  width: 100%;
  text-align: center;

  img {
    width: 100%;
    max-width: 400px;
    height: auto;

    @include desktop {
      width: auto;
    }
  }

  @include mobilexl {
    text-align: unset;
    width: 50%;
  }
}

.how-it-works-description {
  font-weight: 300;
  width: 100%;

  @include tablet {
    width: 50%;

    h3 {
      font-size: 28px;
      font-weight: 800;
      text-align: left;
      margin-bottom: $space-30;
      margin-top: 0;
    }
  }
}

.stats-section {
  background: $violet-10;
  padding-top: 70px;
  padding-bottom: $space-60;

  @include desktop {
    padding-top: 90px;
    padding-bottom: 90px;
  }
}

.stats-section-container {
  padding: 0 $space-20;
  width: 100%;
  max-width: $content-max-width;
  display: flex;
  flex-direction: column;
  row-gap: 50px;

  @include desktop {
    justify-content: space-around;
    flex-direction: row;
    padding: 0 $space-60;
  }
}

.stat-item {
  text-align: center;

  div {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    background-color: $violet-20;
    border-radius: 12px;
    font-size: 24px;
    font-weight: 700;
    padding: $space-5 0;
    width: 115px;
  }

  p {
    margin-top: $space-20;
    font-size: 16px;
    font-weight: 600;
  }
}

.company-section-container {
  @include desktop {
    padding-left: $space-60;
    padding-right: $space-60;
  }
}

.company-section {
  padding-top: $space-50;
  padding-bottom: 58px;

  @include desktop {
    padding-top: $space-45;
    padding-bottom: 88px;
  }
}

.company-info {
  @include tablet {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: $space-10;
  }
}

.carousel-quote-container {
  width: 99%;

  .quote-box {
    box-sizing: border-box;
    display: flex;
    position: relative;
    flex-flow: column;
    justify-content: center;
    margin: $space-40 $space-20;
    padding: 34px $space-35 29px;
    background: $white;
    transition: all ease-in-out 0.3s;
    text-align: center;

    @include desktop {
      padding: $space-65 140px;
    }
  }

  .quote-icon {
    margin-left: auto;
    margin-right: auto;
    margin-bottom: $space-40;
  }

  .quote-text {
    font-weight: 300;
    font-size: 18px;
    line-height: 1.5;
    color: $black;
    margin-bottom: 24px;
    word-break: break-word;

    @include desktop {
      margin-bottom: 29px;
    }
  }

  .quote-author {
    font-weight: 700;
    font-size: 16px;
    line-height: 1.6;
    color: $black;
  }
}

.cta-section {
  background: $violet-10;
  padding-top: 110px;
  padding-bottom: 160px;
  position: relative;
  overflow: hidden;

  &::before {
    z-index: 0;
    content: '';
    position: absolute;
    top: 0;
    left: calc(50% - 135px);
    width: 270px;
    height: 270px;
    background: url('#{$assetUrl}/assets/affiliates/cta-top.webp') 0 0 no-repeat;
    background-size: contain;
  }

  &::after {
    z-index: 0;
    content: '';
    position: absolute;
    bottom: -90px;
    right: -150px;
    width: 640px;
    height: 370px;
    background: url('#{$assetUrl}/assets/affiliates/cta-bg.webp') 0 0 no-repeat;
    background-size: contain;
  }

  @include desktop {
    padding-bottom: 100px;

    &::after {
      right: 0;
    }
  }
}

.cta-section-container {
  padding: 0 $space-20;
  width: 100%;
  max-width: $content-max-width;
  position: relative;

  @include desktop {
    padding: 0 $space-60;
  }
}

.cta-heading {
  font-weight: 800;
  font-size: 24px;
  line-height: 1.3;
  text-align: center;
  color: $black;
  z-index: 1;
  margin-bottom: $space-50;

  @include desktop {
    font-size: 30px;
  }
}

.cta-btn-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}
