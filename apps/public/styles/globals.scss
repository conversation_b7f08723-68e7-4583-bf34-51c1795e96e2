@import '../styles/variables';

* {
  box-sizing: border-box;
}

html {
  background: $white;
  text-size-adjust: 100%;
  tab-size: 4;
}

body {
  margin: unset;
}

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
}

a {
  color: inherit;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

p {
  margin: 0;
}

fieldset,
legend {
  padding: 0;
  margin: 0;
  border: none;
}

#onetrust-banner-sdk {
  display: none;
}

body.guest #onetrust-banner-sdk {
  display: inherit;
}

#chat-widget-container {
  /* stylelint-disable */
  z-index: 90 !important;
  /* stylelint-enable */
}
