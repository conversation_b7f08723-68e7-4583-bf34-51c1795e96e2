@import '../styles/mixins';
@import '../styles/variables';
@import 'styles/article-commons';

.learn-more {
  background-color: $violet-10;
  width: 100%;
}

.learn-more-content {
  padding: 60px 20px;
  max-width: $content-max-width;
  margin: 0 auto;

  @include desktop {
    padding: 60px;
  }
}

.learn-more-title {
  font-size: $text-24;
  margin-top: 0;
}

.suggested {
  @extend %article-grid;
}
