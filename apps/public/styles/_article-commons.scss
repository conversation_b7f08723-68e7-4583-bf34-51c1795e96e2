@import './variables';

$padding: 2rem;
$gap-sm: 1rem;
$gap-bg: 1.25rem;

%article-grid {
  display: grid;
  grid-template-columns: 1fr;
  grid-gap: $space-30;

  @include mobilexl {
    grid-template-columns: 1fr 1fr;
  }

  @include desktop {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

%container {
  border: 1px solid $grey-20;
  border-radius: 8px;
}

%title {
  font-size: $text-24;
  margin: 0;
}

%margin-top {
  margin-top: 2rem;
}
