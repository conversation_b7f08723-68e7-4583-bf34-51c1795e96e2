@import '../styles/mixins';
@import '../styles/variables';

.title {
  h1 {
    font-size: 42px;
    font-weight: 800;
    margin: $space-30 0;
  }
}

.sub-title {
  margin: $space-40 0 $space-30;
  font-weight: 800;
}

.link {
  color: $blue-100;
  font-weight: 400;
  cursor: pointer;
}

.image-container {
  margin: 0 auto;
  width: 210px;

  & img {
    cursor: pointer;
    min-width: 100%;
    max-width: 100%;
    min-height: 100%;
    max-height: 100%;
    height: auto;
  }

  @include desktop {
    width: 100%;
    padding: $space-20 $space-60;
  }
}

.image-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.image-preview {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 $space-40;

  & img {
    min-width: 100%;
    max-width: 100%;
    min-height: 100%;
    max-height: 100%;
  }

  @include mobile {
    padding: 0 20%;
  }

  @include mobilexl {
    padding: 0 25%;
  }

  @include tablet {
    padding: 0 30%;
  }

  @include desktop {
    padding: 0 20px;
  }

  @include xlarge {
    padding: 0 10%;
  }
}

.option-list {
  .option-list-element {
    margin-block: 0;
  }
}

.step-list {
  padding-inline: $space-20;

  .step-list-element {
    margin: $space-30 0;
  }

  .step-list-element::marker {
    font-weight: 700;
  }
}

.image-modal {
  background-color: transparent;
  border-radius: unset;
  display: flex;
  justify-content: center;
}

.image-modal-content {
  height: unset;
  border-radius: unset;
}
