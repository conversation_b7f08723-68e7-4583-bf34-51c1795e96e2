@import '../styles/mixins';
@import '../styles/variables';

.container {
  display: flex;
  flex-direction: column;
  margin-left: auto;
  margin-right: auto;
  max-width: $content-max-width;
  width: 100%;
  justify-content: center;
  padding-left: $space-20;
  padding-right: $space-20;

  @include desktop {
    padding-left: $space-65;
    padding-right: $space-60;
  }
}

.hero-block {
  background: $violet-10;
  text-align: center;
  padding: 80px 0 85px;
  min-height: 550px;

  div,
  p {
    z-index: 1;
  }

  &::before {
    z-index: 0;
    content: '';
    position: absolute;
    top: 200px;
    left: -40px;
    width: 250px;
    height: 250px;
    background: url('#{$assetUrl}/assets/premium-subscription/eclipse-bg-left.webp')
      0 0 no-repeat;
    background-size: contain;
  }

  &::after {
    content: '';
    position: absolute;
    top: -20px;
    right: 0;
    left: auto;
    width: 150px;
    height: 150px;
    background: url('#{$assetUrl}/assets/premium-subscription/eclipse-bg-right.webp')
      0 0 no-repeat;
    background-size: contain;
  }

  h1 {
    font-size: 36px;
    font-weight: 800;
    line-height: 1.1;
    z-index: 2;

    span {
      background-image: $gradient-btn-purple;
      background-repeat: repeat;
      display: inline-block;
      color: transparent;
      background-clip: text;
      background-size: 200% auto;
      background-position: 0 0;
    }
  }

  a {
    margin-left: auto;
    margin-right: auto;
    margin-bottom: $space-10;
    font-size: 14px;
    z-index: 2;
  }

  @include mobilexl {
    &::before {
      top: 120px;
      left: -40px;
      width: 350px;
      height: 350px;
    }

    &::after {
      top: -50px;
      right: 0;
      width: 200px;
      height: 200px;
    }
  }

  @include desktop {
    h1 {
      font-size: 48px;
      font-weight: 800;
      line-height: 1.1;
      z-index: 2;
    }

    &::before {
      top: 70px;
      left: 0;
      width: 450px;
      height: 450px;
    }

    &::after {
      top: -20px;
      right: 0;
      width: 280px;
      height: 280px;
    }
  }
}

.price {
  margin-bottom: $space-40;
  font-size: 72px;
  font-weight: 800;
  z-index: 2;

  .period {
    font-size: 32px;
    font-weight: 600;
  }
}

.cancel {
  margin-top: $space-10;
  z-index: 2;
}

.currency {
  font-size: 38px;
  vertical-align: text-top;
  line-height: 2;
}

.feature-block {
  display: grid;
  grid-auto-flow: row;
  grid-template-columns: 1fr;
  gap: $space-20;
  margin: $space-60 $space-10;

  @include mobilexl {
    grid-template-columns: 1fr 1fr;
  }

  @include desktop {
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }
}

.feature {
  display: flex;
  align-items: center;
  flex-direction: column;
  text-align: center;
  padding: $space-15 10%;

  .feature-title {
    font-size: 18px;
    font-weight: 700;
    display: inline-block;
    margin-bottom: $space-10;
  }

  p {
    font-weight: 400;
  }

  @include desktop {
    padding: 0 $space-5;
  }
}

.feature-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  background: $violet-10;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  margin-bottom: $space-25;
}

.benefit-block {
  display: flex;
  flex-direction: column;
  row-gap: $space-30;
  margin-bottom: 100px;

  h2 {
    font-weight: 800;
    font-size: 36px;
    text-align: center;
    line-height: normal;

    @include desktop {
      font-size: 48px;
    }
  }
}

.benefit-left {
  display: flex;
  flex-direction: column;
  padding: $space-30;
  align-items: center;
  gap: $space-30;

  @include mobilexl {
    flex-direction: row;
    padding: 0 $space-30;
  }
}

.benefit-right {
  display: flex;
  flex-direction: column;
  padding: $space-30;
  align-items: center;

  & div.benefit-image {
    display: flex;
    justify-content: center;

    @include mobilexl {
      justify-content: flex-end;
    }
  }

  @include mobilexl {
    flex-direction: row-reverse;
    padding: 0 $space-30;
  }
}

.benefit-image {
  width: 100%;
  text-align: center;

  img {
    width: 100%;
    max-width: 400px;
    height: auto;

    @include desktop {
      width: auto;
    }
  }

  @include mobilexl {
    width: 50%;
  }
}

.benefit-description {
  width: 100%;

  @include mobilexl {
    width: 50%;
  }
}

.cta-block {
  background: $gradient-btn-purple;
  background-size: 200% auto;
  background-position: 0 0;
  text-align: center;
  color: $white;
  padding-bottom: $space-45;

  h2 {
    font-weight: 800;
    font-size: 36px;
    line-height: normal;

    @include desktop {
      font-size: 48px;
    }
  }

  a {
    margin-left: auto;
    margin-right: auto;
    margin-bottom: $space-10;
  }

  p {
    font-weight: 400;
    font-size: 14px;
  }
}

.faq-block {
  padding: $space-50 0 100px;

  h2 {
    font-weight: 800;
    text-align: center;
    font-size: 36px;
    line-height: normal;

    @include desktop {
      font-size: 48px;
    }
  }
}

.terms {
  padding-top: $space-20;
  font-size: 14px;
  color: $grey-100;

  &.light {
    color: $white;
  }
}

.terms-link {
  text-decoration: underline;
  cursor: pointer;

  &:hover {
    opacity: 0.6;
  }
}
