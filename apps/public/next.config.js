const { withSentryConfig } = require('@sentry/nextjs');

/** @type {import('next').NextConfig} */

const envConfig = {
  production: {
    externalApiUrl: process.env.BASE_URL + '/api',
    publicGateway: process.env.PUBLIC_GW,
    internalGateway: process.env.INTERNAL_GW,
    seoAPIUrl: process.env.SEO_URL,
    cacheMediumTerm: '300',
    cacheLongTerm: '900',
    baseUrl: process.env.BASE_URL,
    redirectTalentUrl: process.env.REDIRECT_TALENT_URL,
    redirectProUrl: process.env.REDIRECT_PRO_URL,
    cookieDomain: process.env.COOKIE_DOMAIN,
    analyticsId: process.env.ANALYTICS_ID,
    amplitudeId: process.env.AMPLITUDE_ID,
    amplitudeConsentProvider: process.env.AMPLITUDE_CONSENT_PROVIDER,
    oneTrustId: process.env.ONETRUST_ID,
    abApiUrl: process.env.AB_API_URL,
    liveStreamEnabled: 'false',
    liveChatEnabled: 'true',
    upgradeEnabled: 'true',
    showCastingCallType: 'false',
    landingPageUrl: process.env.LANDING_PAGE_URL,
    featureManagerApiUrl: process.env.FEATURE_MANAGER_API_URL,
  },
  test: {
    externalApiUrl: process.env.BASE_URL + '/api',
    publicGateway: process.env.PUBLIC_GW,
    internalGateway: process.env.INTERNAL_GW,
    seoAPIUrl: process.env.SEO_URL,
    cacheMediumTerm: '60',
    cacheLongTerm: '60',
    baseUrl: process.env.BASE_URL,
    redirectTalentUrl: process.env.REDIRECT_TALENT_URL,
    redirectProUrl: process.env.REDIRECT_PRO_URL,
    cookieDomain: process.env.COOKIE_DOMAIN,
    analyticsId: process.env.ANALYTICS_ID,
    amplitudeId: process.env.AMPLITUDE_ID,
    amplitudeConsentProvider: process.env.AMPLITUDE_CONSENT_PROVIDER,
    oneTrustId: process.env.ONETRUST_ID,
    abApiUrl: process.env.AB_API_URL,
    liveStreamEnabled: 'false',
    liveChatEnabled: 'true',
    upgradeEnabled: 'true',
    showCastingCallType: 'true',
    landingPageUrl: process.env.LANDING_PAGE_URL,
    featureManagerApiUrl: process.env.FEATURE_MANAGER_API_URL,
  },
  development: {
    externalApiUrl: process.env.BASE_URL + '/api',
    publicGateway: process.env.PUBLIC_GW,
    internalGateway: process.env.INTERNAL_GW,
    seoAPIUrl: process.env.SEO_URL,
    cacheMediumTerm: '60',
    cacheLongTerm: '60',
    baseUrl: process.env.BASE_URL,
    redirectTalentUrl: process.env.REDIRECT_TALENT_URL,
    redirectProUrl: process.env.REDIRECT_PRO_URL,
    cookieDomain: process.env.COOKIE_DOMAIN,
    analyticsId: process.env.ANALYTICS_ID,
    amplitudeId: process.env.AMPLITUDE_ID,
    amplitudeConsentProvider: process.env.AMPLITUDE_CONSENT_PROVIDER,
    oneTrustId: process.env.ONETRUST_ID,
    abApiUrl: process.env.AB_API_URL,
    liveStreamEnabled: 'false',
    liveChatEnabled: 'true',
    upgradeEnabled: 'true',
    showCastingCallType: 'true',
    landingPageUrl: process.env.LANDING_PAGE_URL,
    featureManagerApiUrl: process.env.FEATURE_MANAGER_API_URL,
  },
};

// Determine if we're in development mode
const isDevelopment = process.env.NODE_ENV === 'development';

const nextConfig = {
  eslint: {
    // Warning: This allows production builds to successfully complete even if your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  reactStrictMode: true,
  webpack: (config, { isServer, dev }) => {
    if (dev) {
      const StylelintPlugin = require('stylelint-webpack-plugin');

      config.plugins.push(new StylelintPlugin());
    }

    // https://github.com/gregberge/svgr/blob/main/packages/plugin-svgo/src/config.ts#L32
    // https://github.com/gregberge/svgr/blob/6b329ac8b9f045ef56ba0020ef4d90c2f3080287/packages/plugin-svgo/src/config.ts#L32
    // svgr@8 uses svgo@3, removeViewBox option is removed by default only in svgo@4
    config.module.rules.push({
      test: /\.svg$/,
      use: [
        {
          loader: '@svgr/webpack',
          options: {
            svgoConfig: {
              plugins: [
                {
                  name: 'preset-default',
                  params: {
                    overrides: {
                      removeViewBox: false,
                    },
                  },
                },
                'prefixIds',
              ],
            },
          },
        },
      ],
    });

    if (!isServer) {
      config.resolve.fallback.fs = false;
      config.resolve.fallback.dns = false;
      config.resolve.fallback.net = false;
      config.resolve.fallback.tls = false;
    }

    return config;
  },

  redirects: async () => require('./redirects/redirects'),
  images: {
    formats: ['image/avif', 'image/webp'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'api.allcasting.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'api.ac.stg.entertech.art',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'static.allcasting.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: new URL(process.env.NEXT_PUBLIC_CDN_URL).hostname, // here need to parse only host name
        pathname: '/**',
      },
    ],
    // Configure the loader to use your custom CDN
    loader: 'custom',
    loaderFile: './loaders/imageLoader.js',
  },
  env: envConfig[process.env.NODE_ENV],
  poweredByHeader: false,
  experimental: {
    instrumentationHook: true,
  },
  async rewrites() {
    return [
      {
        source: '/.well-known/apple-developer-merchantid-domain-association',
        destination:
          '/api/apple-developer-merchantid-domain-association-stripe',
      },
      {
        source:
          '/.well-known/apple-developer-merchantid-domain-association.txt',
        destination: '/api/apple-developer-merchantid-domain-association',
      },
      {
        source: '/sitemap.xml',
        destination: '/sitemap',
      },
      {
        source: '/sitemap-:slug.xml',
        destination: '/sitemap/:slug',
      },
    ];
  },
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Vary',
            value: 'User-Agent',
          },
        ],
      },
      {
        source: '/assets/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, immutable, max-age=86400',
          },
        ],
      },
      {
        source: '/_next/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, immutable, max-age=86400',
          },
        ],
      },
      {
        source: '/:path*',
        headers: [
          {
            key: 'Document-Policy',
            value: 'js-profiling',
          },
        ],
      },
    ];
  },
  assetPrefix: isDevelopment ? '' : process.env.NEXT_PUBLIC_CDN_URL, // used for .next/static files
  sassOptions: {
    // Also make the additionalData conditional
    additionalData: `$assetUrl: '${process.env.NEXT_PUBLIC_CDN_URL}/${process.env.NEXT_PUBLIC_VERSION}';`,
  },
};

module.exports = withSentryConfig(nextConfig, {
  org: 'entertech',
  project: 'next-allcasting-com',
  authToken: process.env.SENTRY_AUTH_TOKEN,

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Automatically annotate React components to show their full name in breadcrumbs and session replay
  reactComponentAnnotation: {
    enabled: true,
  },

  // Hides source maps from generated client bundles
  hideSourceMaps: true,

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,
});
