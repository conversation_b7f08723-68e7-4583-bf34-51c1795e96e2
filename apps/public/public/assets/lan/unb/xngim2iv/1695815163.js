window.onload = function () {
  'use strict';

  // Amplitude script start
  !function(){"use strict";!function(e,t){var r=e.amplitude||{_q:[],_iq:{}};if(r.invoked)e.console&&console.error&&console.error("Amplitude snippet has been loaded.");else{var n=function(e,t){e.prototype[t]=function(){return this._q.push({name:t,args:Array.prototype.slice.call(arguments,0)}),this}},s=function(e,t,r){return function(n){e._q.push({name:t,args:Array.prototype.slice.call(r,0),resolve:n})}},o=function(e,t,r){e._q.push({name:t,args:Array.prototype.slice.call(r,0)})},i=function(e,t,r){e[t]=function(){if(r)return{promise:new Promise(s(e,t,Array.prototype.slice.call(arguments)))};o(e,t,Array.prototype.slice.call(arguments))}},a=function(e){for(var t=0;t<m.length;t++)i(e,m[t],!1);for(var r=0;r<g.length;r++)i(e,g[r],!0)};r.invoked=!0;var c=t.createElement("script");c.type="text/javascript",c.integrity="sha384-Chi7fRnlI3Vmej27YiXRbwAkES7Aor2707Qn/cpfhyw4lYue9vH/SOdlrPSFGPL/",c.crossOrigin="anonymous",c.async=!0,c.src="https://cdn.amplitude.com/libs/analytics-browser-2.3.2-min.js.gz",c.onload=function(){e.amplitude.runQueuedFunctions||console.log("[Amplitude] Error: could not load SDK")};var u=t.getElementsByTagName("script")[0];u.parentNode.insertBefore(c,u);for(var l=function(){return this._q=[],this},p=["add","append","clearAll","prepend","set","setOnce","unset","preInsert","postInsert","remove","getUserProperties"],d=0;d<p.length;d++)n(l,p[d]);r.Identify=l;for(var f=function(){return this._q=[],this},v=["getEventProperties","setProductId","setQuantity","setPrice","setRevenue","setRevenueType","setEventProperties"],y=0;y<v.length;y++)n(f,v[y]);r.Revenue=f;var m=["getDeviceId","setDeviceId","getSessionId","setSessionId","getUserId","setUserId","setOptOut","setTransport","reset","extendSession"],g=["init","add","remove","track","logEvent","identify","groupIdentify","setGroup","revenue","flush"];a(r),r.createInstance=function(e){return r._iq[e]={_q:[]},a(r._iq[e]),r._iq[e]},e.amplitude=r}}(window,document)}();
  // Amplitude script end

  // Cookie methods start
  const COOKIE_DOMAIN = '.allcasting.com';

  function setCookie(name, value, expirationDays) {
    const newDate = new Date();

    newDate.setTime(newDate.getTime() + expirationDays * 24 * 60 * 60 * 1000);

    const expires = 'expires=' + newDate.toUTCString();

    document.cookie =
      name +
      '=' +
      value +
      ';' +
      expires +
      ';path=/' +
      ';domain=' +
      COOKIE_DOMAIN +
      ';SameSite=Strict';
  }

  function getCookie(name) {
    const value = '; ' + document.cookie;
    const parts = value.split('; ' + name + '=');

    if (parts.length === 2) {
      return parts.pop().split(';').shift();
    }
  }

  // Tracking
  const urlParams = new URLSearchParams(window.location.search);

  const trackingData = {
    campaignId: urlParams.get('cid') || 1214, // Homepage campaign id
    utm: {
      source: urlParams.get('utm_source') || null,
      medium: urlParams.get('utm_medium') || null,
      campaign: urlParams.get('utm_campaign') || null,
      term: urlParams.get('utm_term') || null,
      content: urlParams.get('utm_content') || null,
      template: urlParams.get('utm_template') || null,
    },
    referrer: document.referrer.includes(location.hostname)
      ? null
      : document.referrer,
  };

  setCookie(
    'x-tracking',
    btoa(
      JSON.stringify({
        tracking: {
          cid: trackingData.campaignId,
          utm: {
            source: trackingData.utm.source,
            medium: trackingData.utm.medium,
            campaign: trackingData.utm.campaign,
            term: trackingData.utm.term,
            content: trackingData.utm.content,
          },
          referrer: trackingData.referrer,
        },
      }),
    ),
    1,
  );

  // Amplitude
  const AMPLITUDE_ID = '2f46514a1809f1cca8f9d51c67391be7';

  const getAccountLevelCookie = () => {
    const accountLevelCookie = getCookie('x-account-level');

    let value = '';

    if (accountLevelCookie) {
      value = decodeURIComponent(String(accountLevelCookie));
    }

    try {
      return JSON.parse(value);
    } catch (e) {
      return null;
    }
  };

  const userLevel = () => {
    const accountLevel = getAccountLevelCookie();

    let userLevelValue = 'guest';

    if (accountLevel) {
      switch (true) {
        case accountLevel.isBasic:
          userLevelValue = 'basic';
          break;
        case accountLevel.isDelayedSubscriptionCancel:
          userLevelValue = 'delayed_cancel';
          break;
        case accountLevel.isPaid:
          userLevelValue = 'paid';
          break;
        case accountLevel.isPortfolioOnly:
          userLevelValue = 'portfolio_only';
          break;
      }
    }

    return { user_level: userLevelValue };
  };

  const userType = () => {
    const userTypeCookie = getCookie('x-user-type');

    let userTypeValue = 'undefined';

    if (userTypeCookie) {
      userTypeValue = userTypeCookie;
    }

    return {
      user_type: userTypeValue,
    };
  };

  const getPageTheme = (width) => {
    if (width < 768) {
      return 'mobile';
    }

    if (width < 1024) {
      return 'tablet';
    }

    return 'desktop';
  };

  const pageInfo = () => {
    const { title } = document;
    const { location, innerWidth, innerHeight } = window;
    const { href, pathname, hash, search } = location;

    return {
      page_title: title,
      page_location: href,
      page_url: href.split('?')[0],
      page_path: pathname,
      page_hash: hash,
      page_search: search,
      page_width: innerWidth,
      page_height: innerHeight,
      page_theme: getPageTheme(innerWidth),
    };
  };

  const parseQueryString = (queryString = '') => {
    return Object.fromEntries(
      new URLSearchParams(
        queryString.startsWith('?') ? queryString.substring(1) : queryString,
      ),
    );
  };

  const getTrackingParams = () => {
    const queries = parseQueryString(window.location.search);

    const filteredQueries = {};

    for (const [key, value] of Object.entries(queries)) {
      const isKeyAllowed = key === 'cid' || key.startsWith('utm_');

      if (isKeyAllowed) {
        filteredQueries[key] = value;
      }
    }

    return filteredQueries;
  };

  const addProperties = () => {
    return {
      name: 'properties',
      type: 'enrichment',
      setup: async () => undefined,
      // Remove default fields
      execute: async (event) => {
        for (const key in event.event_properties) {
          if (key.startsWith('[Amplitude] Page ')) {
            delete event.event_properties[key];
          }
        }

        const accountId = getCookie('x-account');
        const userId = accountId ? String(accountId) : undefined;

        if (amplitude.getUserId() !== userId) {
          amplitude.setUserId(userId);
        }

        if (event.event_type === Amp.events.viewTalentLanding) {
          event.event_properties = {
            ...event.event_properties,
            ...userType(),
            ...userLevel(),
            ...pageInfo(),
            ...getTrackingParams(),
          };
        } else {
          event.event_properties = {
            ...event.event_properties,
            ...userType(),
            ...userLevel(),
            ...pageInfo(),
          };
        }

        event.user_properties = {
          ...event.user_properties,
        };

        return event;
      },
    };
  };

  const updateReferrer = () => {
    return {
      name: 'referrer',
      type: 'enrichment',
      setup: () => undefined,
      execute: (event) => {
        const referrer = document.referrer;
        const referringDomain = referrer ? new URL(referrer).hostname : null;

        if (event.event_type === Amp.events.viewTalentLanding) {
          if (referrer) {
            event.event_properties.referrer = referrer;
            event.event_properties.referring_domain = referringDomain;
          }
        } else if (event.event_type === '$identify') {
          event.user_properties.$set = event.user_properties.$set || {};
          const isThirdPartyReferrer = !referringDomain?.endsWith(
            'allcasting.com',
          );

          if (referrer && isThirdPartyReferrer) {
            event.user_properties.$set.referrer = referrer;
            event.user_properties.$set.referring_domain = referringDomain;
          } else {
            // first-party or missing: remove to ensure nothing is sent
            delete event.user_properties.$set.referrer;
            delete event.user_properties.$set.referring_domain;
          }
        }

        return event;
      },
    };
  };

  const Amp = {
    initialized: false,

    events: {
      viewTalentLanding: 'View Talent Landing',
      signUp: 'Sign Up',
    },

    track(name, properties = null, userId = null) {
      if (!this.initialized) {
        this.init();
      }

      if (userId && amplitude.getUserId() !== userId) {
        amplitude.setUserId(userId);
      }

      amplitude.track(name, { ...properties });
    },

    identifyUser() {
      const identifyEvent = new amplitude.Identify();
      const params = getTrackingParams();

      for (const [key, value] of Object.entries(params)) {
        identifyEvent.set(key, value);
        identifyEvent.setOnce(`initial_${key}`, value);
      }

      amplitude.identify(identifyEvent);
    },

    init() {
      if (this.initialized) {
        return;
      }

      const accountId = getCookie('x-account');
      const userId = accountId ? String(accountId) : undefined;
      const options = {
        // To make sure the event will be scheduled right away.
        flushIntervalMillis: 0,
        flushQueueSize: 1,
        defaultTracking: {
          sessions: true,
          formInteractions: false,
          fileDownloads: false,
          pageViews: false
        },
      };

      amplitude.init(AMPLITUDE_ID, userId, options);

      // Add custom user properties before any events are sent.
      this.identifyUser();

      // Enrich events with custom data.
      amplitude.add(addProperties());
      amplitude.add(updateReferrer());

      this.initialized = true;
    },
  };

  Amp.init();
  Amp.track(Amp.events.viewTalentLanding);

  // Privacy modal controls	
  var privacyModal = document.getElementById("privacy-modal");
  var privacyBtn = document.getElementById("btnPrivacyModal");
  var privacyClose = document.getElementById("privacy-close");
  var privacyScrollBtn = document.getElementById("privacyTopScroll");
  var privacyTop = document.getElementById("privacy-top");

  privacyBtn.onclick = function(e) {
    e.preventDefault();
    privacyModal.style.display = "block";
  }

  privacyClose.onclick = function() {
    privacyModal.style.display = "none";
  }

  privacyScrollBtn.onclick = function () {
    privacyTop.scrollIntoView(true);
  }
  
  // Terms modal controls	
  var termsModal = document.getElementById("terms-modal");
  var termsBtn = document.getElementById("btnTermsModal");
  var termsClose = document.getElementById("terms-close");
  var termsScrollBtn = document.getElementById("termsTopScroll");
  var termsTop = document.getElementById("terms-top");

  termsBtn.onclick = function(e) {
    e.preventDefault();
    termsModal.style.display = "block";
  }

  termsClose.onclick = function() {
    termsModal.style.display = "none";
  }

  window.onclick = function(event) {
    if (event.target == privacyModal) {
      privacyModal.style.display = "none";
    }
    if (event.target == termsModal) {
      termsModal.style.display = "none";
    }
  }

  termsScrollBtn.onclick = function () {
    termsTop.scrollIntoView(true);
  }
  
  document.querySelectorAll(".js-scroll-to-form").forEach(function (element) {
    element.addEventListener('click', (e) => {
      e.preventDefault();
      window.scroll({top: document.querySelector(".js-form-section").offsetTop, behavior: 'smooth'});
      return false;
    });
  });

  var API_URL = 'https://api.allcasting.com';
  // var API_URL = 'https://api-ac.gitlab-runner.php7.dyninno.net';
  var WEB_URL = 'https://allcasting.com';
  // var WEB_URL = 'http://localhost:4200';

  var errors = function (form, fieldNames) {
    var input;
    fieldNames.forEach(function (item) {
      input = form.querySelector('input[name="' + item + '"]');
      input.parentNode.classList.add('has-error');
    });
  };

  var showErrorMessage = function (form, message) {
    hideErrorMessage();
    var msg = document.createElement('div');
    msg.classList.add('form-error');
    msg.innerHTML = message;
    document.querySelector('.form-error-container').append(msg);
  };

  var hideErrorMessage = function () {
    document.querySelectorAll('.form-error').forEach((element) => {
      element.remove();
    })
  };

  var findEmpty = function (form) {
    var fields = [];

    ['firstname', 'lastname', 'email', 'password', 'zip'].forEach(function (name) {
      if (form[name].value.length === 0) {
        fields.push(name);
      }
    });

    ['check-1'].forEach(function (name) {
      if (form[name].checked === false) {
        fields.push(name);
      }
    });

    return fields;
  };

  var strength = function (field) {
    var indicator = document.querySelector('.js-password-strength');
    indicator.innerHTML = '';
    indicator.classList.remove(['password-strength__weak', 'password-strength__normal', 'password-strength__strong']);
    if (field.value) {
      var passwordStrength = calculatePasswordStrength(field.value);
      var strength = passwordStrengthToString(passwordStrength);

      indicator.classList.add('password-strength__' + strength);
      indicator.innerHTML = strength;
    }
  };

  var calculatePasswordStrength = function (password) {
    var WEAK_LIST = [
      '123456',
      '123456789',
      'qwerty',
      '12345678',
      '111111',
      '1234567890',
      '1234567',
      'password',
      '123123',
      '987654321',
      'qwertyuiop',
      'mynoob',
      '123321',
      '666666',
      '18atcskd2w',
      '7777777',
      '1q2w3e4r',
      '654321',
      '555555',
      '3rjs1la7qe',
      'google',
      '1q2w3e4r5t',
      '123qwe',
      'zxcvbnm',
      '1q2w3e',
      'admin',
      'root',
      'foobar'
    ];
    var force = 0;
    password = password.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, '');

    // if password in top list always weak
    for (var i = 0; i < WEAK_LIST.length; i++) {
      if (WEAK_LIST[i] === password) {
        return force;
      }
    }

    // check numbers
    if (/[0-9]+/.test(password)) {
      force += 10;
    }
    // check lower case
    if (/[a-z]+/.test(password)) {
      force += 10;
    }
    // check upper case
    if (/[A-Z]+/.test(password)) {
      force += 10;
    }
    // check symbols
    if (/[$-/:-?{-~!"^_`\[\]]/g.test(password)) {
      force += 10;
    }
    // check length
    if (password.length > 25) {
      force += 30;
    }
    if (password.length > 15) {
      force += 20;
    } else if (password.length > 7) {
      force += 10;
    } else if (password.length < 6) {
      force -= 20;
    }
    return force;
  };

  var passwordStrengthToString = function (strength) {
    if (strength >= 50) {
      return 'strong';
    }

    if (strength >= 30) {
      return 'normal';
    }

    return 'weak';
  };

  function showLoader() {
    document.querySelector('.js-loader-body').disabled = true;
    document.querySelector('.js-loader-guard').classList.add('active');
  }

  function hideLoader() {
    document.querySelector('.js-loader-body').disabled = false;
    document.querySelector('.js-loader-guard').classList.remove('active');
  }

  function getQueryParams() {
    var collectedParams = [];

    var quyeryParams = window.location.search.substr(1).split('&');
    for (var i = 0; i < quyeryParams.length; i++) {
      var values = quyeryParams[i].split('=');
      collectedParams.push({key: values[0], value: values[1]});
    }

    return collectedParams;
  }
  
  var localizedMessages = {
    'This email address belongs to an existing account.':
      `This email address belongs to an existing user. Please <a href="https://allcasting.com/login">log in</a> or click <a href="https://allcasting.com/auth/forgot-password">here</a> to restore your password`,
    'Incorrect Zip Code.' : `Please enter a valid zip/postal code`,
    'Please enter a valid email address.' : `The email address you entered is invalid. Please re-enter your email address and re-submit your information.`,
    'Your password must be at least 8 characters long and contain a letter. Please try another.' : `Your password must be at least 8 characters and contain a letter. Please try another.`
  };

  function getLocalizedMessage(key){
    return localizedMessages.hasOwnProperty(key) ? localizedMessages[key] : key;
  }
  
  function sendRegistrationGTMEvent(data) {
    try {
      dataLayer.push({
        'event': 'interaction',
        'action': 'registration',
        'target': 'success registration',
        'label': 'talent',
        // 'client.email_hash': data.email_hash,
        client: {
          'user_id': data.userId,
          'account_id_hash': data.accountIdHash,
          'campaign_id': trackingData.campaignId,
          'campaign_group': data.campaignGroup,
          'first_name': data.firstName,
          'last_name': data.lastName,
          'email':  data.email,
          'phone': data.phone,
          'postal_code': data.postalCode,
          'city': data.city,
          'region': data.region,
          'country': data.country,
          'street': data.street
        },
        tracking: {
          'cid': trackingData.campaignId,
          'utm': {
            'source':  trackingData.utm.source,
            'medium': trackingData.utm.medium,
            'campaign': trackingData.utm.campaign,
            'term': trackingData.utm.term,
            'content': trackingData.utm.content
          },
          'referrer': trackingData.referrer
        }
      });
    } catch (e) {
      console.log(e);
    }
  }

  function sendRegistrationAmplitudeEvent(data) {
    try {
      Amp.track(
        Amp.events.signUp,
        {
          type: 'talent',
          provider: 'landing',
        },
        data.accountId,
      )
    } catch (e) {
      console.log(e);
    }
  }

  function handleSuccessRegistration (data) {
    window.location = WEB_URL + '/auth/nonce?identifier=' + data.identifier + '&key=' + data.key;
  }

  var clientData;

  var register = function (form) {
    showLoader();
    var fields = new FormData(form);
    var url = new URL(API_URL + '/api/gta/auth/register');

    getQueryParams().forEach(function (param, index) {
      fields.append('_params[' + index + '][key]', param.key || '');
      fields.append('_params[' + index + '][value]', param.value || '');
    })

    const hasTrackingParams = [
      trackingData.campaignId,
      trackingData.utm.source,
      trackingData.utm.medium,
      trackingData.utm.campaign,
      trackingData.utm.term,
      trackingData.utm.content,
      trackingData.referrer,
    ].some((value) => value);

    const trackingCode = btoa(
      JSON.stringify({
        tracking: {
          'cid': trackingData.campaignId,
          'utm': {
            'source':  trackingData.utm.source,
            'medium': trackingData.utm.medium,
            'campaign': unescape(encodeURIComponent(trackingData.utm.campaign)),
            'term': trackingData.utm.term,
            'content': trackingData.utm.content
          },
          'referrer': trackingData.referrer
        }
      })
    );

    const headers = hasTrackingParams ? {
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
      'x-tracking': trackingCode
    } : {'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'};
    
    fetch(url, {
      method: 'POST',
      credentials: 'include',
      headers: headers,
      body: new URLSearchParams(fields)
    }).then(function (response){
      return response.json();
    }).then(function (data) {
      if (data.status !== 'ok') {
        if (data.fields) {
          errors(form, data.fields);
        }
        if (data.message) {
          showErrorMessage(form, getLocalizedMessage(data.message));
        }
        return;
      }

      var regData = {
        firstName: form.querySelector('input[name=firstname]').value,
        lastName: form.querySelector('input[name=lastname]').value
      };

      setCookie('registrationProfileData', JSON.stringify(regData), 1);

      const streetRandomArray = [
        'A street',
        'B street',
        'C street',
        'D street',
        'E street',
        'F street',
      ]

      clientData = {
        identifier: data.identifier,
        key: data.key,
        userId: data.uid,
        accountId: data.account_id,
        accountIdHash: data.account_id_hash,
        campaignGroup: 'Homepage',
        firstName: document.querySelector('input[name=firstname]').value,
        lastName: document.querySelector('input[name=lastname]').value,
        email:  document.querySelector('input[name=email]').value,
        phone: document.querySelector('input[name=phone]')?.value || Math.floor(Math.random() * ***********),
        postalCode: document.querySelector('input[name=zip]').value,
        street: streetRandomArray[streetRandomArray.length * Math.random() | 0]
      }

      return fetch(API_URL + '/api/gta/locations?query=' + clientData.postalCode);

    }).then(function (response){
      return response.json();
    }).then(function (data) {
      if (data.status === 'ok') {
        var location = data.items?.shift();
        if (location) {
          clientData.city = location.links.city.title;
          clientData.region = location.links.state.code;
          clientData.country = location.links.country.code;
        }
      }
      sendRegistrationGTMEvent(clientData);
      sendRegistrationAmplitudeEvent(clientData);

      setTimeout(handleSuccessRegistration(clientData), 2000);
    }).catch(() => {
      hideLoader();
    });
  };

  document.querySelectorAll('.signup_main_form').forEach(function (element) {
    var rise = function () {
      element.querySelectorAll('input').forEach(function (input) {
        if (input.type !== 'hidden' && input.value.length > 0) {
          input.classList.add('not-empty');
        }

        input.addEventListener('focusin', function () {
          hideErrorMessage();
        }, false);
      });
    }

    setTimeout(rise, 100);
    
    element.addEventListener('change', function (event) {
      var input = event.target;
      input.parentNode.classList.remove('has-error');
    }, false);
    
    element.addEventListener('focusout', function (event) {
      var input = event.target;

      if (input.value && input.value.length !== 0) {
        input.classList.add('not-empty');
      } else {
        input.classList.remove('not-empty');
      }

    }, false);
    
    element.addEventListener('submit', function (event) {

      event.preventDefault();
      var fields = findEmpty(this);

      if (fields.length >= 2) {
        errors(this, fields);
        showErrorMessage(this, 'Please complete all required fields');
        return;
      } else if (fields.length !== 0) {
        errors(this, fields);
        hideErrorMessage();
        return;
      }

      register(element);
    }, false);
  });

  var passwordField = document.querySelector('.signup_main_form input[name="password"]');

  passwordField.addEventListener('keyup', function (event) {
    strength(event.target);
  });
  
  document.querySelector('.js-show-password').addEventListener('click', function (event) {
    this.classList.toggle('show');
    var password = document.querySelector('input[name=password]');
    if (password.getAttribute('type') === 'password') {
      password.setAttribute('type', 'text');
    } else {
      password.setAttribute('type', 'password');
    }
  });

  // - tracking pixel
  function guid() {
    function s4() {
      return Math.floor((1 + Math.random()) * 0x10000)
        .toString(16)
        .substring(1);
    }

    return s4() + s4() + '-' + s4() + '-' + s4() + '-' + s4() + '-' + s4() + s4() + s4();
  }

  const googleAnalyticsCookie = getCookie('_ga');

  let analyticsCookie = getCookie('_ac');

  if (!analyticsCookie) {
    analyticsCookie = guid();

    setCookie('_ac=', analyticsCookie, 30);
  }

  const url = new URL(API_URL + '/api/gta/pixel/view');

  const data = {
    url: location.href,
    _ga: googleAnalyticsCookie,
    _ac: analyticsCookie
  }

  url.search = new URLSearchParams(data).toString();
  fetch(url);
};
