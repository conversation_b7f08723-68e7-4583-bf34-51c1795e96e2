import { NextResponse } from 'next/server';
import dayjs from 'dayjs';
import {
  authGuard,
  paidAccountGuard,
  proAllowedPaths,
} from './redirects/clientSide';
import {
  extractExpiresFromAuthenticationToken,
  extractAccountIdFromToken,
  callbackAccount,
  responseExtractType,
  responseExtractProfileId,
  responseExtractProfiles,
  responseAccountLevel,
} from './utils/authHelper';
import { CookieServiceForMiddleware } from './services/cookieServiceForMiddleware';
import * as Sentry from '@sentry/nextjs';
import { getUpgradeValue } from './utils/formatAccountLevel';
import {
  getIsFullProProfile,
  getIsFullTalentProfile,
} from './utils/isFullProfile';

export async function middleware(request) {
  if (request.nextUrl.pathname.match(/.*(_next|assets|api).*/)) {
    return NextResponse.next();
  }

  if (
    request.nextUrl.pathname.match(
      /.*(nonce|perpetual|stripe-billing-portal).*/,
    )
  ) {
    return CookieServiceForMiddleware.cleanAuthenticationCookies(
      NextResponse.next(),
    );
  }

  const nextPath = request.nextUrl.pathname;
  const queryParams = request.nextUrl.search;
  const redirectUrl = request.nextUrl.origin;
  const proRedirectUrl = process.env.redirectProUrl;
  const talentRedirectUrl = process.env.redirectTalentUrl;
  const baseUrl = process.env.baseUrl;

  const authenticationToken =
    CookieServiceForMiddleware.getAuthenticationCookie(request.cookies);
  const volatileToken = CookieServiceForMiddleware.getVolatileCookie(
    request.cookies,
  );

  if (authenticationToken && volatileToken) {
    const insuredCookies = await insureRequiredCookies(
      request,
      authenticationToken,
      volatileToken,
      request.cookies,
      nextPath,
    );

    if (!insuredCookies) {
      return CookieServiceForMiddleware.cleanAuthenticationCookies(
        NextResponse.redirect(`${baseUrl}`, 307),
      );
    }
  }

  const tokenExpiration = Number(
    extractExpiresFromAuthenticationToken(authenticationToken),
  );
  const userProfilesCookie = CookieServiceForMiddleware.getUserProfilesCookie(
    request.cookies,
  );
  const isTalent =
    CookieServiceForMiddleware.getUserTypeCookie(request.cookies) === 'talent';
  const accountLevelCookie = CookieServiceForMiddleware.getAccountLevelCookie(
    request.cookies,
  );
  const isPaidOrDelayed = accountLevelCookie?.isPaidOrDelayed;
  const currentEpoch = dayjs().unix();
  const isAuthenticated =
    authenticationToken && volatileToken && tokenExpiration > currentEpoch;
  const { firstName, lastName, birthday, gender, zipCode, isEmailValid, city } =
    userProfilesCookie?.[0] || {};

  const isFullTalentProfile = getIsFullTalentProfile(
    firstName,
    lastName,
    birthday,
    gender,
    zipCode,
    isEmailValid,
  );

  if (
    isAuthenticated &&
    !isTalent &&
    !getIsFullProProfile(firstName, lastName, isEmailValid)
  ) {
    return CookieServiceForMiddleware.passCookiesFromRequestToResponse(
      request,
      NextResponse.redirect(`${proRedirectUrl}/missing-info`, 307),
    );
  }

  if (
    isAuthenticated &&
    !isTalent &&
    !proAllowedPaths.some((path) => nextPath.includes(path))
  ) {
    if (
      ['/castingcalls', '/talent', '/reviews'].some((url) =>
        nextPath.includes(url),
      )
    ) {
      return CookieServiceForMiddleware.passCookiesFromRequestToResponse(
        request,
        NextResponse.redirect(`${proRedirectUrl}${nextPath}`, 308),
      );
    }

    return CookieServiceForMiddleware.passCookiesFromRequestToResponse(
      request,
      NextResponse.redirect(`${proRedirectUrl}`, 307),
    );
  }

  if (isAuthenticated && isTalent && !isFullTalentProfile) {
    return CookieServiceForMiddleware.passCookiesFromRequestToResponse(
      request,
      NextResponse.redirect(`${talentRedirectUrl}/wizard${queryParams}`, 307),
    );
  }

  if (
    [
      isAuthenticated,
      isTalent,
      isFullTalentProfile,
      nextPath === '/castingcalls' || authGuard.includes(nextPath),
      !request.nextUrl.search,
      city,
    ].every((value) => value)
  ) {
    return CookieServiceForMiddleware.passCookiesFromRequestToResponse(
      request,
      NextResponse.redirect(`${redirectUrl}/castingcalls?city=${city}`, 307),
    );
  }

  if (isAuthenticated && authGuard.includes(nextPath)) {
    return CookieServiceForMiddleware.passCookiesFromRequestToResponse(
      request,
      NextResponse.redirect(`${redirectUrl}/castingcalls${queryParams}`, 307),
    );
  }

  if (
    isAuthenticated &&
    isTalent &&
    !isPaidOrDelayed &&
    nextPath === '/onboarding'
  ) {
    return CookieServiceForMiddleware.passCookiesFromRequestToResponse(
      request,
      NextResponse.redirect(`${talentRedirectUrl}/onboarding`, 307),
    );
  }

  if (isPaidOrDelayed && paidAccountGuard.includes(nextPath)) {
    return NextResponse.redirect(`${baseUrl}`, 307);
  }

  let response;

  if (
    request.nextUrl.pathname === request.nextUrl.pathname.toLocaleLowerCase()
  ) {
    response = NextResponse.next();
  } else {
    response = NextResponse.redirect(
      `${
        request.nextUrl.origin
      }${request.nextUrl.pathname.toLocaleLowerCase()}${
        request.nextUrl.search
      }`,
      308,
    );
  }

  if (!isAuthenticated) {
    CookieServiceForMiddleware.cleanAuthenticationCookies(response);
  } else {
    CookieServiceForMiddleware.passCookiesFromRequestToResponse(
      request,
      response,
    );
  }

  // Failsafe for legacy paths
  // ToDo: remove after migration
  if (['/news', '/academy'].some((url) => nextPath.startsWith(url))) {
    return CookieServiceForMiddleware.passCookiesFromRequestToResponse(
      request,
      NextResponse.redirect(`${baseUrl}/blog`, 307),
    );
  }

  return response;
}

const insureRequiredCookies = async (
  req,
  authenticationToken,
  volatileToken,
  cookies,
  path,
) => {
  const cookiesToCheck = [
    CookieServiceForMiddleware.getAuthenticationCookie(req.cookies),
    CookieServiceForMiddleware.getVolatileCookie(req.cookies),
    CookieServiceForMiddleware.getExpiresCookie(req.cookies),
    CookieServiceForMiddleware.getAccountCookie(req.cookies),
    CookieServiceForMiddleware.getUserTypeCookie(req.cookies),
    CookieServiceForMiddleware.getProfileCookie(req.cookies),
    CookieServiceForMiddleware.getUserProfilesCookie(req.cookies),
    CookieServiceForMiddleware.getAccountLevelCookie(req.cookies),
  ];

  let allGood = true;

  cookiesToCheck.forEach((val) => {
    if (!val) {
      allGood = false;
    }
  });

  if (!allGood) {
    const accountId = extractAccountIdFromToken(authenticationToken);

    const accountResponse = await callbackAccount(
      accountId,
      authenticationToken,
      volatileToken,
      req,
      path,
    );

    if (accountResponse.status === 'error') {
      console.error({
        message: 'Account information fetch error',
        response: accountResponse,
        data: {
          accountId,
          authenticationToken,
          volatileToken,
        },
      });

      return null;
    }

    const type = responseExtractType(accountResponse);

    Sentry.setUser({ id: accountId, type });

    CookieServiceForMiddleware.setAuthenticationCookie(
      authenticationToken,
      cookies,
    );
    CookieServiceForMiddleware.setVolatileCookie(volatileToken, cookies);
    CookieServiceForMiddleware.setExpiresCookie(
      extractExpiresFromAuthenticationToken(authenticationToken),
      cookies,
    );
    CookieServiceForMiddleware.setAccountCookie(accountId, cookies);
    CookieServiceForMiddleware.setUserTypeCookie(type, cookies);
    CookieServiceForMiddleware.setProfileCookie(
      responseExtractProfileId(accountResponse),
      cookies,
    );
    CookieServiceForMiddleware.setUserProfilesCookie(
      responseExtractProfiles(accountResponse),
      cookies,
    );
    CookieServiceForMiddleware.setAccountLevelCookie(
      responseAccountLevel(accountResponse),
      cookies,
    );
    CookieServiceForMiddleware.setUpgradeCookie(
      getUpgradeValue(accountResponse.links?.account_level),
      cookies,
    );
  }

  return true;
};
