import styles from './CastingCallList.module.scss';
import React, { Fragment, memo } from 'react';
import { CastingCall } from '../index';
import cn from 'classnames';
import CallToActionBlock from '../CallToActionBlock/CallToActionBlock';
import { TYPE } from '../../constants/castingCalls';
import { useAuth } from '../../contexts/AuthContext';
import { Amp } from '../../services/amp';

function CastingCallList({ items = [], mode = '', related = false }) {
  const { accountLevel } = useAuth();

  return (
    <div
      className={cn(styles.related, styles[mode], related ? styles.short : '')}
    >
      {items.map((item, index) => (
        <Fragment key={index}>
          {!accountLevel?.isPaidOrDelayed && (index === 8 || index === 18) && (
            <CallToActionBlock
              list
              listGrid={mode === 'grid'}
              position={`${Amp.element.position.list}: ${index}`}
            />
          )}
          <CastingCall
            mode={mode}
            type={item.type}
            mainCategory={item.category}
            additionalCategories={item.additional_categories}
            location={item.location}
            title={item.title}
            description={!related ? item.description.slice(0, 300) : ''}
            rolesCount={item.roles.length}
            expires={item.expiration_date || item.expiration}
            isOnline={item.online_audition}
            isEasyToApply={item.type === TYPE.Web}
            hot={item.hot}
            id={item.id}
            viewed={item.viewed}
            applied={item.applied}
            paymentAmount={item.payment_amount}
            paymentPeriod={item.payment_period}
            paymentCurrency={item.payment_currency}
          />
        </Fragment>
      ))}
    </div>
  );
}

export default memo(CastingCallList);
