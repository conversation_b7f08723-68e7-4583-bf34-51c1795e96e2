import React, { memo } from 'react';
import styles from './FilterMobileHeader.module.scss';
import cn from 'classnames';
import IconArrow from '/public/assets/icons/icon-angle-top.svg';
import IconFilter from '/public/assets/icons/icon-filter.svg';
import { useAuth } from '../../contexts/AuthContext';
import { useSale } from '../../contexts/SaleContext';

const FilterMobileHeader = ({
  isFilterFormOpen,
  onMobileFilterToggle,
  showReset,
  resetCallback,
}) => {
  const { accountLevel } = useAuth();
  const { showSale } = useSale();

  return (
    <section
      className={cn(styles.header, {
        [styles['opened-filter-section']]: isFilterFormOpen,
        [styles.sale]: showSale,
        [styles['upgrade-header-visible']]:
          !showSale &&
          accountLevel &&
          (!accountLevel?.isPaidOrDelayed ||
            !!accountLevel?.canUpgradeExistingSubscription),
      })}
    >
      <div
        className={cn(styles.container, {
          [styles['opened-filter']]: isFilterFormOpen,
        })}
      >
        <div className={styles.link} onClick={onMobileFilterToggle}>
          <IconFilter className={styles['filter-icon']} alt="filter-icon" />
          Filter results
          <IconArrow
            className={cn([styles['arrow-icon']], {
              [styles.open]: isFilterFormOpen,
            })}
            alt="arrow-icon"
          />
        </div>
        {showReset && (
          <span className={styles.reset} onClick={resetCallback}>
            Reset filters
          </span>
        )}
      </div>
    </section>
  );
};

export default memo(FilterMobileHeader);
