import React, { memo } from 'react';
import styles from './Filter.module.scss';
import { Button, Input } from '../../components';
import {
  Autocomplete,
  Box,
  FormControl,
  FormControlLabel,
  FormGroup,
  Switch,
  TextField,
} from '@mui/material';
import classnames from 'classnames';
import Location from './Location';
import { useAnalytics } from 'use-analytics';
import { GTM_EVENTS, GTM_ACTIONS } from '../../constants/analytics';
import { Amp } from '../../services/amp';

const Filter = ({
  type = 'default',
  data = {},
  onFilterChange = () => {},
  onFilterSubmit = () => {},
  cities,
}) => {
  const { track } = useAnalytics();

  const handleFilterSubmit = () => {
    track(GTM_EVENTS.interaction, {
      target: type,
      action: GTM_ACTIONS.filters,
      label: 'search',
    });
    onFilterSubmit();
  };

  const getScope = () => {
    switch (type) {
      case 'talent':
        return Amp.element.scope.talentSearchPage;
      case 'castingcalls':
        return Amp.element.scope.castingCallListPage;
      default:
        return Amp.element.scope.global;
    }
  };

  const handleSubmitButtonClick = () => {
    Amp.track(Amp.events.elementClicked, {
      name: `submit button clicked`,
      scope: getScope(),
      section: Amp.element.section.filter,
      type: Amp.element.type.button,
    });

    handleFilterSubmit();
  };

  const handleApplyButtonClick = () => {
    Amp.track(Amp.events.elementClicked, {
      name: `apply button clicked`,
      scope: getScope(),
      section: Amp.element.section.filter,
      type: Amp.element.type.button,
    });

    handleFilterSubmit();
  };

  const handleFieldClick = (name, type) => {
    Amp.track(Amp.events.elementClicked, {
      name: `'${name}' field clicked`,
      scope: getScope(),
      section: Amp.element.section.filter,
      type: type,
    });
  };

  const handleKeyDown = (event) => {
    Amp.track(Amp.events.elementClicked, {
      name: `submit on enter`,
      scope: getScope(),
      section: Amp.element.section.filter,
      type: Amp.element.type.enterKey,
    });

    if (event.key === 'Enter') {
      handleFilterSubmit();
    }
  };

  const renderWidget = (widget) => {
    const {
      type,
      subtype,
      title,
      slug,
      label,
      options,
      value,
      selected,
      auto_submit,
    } = widget;

    switch (type) {
      case 'input':
        return (
          <Input
            type={'text'}
            placeholder={title}
            name={slug}
            value={String(value)}
            onClick={() => {
              handleFieldClick(slug, Amp.element.type.input);
            }}
            onChange={onFilterChange}
            onKeyDown={auto_submit ? handleKeyDown : () => {}}
          />
        );
      case 'location':
        return (
          <Location
            cities={cities}
            widget={widget}
            callback={onFilterChange}
            onClick={() => {
              handleFieldClick(slug, Amp.element.type.input);
            }}
          />
        );
      case 'select':
        let val = null;

        options.map((option) => {
          if (String(selected) === String(option.value)) {
            val = option;
          }
        });

        return (
          <FormControl className={styles.control} variant="standard">
            <Autocomplete
              options={options}
              disableClearable={subtype === 'disableClearable'}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label={title}
                  variant="standard"
                  onClick={() => {
                    handleFieldClick(slug, Amp.element.type.select);
                  }}
                />
              )}
              renderOption={(props, option) => {
                const { key, ...restProps } = props;

                return (
                  <Box
                    component="li"
                    sx={{ '& > img': { mr: 2, flexShrink: 0 } }}
                    key={key}
                    {...restProps}
                  >
                    {option.label}
                  </Box>
                );
              }}
              onChange={(event, option) => {
                onFilterChange({
                  target: {
                    name: slug,
                    value: option ? option.value : null,
                  },
                });
              }}
              name={slug}
              label={label}
              value={val}
            />
          </FormControl>
        );
      case 'checkbox':
        return (
          <FormGroup className={styles.select} onChange={onFilterChange}>
            {options.map((item, key) => (
              <FormControlLabel
                key={key}
                sx={{
                  '& .Mui-checked+.MuiSwitch-track': {
                    border: 'none !important',
                    backgroundColor: '#55d77a !important',
                    opacity: '1 !important',
                    boxShadow: 'none',
                  },
                  '& .MuiSwitch-root': {
                    order: 2,
                    marginLeft: 'auto',
                    marginRight: '-10px',
                  },
                  '& .MuiTypography-root': {
                    order: 1,
                  },
                }}
                value={item.value}
                name={slug}
                control={
                  <Switch
                    onClick={() => {
                      handleFieldClick(slug, Amp.element.type.checkbox);
                    }}
                    sx={{
                      '& .Mui-checked': {
                        '& .MuiSwitch-thumb': {
                          boxShadow: 'inset 0 0 0 2px #55d77a',
                          backgroundColor: '#fff',
                        },
                      },
                      '& .MuiSwitch-track': {
                        backgroundColor: '#fff',
                        boxShadow: 'inset 0 0 0 2px #bbb',
                        opacity: '1',
                      },
                      '& .MuiSwitch-thumb': {
                        height: '24px',
                        width: '24px',
                        position: 'relative',
                        top: '-2px',
                        backgroundColor: '#fff',
                        boxShadow: 'inset 0 0 0 2px #bbb',
                      },
                    }}
                  />
                }
                label={item.label}
                checked={item.checked}
                className={styles.checkbox}
              />
            ))}
          </FormGroup>
        );
      default:
        return <span>&nbsp;</span>;
    }
  };

  return (
    <>
      {data.sections &&
        data.sections.map((section, sectionKey) => (
          <div
            key={sectionKey}
            className={section.title === '' ? styles.hidden : ''}
            data-cy="filter-section"
          >
            <span className={styles.title}>
              {section.title}
              <span className={styles.apply} onClick={handleApplyButtonClick}>
                apply
              </span>
            </span>
            <span className={styles.section}>
              {section.widgets.map((widget, widgetKey) => (
                <div
                  key={widgetKey}
                  className={classnames(styles.part, styles[widget.class])}
                >
                  {renderWidget(widget)}
                </div>
              ))}
            </span>
          </div>
        ))}
      <div className={styles['submit-button-container']}>
        <Button
          label={'Search'}
          minWidth={'100%'}
          onClick={handleSubmitButtonClick}
        />
      </div>
    </>
  );
};

export default memo(Filter);
