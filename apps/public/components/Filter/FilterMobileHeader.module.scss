@import '../../styles/variables';
@import '../../styles/mixins';

.header {
  display: block;
  position: sticky;
  z-index: 3;
  top: 0;
  bottom: auto;
  margin: 0 (-$space-20);

  @include desktop {
    display: none;
  }

  &.opened-filter-section {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    margin: 0;
  }

  &.upgrade-header-visible {
    top: 65px;
  }
}

.container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-self: auto;
  max-width: $content-max-width;
  padding: $space-10 $space-15;
  margin-left: auto;
  margin-right: auto;
  min-height: 45px;
  background-color: $violet-100;
}

.link {
  color: $blue-100;
  font-weight: 400;
  cursor: pointer;
  display: flex;
  gap: $space-10;
  align-items: center;
}

.reset {
  margin-left: auto;
  color: $red-60;
  cursor: pointer;
}

.opened-filter {
  background-color: $white;
  border-bottom: 1px solid rgba(0, 0, 0, 30%);
}

.arrow-icon {
  width: 12px;
  height: 12px;
  transform: rotate(180deg);

  &.open {
    transform: rotate(0deg);
  }
}

.filter-icon {
  width: 16px;
  height: 16px;
}
