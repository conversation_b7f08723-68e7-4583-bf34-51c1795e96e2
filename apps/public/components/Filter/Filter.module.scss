@import '../../styles/variables';
@import '../../styles/mixins';

.title {
  padding: 3px $space-20;
  background: $grey-10;
  font-size: 14px;
  line-height: 30px;
  color: $grey-100;
  text-transform: uppercase;
  display: flex;
  align-items: center;
  font-weight: 300;
  margin-right: 0;
  margin-left: 0;
  position: relative;

  .apply {
    text-transform: lowercase;
    position: absolute;
    right: 15px;
    opacity: 0.6;
    background: #dedede;
    border-radius: 8px;
    padding: 8px $space-10;
    height: 20px;
    line-height: 0;

    &:hover {
      opacity: 1;
      cursor: pointer;
    }
  }
}

.checkbox {
  display: flex;
  margin-right: 0;
}

.checkbox span {
  font-weight: 200;
}

.control {
  width: 100%;
}

.control label {
  font-size: 16px;
  color: #777;
  font-weight: 300;
}

.section {
  display: block;
  padding: 20px;
}

.part {
  width: 100%;
  margin-bottom: $space-20;
}

.submit-button-container {
  position: sticky;
  bottom: 0;
  padding: $space-20 $space-15;
  background-color: $white;
  box-shadow: $shadow-box-dark-wide;

  @include desktop {
    position: relative;
    padding: 0;
    background-color: transparent;
    box-shadow: none;
  }
}

.left {
  width: 45%;
  display: inline-block;
}

.right {
  float: right;
  width: 45%;
  display: inline-block;
}

.select {
  margin-left: $space-10;
}

.hidden {
  display: none;
}
