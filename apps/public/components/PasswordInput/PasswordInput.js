import styles from './PasswordInput.module.scss';
import cn from 'classnames';
import { Input } from '../index';
import { memo, useState } from 'react';

const PasswordInput = ({
  name,
  onChange,
  onBlur,
  value,
  isTouched,
  error,
  placeholder = 'Password',
  hint,
  styleTheme = 'light',
  dataCy,
}) => {
  const [showPassword, setShowPassword] = useState(false);

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div
      className={cn(styles['password-field'], styles[`theme-${styleTheme}`])}
    >
      <div className={styles['password-input-container']}>
        <Input
          className={cn([styles.input], {
            [styles['input-error']]: error,
          })}
          name={name}
          placeholder={placeholder}
          onChange={onChange}
          onBlur={onBlur}
          value={value}
          isTouched={isTouched}
          type={showPassword ? 'text' : 'password'}
          hint={hint}
          error={error}
          styleTheme={styleTheme}
          dataCy={dataCy}
          autoComplete="new-password"
        />
      </div>
      <div className={styles['password-field-image']}>
        <div
          onClick={toggleShowPassword}
          className={cn(styles['password-icon-container'], {
            [styles['show-password']]: showPassword,
          })}
        ></div>
      </div>
    </div>
  );
};

export default memo(PasswordInput);
