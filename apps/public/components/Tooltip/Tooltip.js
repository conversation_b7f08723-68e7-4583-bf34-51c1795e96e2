import { memo, useState } from 'react';
import Image from 'next/image';
import styles from './Tooltip.module.scss';
import { ArrowContainer, Popover } from 'react-tiny-popover';
import cn from 'classnames';

const Tooltip = ({
  children, // Tooltip content
  clickable = false, // Allow interaction with elements inside the tooltip
  openOnHover = false, // Set false to open on trigger click
  positions = ['right', 'top', 'bottom', 'left'], // Preferred positions by priority
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const closeTooltip = () => {
    setIsOpen(false);
  };

  const openTooltip = () => {
    setIsOpen(true);
  };

  const onContentClick = () => {
    if (!clickable) {
      closeTooltip();
    }
  };

  const onMouseEnter = () => {
    if (openOnHover) {
      openTooltip();
    }
  };

  const onMouseLeave = () => {
    if (openOnHover) {
      closeTooltip();
    }
  };

  return (
    <Popover
      containerClassName={styles['tooltip']}
      isOpen={isOpen}
      positions={positions}
      content={({ position, childRect, popoverRect }) => (
        <ArrowContainer
          position={position}
          childRect={childRect}
          popoverRect={popoverRect}
          arrowColor={'white'}
          arrowSize={10}
          className={styles['tooltip-arrow-container']}
          arrowClassName={styles['tooltip-arrow']}
        >
          <div
            onClick={onContentClick}
            className={cn(styles['tooltip-content'], {
              [styles.clickable]: clickable,
            })}
          >
            {children}
          </div>
        </ArrowContainer>
      )}
      onClickOutside={closeTooltip}
    >
      <Image
        className={styles['tooltip-trigger']}
        src="/assets/icons/icon-hint.svg"
        alt="icon"
        width={15}
        height={15}
        onClick={openTooltip}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      />
    </Popover>
  );
};

export default memo(Tooltip);
