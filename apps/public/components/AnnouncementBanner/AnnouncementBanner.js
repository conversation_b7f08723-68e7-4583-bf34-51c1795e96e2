import { memo } from 'react';
import styles from './AnnouncementBanner.module.scss';

const AnnouncementBanner = ({ announcement }) => {
  return (
    <a
      href={announcement.link}
      className={styles['article-banner-link']}
      target="_blank"
      rel="noreferrer"
    >
      <picture>
        <source
          srcSet={announcement.desktopImageHref}
          media="(min-width: 620px)"
        />
        <img
          className={styles['article-banner-image']}
          src={announcement.mobileImageHref}
          alt="article-banner-image"
        />
      </picture>
    </a>
  );
};

export default memo(AnnouncementBanner);
