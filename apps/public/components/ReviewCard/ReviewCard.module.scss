@import '../../styles/variables';

.card {
  display: flex;
  width: 100%;
  flex-flow: column nowrap;
  box-shadow: $shadow-form-control;
  border-radius: 10px;
  background-color: $white;
  height: fit-content;
}

.full-height {
  height: auto;
}

.card-header {
  position: relative;
}

.card-image-container {
  display: block;
  border-radius: 50%;
  margin: (-$space-15) auto 0;
  border: 1px solid $grey-60;
  position: relative;
  width: 87px;
  height: 87px;
  padding: $space-5;
}

.card-image {
  position: relative;
  height: 100%;
  background-color: $grey-60;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.card-icon-container {
  position: absolute;
  top: 20px;
  right: 20px;
  height: 40px;
  filter: invert(99%) sepia(3%) saturate(657%) hue-rotate(238deg)
    brightness(115%) contrast(80%);
}

.card-content {
  padding: $space-25 $space-30;
  display: flex;
  flex-direction: column;
  gap: $space-10;
}

.card-title {
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
}

.card-sub-title {
  font-size: 14px;
  color: $grey-80;
  text-transform: capitalize;
  margin-left: $space-5;
}

.card-date {
  font-weight: 300;
  display: flex;
  gap: 3px;
  align-items: center;
}

.card-rating div img {
  margin: 0;
}

.card-details {
  border-bottom: 1px solid $grey-20;
  padding-bottom: $space-15;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.unclickable {
  cursor: default;
}

.link {
  color: $blue-100;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}
