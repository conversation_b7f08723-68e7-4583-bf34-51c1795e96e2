import { memo, useState } from 'react';
import styles from './ReviewCard.module.scss';
import Image from 'next/image';
import CalendarIcon from '../../public/assets/icons/icon-calendar-1.svg';
import dayjs from 'dayjs';
import { StarRating } from '../index';
import cn from 'classnames';

const ReviewCard = ({
  title,
  subTitle,
  date,
  description,
  imageSrc,
  rating,
  openProfilePopup,
  fullHeight = false,
  textLimit = 10000,
  isClickable = false,
}) => {
  const [limit, setLimit] = useState(textLimit);

  const onExpandDescription = () => {
    setLimit(10000);
  };

  return (
    <div
      className={cn(styles.card, {
        [styles['full-height']]: fullHeight,
      })}
      data-cy="review-card"
    >
      <div className={styles['card-header']}>
        <div className={styles['card-image-container']}>
          {isClickable ? (
            <div
              onClick={openProfilePopup}
              className={styles['card-image']}
              style={{
                backgroundImage: `url('${imageSrc}')`,
              }}
            />
          ) : (
            <div
              className={cn(styles['card-image'], styles.unclickable)}
              style={{
                backgroundImage: `url('${imageSrc}')`,
              }}
            />
          )}
        </div>
        <div className={styles['card-icon-container']}>
          <Image
            src={'/assets/icons/icon-quote-1.svg'}
            height={40}
            width={40}
            alt="quote icon"
          />
        </div>
      </div>
      <div className={styles['card-content']}>
        <div className={styles['card-title-container']}>
          {isClickable ? (
            <span className={styles['card-title']} onClick={openProfilePopup}>
              {title}
            </span>
          ) : (
            <span className={cn(styles['card-title'], styles.unclickable)}>
              {title}
            </span>
          )}
          <span>, </span>
          <small className={styles['card-sub-title']}>{subTitle}</small>
        </div>
        <div className={styles['card-details']}>
          <div className={styles['card-date']}>
            <CalendarIcon />
            <span suppressHydrationWarning>
              {dayjs(date).format('M/D/YYYY')}
            </span>
          </div>
          <div className={styles['card-rating']}>
            <StarRating initialRating={rating} />
          </div>
        </div>
        <div className={styles['card-description']}>
          {description.length > limit ? (
            <>
              {`${description.slice(0, limit)}...`}
              &nbsp;
              <div className={styles.link} onClick={onExpandDescription}>
                view more
              </div>
            </>
          ) : (
            description
          )}
        </div>
      </div>
    </div>
  );
};

export default memo(ReviewCard);
