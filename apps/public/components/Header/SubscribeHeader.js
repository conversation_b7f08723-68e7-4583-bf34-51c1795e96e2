import { memo } from 'react';
import styles from './SubscribeHeader.module.scss';
import { Button } from '../index';
import { Amp } from '../../services/amp';

const SubscribeHeader = ({ canUpgradeExistingSubscription }) => {
  const navigateToUpgradePage = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'subscribe',
      scope: Amp.element.scope.global,
      section: Amp.element.section.header,
      type: Amp.element.type.button,
      upgrade_to_12: !!canUpgradeExistingSubscription,
    });

    window.location.href = `${process.env.redirectTalentUrl}/upgrade${
      canUpgradeExistingSubscription ? '-12' : ''
    }`;
  };

  return (
    <div className={styles['upgrade-header']}>
      <Button
        onClick={navigateToUpgradePage}
        label={
          canUpgradeExistingSubscription ? 'Upgrade your plan' : 'Subscribe now'
        }
        minWidth="220px"
        color="green-gradient"
        shadow={false}
        className={styles['subscribe-btn-animation-highlight']}
      />
    </div>
  );
};

export default memo(SubscribeHeader);
