import { memo } from 'react';
import styles from './SaleMobileHeader.module.scss';
import { Button, Countdown } from '../index';
import Link from 'next/link';

const SaleMobileHeader = ({
  saleExpirationTime,
  onStopSale,
  loweredOpacity,
  loweredFontWeight,
}) => {
  return (
    <div className={styles['sale-header']}>
      <Countdown
        onCountdownEnd={onStopSale}
        expirationTime={saleExpirationTime}
        loweredOpacity={loweredOpacity}
        loweredFontWeight={loweredFontWeight}
      />
      <div className={styles['sale-header-button']}>
        <Link href={`${process.env.redirectTalentUrl}/upgrade`}>
          <Button
            label="LIMITED OFFER"
            kind="primary"
            color="green-gradient"
            minWidth={'200px'}
            shadow={false}
          />
        </Link>
      </div>
    </div>
  );
};

export default memo(SaleMobileHeader);
