@import '../../styles/variables';
@import '../../styles/mixins';
@import '../../styles/button-animations';

.header-container {
  display: none;
  position: sticky;
  z-index: 3;
  top: 0;
  bottom: auto;
  box-shadow: $shadow-card-container;
  background-color: $white;

  @include desktop {
    display: block;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-self: auto;
  max-width: $content-max-width;
  margin-left: auto;
  margin-right: auto;

  @include desktop {
    padding: 0 $space-60;
  }

  .logo-link {
    line-height: 1;
  }

  .navbar {
    display: flex;
    align-items: center;

    .navbar-link {
      position: relative;
      margin-left: 3px;
      margin-right: 3px;
      padding: 23px 12px;
      color: $black;
      font-weight: 300;

      h1 {
        margin: 0;
        font-weight: 300;
        font-size: 16px;
      }

      &::before {
        content: '';
        position: absolute;
        left: 50%;
        bottom: 0;
        width: 0;
        height: 4px;
        opacity: 0.5;
        background-color: $violet-100;
        background-image: $gradient-port-gore;
        transform: translate(-50%);
        transition: all 0.3s ease-in-out;
      }

      &:hover,
      &.active {
        text-decoration: none;

        &::before {
          width: calc(100% - 5px);
          opacity: 0.5;
        }
      }

      &.active::before {
        opacity: 1;
      }
    }

    .navbar-link:first-of-type {
      margin-left: 0;
    }

    .link {
      color: $blue-100;
      font-size: 16px;
      font-weight: 400;
      cursor: pointer;
      border-bottom: 1.5px solid transparent;
      line-height: 1;

      &:hover {
        border-bottom: 1.5px solid $blue-100;
      }
    }
  }

  .divider {
    width: 3px;
    height: 3px;
    background-color: $black;
    margin-left: $space-20;
    margin-right: $space-30;
  }

  .sign-up-button {
    color: $green-60;
    margin-left: $space-20;
    padding-top: $space-10;
    padding-bottom: $space-10;
    font-size: 14px;

    &:hover:enabled {
      padding-top: 8px;
      padding-bottom: 8px;
    }
  }

  @include xlarge {
    .navbar .navbar-link {
      margin-left: $space-10;
      margin-right: $space-10;
    }
  }
}

.header-actions {
  display: flex;
  gap: $space-10;
  align-items: center;
}
