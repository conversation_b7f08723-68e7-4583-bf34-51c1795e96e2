'use client';

import React, { memo } from 'react';
import styles from './Header.module.scss';
import cn from 'classnames';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Amp } from '@services/amp';

<<<<<<< HEAD:apps/public/components/Header/MenuItem.tsx
type MenuItemProps = {
  label: string;
  href: string;
  aliasHref?: string;
};

const MenuItem = ({ label, href, aliasHref }: MenuItemProps) => {
  const router = useRouter();
=======
const MenuItem = ({ label, href, aliasHref }) => {
  const path = usePathname();

>>>>>>> 67bff04e (feat: app migration):apps/public/components/Header/MenuItem.js
  const onMenuItemClick = () => {
    Amp.track(Amp.events.elementClicked, {
      name: label,
      section: Amp.element.section.navigation,
      type: Amp.element.type.link,
    });
  };

  return (
    <Link
      href={href}
      onClick={onMenuItemClick}
      className={cn(styles['navbar-link'], {
<<<<<<< HEAD:apps/public/components/Header/MenuItem.tsx
        [styles.active]:
          router.asPath.startsWith(href) ||
          (aliasHref && router.asPath.startsWith(aliasHref)),
=======
        [styles.active]: path.startsWith(href) || path.startsWith(aliasHref),
>>>>>>> 67bff04e (feat: app migration):apps/public/components/Header/MenuItem.js
      })}
    >
      {label}
    </Link>
  );
};

export default memo(MenuItem);
