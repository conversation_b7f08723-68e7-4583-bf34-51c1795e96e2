import React, { memo } from 'react';
import styles from './LogoHeader.module.scss';
import Link from 'next/link';
import Image from 'next/image';

const LogoHeader = () => (
  <section className={styles['header-container']}>
    <div className={styles.header}>
      <Link
        href="/"
        aria-label="allcasting"
        className={styles.link}
        prefetch={false}
      >
        <Image
          src="/assets/logo/logo-4.svg"
          alt="logo"
          width={93}
          height={21}
        />
      </Link>
    </div>
  </section>
);

export default memo(LogoHeader);
