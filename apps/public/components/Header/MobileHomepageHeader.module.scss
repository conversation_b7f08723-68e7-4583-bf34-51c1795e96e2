@import '../../styles/variables';
@import '../../styles/mixins';

.header {
  display: block;
  position: sticky;
  z-index: 3;
  top: 0;
  bottom: auto;
  background-color: transparent;

  @include desktop {
    display: none;
  }
}

.container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-self: auto;
  max-width: $content-max-width;
  padding: $space-10 $space-15;
  margin-left: auto;
  margin-right: auto;

  .logo {
    color: $white;
    cursor: pointer;
    width: 93px;
    height: 21px;
  }

  .logo-link {
    line-height: 1;
  }

  .login-link {
    color: $blue-100;
    font-weight: 400;
    cursor: pointer;
    border-bottom: 1px solid transparent;
    line-height: 1;

    &:hover {
      border-bottom: 1px solid $blue-100;
    }
  }
}

.sticky-header {
  background: $white;
  transition: all 0.2s ease-in-out;

  .sticky-logo {
    color: $violet-100;
  }
}
