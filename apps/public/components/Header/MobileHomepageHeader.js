import React, { memo } from 'react';
import Logo from '/public/assets/logo/logo-1.svg';
import styles from './MobileHomepageHeader.module.scss';
import { useScroll } from '../../utils/useScroll';
import cn from 'classnames';
import { useModalContext } from '../../contexts/ModalContext';
import Link from 'next/link';

const MobileHomepageHeader = () => {
  const { scrolled } = useScroll();
  const { onLoginModalOpen } = useModalContext();

  return (
    <section className={styles.header}>
      <div
        className={cn(styles.container, {
          [styles['sticky-header']]: scrolled,
        })}
      >
        <Link href="/" passHref className={styles['logo-link']}>
          <Logo
            className={cn(styles.logo, {
              [styles['sticky-logo']]: scrolled,
            })}
          />
        </Link>
        <div className={styles['login-link']}>
          <span onClick={onLoginModalOpen}>Log In</span>
        </div>
      </div>
    </section>
  );
};

export default memo(MobileHomepageHeader);
