import React, { memo } from 'react';
import styles from './PremiumHeader.module.scss';
import MenuItem from './MenuItem';
import Link from 'next/link';
import Image from 'next/image';

const PremiumHeader = () => {
  return (
    <div className={styles['header-container']}>
      <div className={styles.header}>
        <Link
          href="/"
          passHref
          aria-label="allcasting"
          className={styles['logo-link']}
        >
          <Image
            src="/assets/logo/logo-4.svg"
            alt="logo"
            width={93}
            height={21}
          />
        </Link>
        <div className={styles.navbar}>
          <MenuItem label="Go to homepage" href="/" />
        </div>
      </div>
    </div>
  );
};

export default memo(PremiumHeader);
