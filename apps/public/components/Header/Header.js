import React, { memo } from 'react';
import styles from './Header.module.scss';
import { Button, PremiumActions } from '../index';
import MenuItem from './MenuItem';
import Link from 'next/link';
import { Amp } from '../../services/amp';
import { useFeature } from '../../contexts/FeatureContext';
import Image from 'next/image';

const Header = ({
  showSignUpModal,
  showLoginModal,
  isAuthenticated,
  isTalent,
  isPaidOrDelayed,
  canUpgradeExistingSubscription,
}) => {
  const { premiumActionsButtonEnabled } = useFeature();

  const onNavigateToUpgrade = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'subscribe',
      scope: Amp.element.scope.global,
      section: Amp.element.section.header,
      type: Amp.element.type.button,
      upgrade_to_12: !!canUpgradeExistingSubscription,
    });

    window.location.href = `${process.env.redirectTalentUrl}/upgrade${
      canUpgradeExistingSubscription ? '-12' : ''
    }`;
  };

  const onSignUpModalClick = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'sign up',
      section: Amp.element.section.header,
      type: Amp.element.type.button,
    });
    showSignUpModal();
  };

  const onLoginModalClick = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'log in',
      section: Amp.element.section.header,
      type: Amp.element.type.link,
    });
    showLoginModal();
  };

  return (
    <section className={styles['header-container']} data-cy="header">
      <div className={styles.header}>
        <Link
          href={isAuthenticated ? '/castingcalls' : '/'}
          passHref
          aria-label="allcasting"
          className={styles['logo-link']}
        >
          <Image
            src="/assets/logo/logo-4.svg"
            alt="logo"
            width={93}
            height={21}
          />
        </Link>
        <div className={styles.navbar}>
          <MenuItem
            label="casting calls"
            href="/castingcalls"
            aliasHref="/castingcall"
          />
          <MenuItem label="talent" href="/talent" aliasHref="/profile" />
          {!isAuthenticated && (
            <MenuItem label="casting directors" href="/casting-directors" />
          )}
          <MenuItem label="blog" href="/blog" />
          <MenuItem label="reviews" href="/reviews" />
          {isTalent &&
            !premiumActionsButtonEnabled &&
            (!isPaidOrDelayed || canUpgradeExistingSubscription) && (
              <>
                <span className={styles.divider}></span>
                <div className={styles['header-actions']}>
                  <Button
                    onClick={onNavigateToUpgrade}
                    label={
                      canUpgradeExistingSubscription
                        ? 'Upgrade your plan'
                        : 'Subscribe now'
                    }
                    kind="primary"
                    color="green-gradient"
                    minWidth="160px"
                    className={styles['subscribe-btn-animation-highlight']}
                  />
                </div>
              </>
            )}
          {premiumActionsButtonEnabled && (
            <>
              <span className={styles.divider}></span>
              <PremiumActions />
            </>
          )}
          {!isAuthenticated && !premiumActionsButtonEnabled && (
            <>
              <span className={styles.divider}></span>
              <span
                onClick={onLoginModalClick}
                className={styles.link}
                data-cy="header-login-btn"
              >
                log in
              </span>
              <Button
                onClick={onSignUpModalClick}
                label="SIGN UP"
                kind="secondary"
                color="greenblue"
                minWidth="160px"
                className={styles['sign-up-button']}
                dataCy="header-signup-btn"
              />
            </>
          )}
        </div>
      </div>
    </section>
  );
};

export default memo(Header);
