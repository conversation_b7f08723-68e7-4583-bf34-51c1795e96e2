@import '../../styles/variables';
@import '../../styles/mixins';
@import '../../styles/button-animations';

.header-container {
  position: sticky;
  z-index: 3;
  top: 0;
  bottom: auto;
  box-shadow: $shadow-card-container;
  background-color: $white;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-self: auto;
  max-width: $content-max-width;
  margin-left: auto;
  margin-right: auto;
  padding: $space-20;

  @include desktop {
    padding: $space-20 $space-60;
  }

  .logo-link {
    line-height: 1;
  }

  .navbar {
    display: flex;
    align-items: center;
  }
}
