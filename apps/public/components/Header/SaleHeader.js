import { memo } from 'react';
import styles from './SaleHeader.module.scss';
import { Countdown } from '../index';

const SaleHeader = ({ saleExpirationTime, onStopSale }) => {
  const onClick = () => {
    window.location.href = `${process.env.redirectTalentUrl}/upgrade`;
  };

  return (
    <div className={styles['sale-header']} onClick={onClick}>
      <div className={styles['sale-header-title-container']}>
        <span className={styles['sale-header-title']}>Sale</span>
        <span className={styles['sale-header-sub-title']}>ends in:</span>
      </div>
      <Countdown
        onCountdownEnd={onStopSale}
        expirationTime={saleExpirationTime}
        horizontalLayout
      />
    </div>
  );
};

export default memo(SaleHeader);
