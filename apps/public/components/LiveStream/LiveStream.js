import React from 'react';
import styles from './LiveStream.module.scss';

const LiveStream = ({ title, video, onClose, autoplay }) => {
  return (
    <div className={styles.container}>
      <div className={styles['close-button']} onClick={onClose} />
      <div className={styles['live-badge']}>LIVE</div>
      <div className={styles.video}>
        <iframe
          src={`${video}${autoplay && '?autoplay=1'}`}
          title={title}
          allow="autoplay; accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          allowFullScreen
          frameborder="0"
        ></iframe>
      </div>
      <div className={styles.description}>
        <span className={styles.webinar}>WEBINAR</span>
        <p className={styles.title}>{title}</p>
      </div>
    </div>
  );
};

export default LiveStream;
