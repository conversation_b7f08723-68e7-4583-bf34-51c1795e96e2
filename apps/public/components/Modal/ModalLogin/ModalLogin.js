import { LoginForm, Modal } from '../../index';
import styles from './ModalLogin.module.scss';
import React, { memo } from 'react';

const ModalLogin = ({
  onClose = () => {},
  openRegistrationModal = () => {},
}) => {
  return (
    <Modal
      backdropClose
      onClose={onClose}
      showDefaultLayout={false}
      classNameContainer={styles['login-modal']}
      hideMobileCloseButton
      showMobileBorderRadius={false}
      floatCloseButton
    >
      <LoginForm
        isModal
        closeModal={onClose}
        openRegistrationModal={openRegistrationModal}
      >
        <h1>Log in to allcasting</h1>
      </LoginForm>
    </Modal>
  );
};

export default memo(ModalLogin);
