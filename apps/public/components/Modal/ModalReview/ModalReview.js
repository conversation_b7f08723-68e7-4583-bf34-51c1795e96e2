import React, { memo } from 'react';
import styles from './ModalReview.module.scss';
import { Button, HeaderMobile, Input, Modal, StarRating } from '../../index';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useNotifications } from '../../../contexts/NotificationContext';
import ApiNoCache from '../../../services/apiNoCache';
import { ErrorMessage } from '../../../constants/form';

const ModalReview = ({ onClose, imgSrc, profileId }) => {
  const { setNotification } = useNotifications();

  const formik = useFormik({
    initialValues: {
      content: '',
      rating: 0,
    },
    onSubmit: async () => {
      const body = new FormData();

      body.append('content', formik.values.content);
      body.append('rating', String(formik.values.rating));

      const reviewResponse = (
        await ApiNoCache.clientGateway(`/profiles/${profileId}/testimonials`, {
          method: 'POST',
          body,
        })
      ).data;

      if (reviewResponse.status !== 'ok') {
        setNotification({
          type: 'error',
          message: reviewResponse.message || ErrorMessage.Unexpected,
          timeout: '5000',
        });
      } else {
        onClose(true);
      }
    },
    validationSchema: Yup.object({
      content: Yup.string()
        .required(ErrorMessage.ReviewRequired)
        .max(500, ErrorMessage.MaxCharactersLatin.replace('X', '500')),
      rating: Yup.number().min(1, ErrorMessage.RatingRequired),
    }),
  });

  const onRatingChange = (rating) => {
    formik.setFieldValue('rating', rating);
  };

  const disabled =
    (formik.errors.content && formik.touched.content) ||
    (formik.errors.rating && formik.touched.rating);

  return (
    <Modal
      backdropClose
      onClose={onClose}
      classNameContainer={styles['review-form-modal']}
      showDefaultLayout={false}
      hideMobileCloseButton
      showMobileBorderRadius={false}
    >
      <form className={styles['review-form']} onSubmit={formik.handleSubmit}>
        <HeaderMobile onClose={onClose} hideOnTablet>
          Leave your review
        </HeaderMobile>
        <div className={styles['review-form-header']}>
          <div className={styles['review-form-image-container']}>
            <div
              className={styles['review-form-image']}
              style={{
                backgroundImage: `url('${imgSrc}')`,
              }}
            />
          </div>
          <span className={styles['review-form-title']}>Leave your review</span>
          <div className={styles['review-form-rating-container']}>
            <div className={styles['review-form-rating-title']}>
              Rate this site
            </div>
            <StarRating
              fixed={false}
              width={20}
              height={20}
              onChange={onRatingChange}
            />
          </div>
        </div>
        <div className={styles['review-form-content']}>
          <div className={styles['review-form-field']}>
            <Input
              name="content"
              placeholder="Type your review here"
              hint="Max 500 characters"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.content}
              isTouched={formik.touched.content}
              error={formik.errors.content}
              charCount={500}
            />
            {formik.errors.rating && (
              <div className={styles.error}>{formik.errors.rating}</div>
            )}
          </div>
          <div className={styles['review-form-actions']}>
            <Button
              disabled={disabled}
              type="submit"
              minWidth="220px"
              kind="secondary"
              label="Submit"
            />
          </div>
        </div>
      </form>
    </Modal>
  );
};

export default memo(ModalReview);
