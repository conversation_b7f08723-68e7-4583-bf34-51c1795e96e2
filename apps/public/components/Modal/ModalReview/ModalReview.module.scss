@import '../../../styles/variables';
@import '../../../styles/mixins';

.review-form {
  display: flex;
  flex-flow: column nowrap;
  height: 100%;
  background: $gradient-port-gore;

  @include tablet {
    background: $white;
    min-height: unset;
    height: fit-content;
    padding: $space-15 $space-45 $space-40;
  }
}

.review-form-header {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $space-20;

  @include tablet {
    padding: 0;

    &::after {
      content: '';
      display: block;
      width: 194px;
      height: 1px;
      margin: $space-20 auto 0;
      background-color: $grey-40;
    }
  }
}

.review-form-image-container {
  display: none;
  border-radius: 50%;
  margin: 0 auto;
  border: 1px solid $grey-60;
  position: relative;
  width: 87px;
  height: 87px;
  padding: $space-5;

  @include tablet {
    display: block;
  }
}

.review-form-image {
  position: relative;
  height: 100%;
  background-color: $grey-60;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.review-form-title {
  font-size: 30px;
  font-weight: 700;
  display: none;
  line-height: 2;

  @include tablet {
    display: initial;
  }
}

.review-form-rating-container {
  display: flex;
  align-items: center;
  font-weight: 300;
  font-size: 16px;
  gap: $space-15;
  flex-direction: column;
  width: 100%;
  background-color: $white;
  border-radius: 10px;
  padding: $space-20;

  @include tablet {
    justify-content: center;
    flex-direction: row;
    border-radius: unset;
    padding: 0;
  }
}

.review-form-content {
  background-color: $white;
  border-radius: 10px;
  padding: $space-20;
  margin: $space-20;

  @include tablet {
    padding: 0;
    margin: 0;
    border-radius: unset;
  }
}

.review-form-field {
  padding-top: $space-30;
  padding-bottom: 70px;
  position: relative;
}

.review-form-actions {
  display: flex;
  align-items: center;
  justify-content: center;
}

.error {
  padding-top: $space-30;
  color: $red-60;
  text-align: center;
  font-size: 14px;
  font-weight: 300;
  position: absolute;
  margin: 0 auto;
  width: 100%;
}

.review-form-rating-title {
  width: 100%;
  text-align: center;
  font-size: 18px;
  font-weight: 700;

  &::after {
    content: '';
    display: block;
    width: 100%;
    height: 1px;
    margin: $space-20 auto 0;
    background-color: $grey-40;
  }

  @include tablet {
    font-size: 16px;
    font-weight: 300;
    width: initial;

    &::after {
      display: none;
    }
  }
}

.review-form-modal {
  width: 100vw;
  height: 100vh;
  height: 100dvh;

  @include tablet {
    height: unset;
    max-width: 620px;
    border-radius: 10px;
  }
}
