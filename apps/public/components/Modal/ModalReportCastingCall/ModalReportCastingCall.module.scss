@import '../../../styles/variables';
@import '../../../styles/mixins';

.report-modal-content {
  @include desktop {
    text-align: center;
  }
}

.report-modal-overlay {
  padding: 0;
  align-items: flex-end;

  @include desktop {
    align-items: center;
  }
}

.report-modal-container {
  border-radius: 15px 15px 0 0;
  max-width: initial;
  width: 100%;

  @include desktop {
    max-width: 670px;
    border-radius: 10px;
  }
}

.title {
  margin-top: 0;
}

.report-form {
  display: flex;
  flex-direction: column;
  gap: $space-20;
  text-align: center;

  @include desktop {
    padding: $space-10 $space-30 0;
  }
}

.info {
  margin-top: -$space-20;
}

.actions {
  margin-top: $space-40;
}

.actions-submit {
  min-width: 160px;
}

.success-content {
  display: flex;
  flex-direction: column;
  text-align: center;
  align-items: center;

  @include desktop {
    padding: $space-10 $space-60 $space-10;
  }
}

.success-icon {
  width: 64;
}
