import React, { useState } from 'react';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import Image from 'next/image';

import styles from './ModalReportCastingCall.module.scss';
import { Button, Modal, RadioWithHint, InputFormik } from '../../index';
import { useNotifications } from '../../../contexts/NotificationContext';
import ApiNoCache from '../../../services/apiNoCache';
import { Amp } from '../../../services/amp';
import { ErrorMessage } from '../../../constants/form';

const byWeight = (a, b) => b.weight - a.weight;

const ModalReportCastingCall = ({
  callId,
  onClose,
  reportOptions,
  setReportSubmitted,
}) => {
  const [page, setPage] = useState('form');
  const { setNotification } = useNotifications();

  const submitComplaint = async (values) => {
    const { type, info } = values;
    const body = new FormData();

    body.append('issue_type', type);
    body.append('issue_description', info);

    const res = (
      await ApiNoCache.clientGateway(`/casting-calls/${callId}/complaint`, {
        method: 'POST',
        body: body,
      })
    ).data;

    if (res.status === 'ok') {
      Amp.track(Amp.events.formSubmitted, {
        name: 'casting call complaint',
        section: Amp.element.section.castingCall,
        scope: Amp.element.scope.castingCallItemPage,
        result: Amp.element.result.success,
        casting_call_id: callId,
      });
      setReportSubmitted(true);
      setPage('success');
    } else {
      setNotification({
        type: 'error',
        message: res.error || ErrorMessage.Unexpected,
        timeout: '5000',
      });
      onClose();
    }
  };

  const schema = Yup.object().shape({
    type: Yup.string().required(),
    info: Yup.string()
      .max(1000, ErrorMessage.MaxDescription)
      .required(ErrorMessage.InfoRequired)
      .test(
        'minWords',
        ErrorMessage.MinDescription,
        (value) => (value?.trim().split(' ').length || 0) >= 10,
      ),
  });

  const options = reportOptions.sort(byWeight);

  const form = (
    <>
      <h3 className={styles.title}>
        Choose the type of issue you&apos;d like to report:
      </h3>
      <Formik
        initialValues={{ type: options[0]?.type || '', info: '' }}
        validationSchema={schema}
        onSubmit={submitComplaint}
      >
        {({ isValid, dirty, isSubmitting }) => (
          <Form className={styles['report-form']}>
            {options.map((option) => (
              <Field
                key={option.type}
                as={RadioWithHint}
                name="type"
                type="radio"
                value={option.type}
                label={option.title}
                hint={option.description}
              />
            ))}
            <InputFormik
              name="info"
              className={styles.info}
              placeholder="Additional info"
              hint="Provide additional information describing the issue"
              charCount={1000}
              required
            />
            <div className={styles.actions}>
              <Button
                className={styles['actions-submit']}
                disabled={!(isValid && dirty && !isSubmitting)}
                color="blue"
                type="submit"
                label="Submit"
                width={'220px'}
              />
            </div>
          </Form>
        )}
      </Formik>
    </>
  );

  const success = (
    <div className={styles['success-content']}>
      <Image
        src={'/assets/icons/icon-completed.svg'}
        alt="icon-completed"
        width={63}
        height={59.5}
      />
      <h3>Your report has been successfully submitted!</h3>
      <p>
        Your feedback helps us maintain a safe and trustworthy environment for
        all users. Our team will review the reported content promptly.
      </p>
    </div>
  );

  return (
    <Modal
      backdropClose
      onClose={onClose}
      classNameOverlay={styles['report-modal-overlay']}
      classNameContainer={styles['report-modal-container']}
      classNameContent={styles['report-modal-content']}
    >
      {page === 'form' && form}
      {page === 'success' && success}
    </Modal>
  );
};

export default ModalReportCastingCall;
