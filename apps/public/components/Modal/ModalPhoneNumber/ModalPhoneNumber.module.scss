@import '../../../styles/variables';
@import '../../../styles/mixins';

.overlay {
  padding: 0;
  align-items: flex-end;

  @include tablet {
    align-items: center;
  }
}

.container {
  width: 100%;
  padding: 60px 20px;
  border-radius: 15px 15px 0 0;

  @include tablet {
    max-width: 570px;
    padding: 60px;
    border-radius: 15px;
  }
}

.content {
  padding: 0;
}

.title {
  font-size: 18px;
  font-weight: 700;

  @include tablet {
    text-align: center;
  }
}

.main {
  display: flex;
  flex-direction: column;
  gap: 30px;

  @include tablet {
    align-items: center;
  }
}

.form {
  width: 100%;
  padding-top: 5px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
}

.button {
  text-transform: none;
  padding: 9.5px 20px;
}

.disclaimer {
  margin-top: -6px;
  font-size: 12px;
  line-height: 24px;
}

.link {
  color: $blue-100;
  cursor: pointer;
  font-weight: 700;
}
