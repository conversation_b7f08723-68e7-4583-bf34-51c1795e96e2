import { Form, Formik } from 'formik';
import { useEffect, useRef, useState } from 'react';
import * as Yup from 'yup';
import dayjs from 'dayjs';
import Button from '../../Button/Button';
import Modal from '../Modal';
import styles from './ModalPhoneNumber.module.scss';
import InputFormik from '../../FormFields/InputFormik';
import { ErrorMessage, PHONE_NUMBER_REGEX } from '../../../constants/form';
import { checkTollFreeNumber } from '../../../utils/checkTollFreeNumber';
import { useAuth } from '../../../contexts/AuthContext';
import ApiNoCache from '../../../services/apiNoCache';
import { useUINotificationsContext } from '../../../contexts/UINotificationsContext';
import { useNotifications } from '../../../contexts/NotificationContext';
import { UIEvent } from '../../../constants/uiEvents';
import { Amp } from '../../../services/amp';
import PrivacyPolicy from '../../PrivacyPolicy/PrivacyPolicy';
import TermsOfUse from '../../TermsOfUse/TermsOfUse';

const maskPhone = (value) => {
  const trimmed = value.replace(/[\D]/g, '');
  const [, g0, g1, g2] = trimmed.match(/(\d{0,3})(\d{0,3})(\d{0,4})/);
  const final = g0 + (g1 && '-') + g1 + (g2 && '-') + g2;

  return final || '';
};

const FIVE_SECONDS = 1_000 * 5;

const onPhoneChange = (event, setFieldValue) => {
  const { value, selectionStart, selectionEnd } = event.target;
  const dashDiff = [4, 8].some(
    (pos) => pos === selectionStart && pos === value.length,
  )
    ? 1
    : 0;

  const final = maskPhone(value);

  event.target.value = final;
  event.target.selectionStart = selectionStart + dashDiff;
  event.target.selectionEnd = selectionEnd + dashDiff;

  setFieldValue('phone', final);
};

const trackEvent = async (accountId) => {
  const body = new FormData();

  body.append('event_name', UIEvent.PhoneNumberModalViewed);
  body.append('account_id', String(accountId));

  await ApiNoCache.clientGateway(`/tracking/events`, {
    body,
    method: 'POST',
  });
};

const ModalPhoneNumber = ({}) => {
  const [showCloseButton, setShowCloseButton] = useState(false);
  const { accountId, userProfiles, refreshUserProfiles } = useAuth();
  const { setShowPhoneNumberModal, phoneNumberModalOccurrence: occurrence } =
    useUINotificationsContext();
  const { setNotification } = useNotifications();
  const timestamp = useRef(null);
  const [showTerms, setShowTerms] = useState(false);
  const [showPrivacyPolicy, setShowPrivacyPolicy] = useState(false);

  const toggleShowTerms = (e) => {
    e?.preventDefault();
    setShowTerms(!showTerms);
  };

  const toggleShowPrivacyPolicy = (e) => {
    e?.preventDefault();
    setShowPrivacyPolicy(!showPrivacyPolicy);
  };

  useEffect(() => {
    timestamp.current = new Date();
    const timeoutId = setTimeout(() => {
      Amp.track(Amp.events.phoneNotificationDisplayed, { occurrence });
      trackEvent(accountId);
      setShowCloseButton(true);
    }, FIVE_SECONDS);

    return () => clearTimeout(timeoutId);
  }, []);

  const onClose = () => {
    if (showCloseButton) {
      Amp.track(Amp.events.phoneNotificationDismissed, {
        time_displayed: dayjs().diff(timestamp.current, 's'),
      });
      setShowPhoneNumberModal(false);
    }
  };

  const validationSchema = Yup.object({
    phone: Yup.string()
      .required('Please provide a phone number')
      .matches(PHONE_NUMBER_REGEX, ErrorMessage.PhonePattern)
      .test('phoneIsValid', ErrorMessage.PhonePatternToll, async (value) => {
        return checkTollFreeNumber(value || '');
      }),
  });

  const handleSubmit = async (values) => {
    const body = new FormData();
    const newPhone = values.phone.replace(/[\D]/g, '');
    const user = userProfiles[0] || {};

    body.append('phone', newPhone);
    body.append('phone_opt_outed', '0');

    const response = await ApiNoCache.clientGateway(
      `/accounts/${accountId}/touches`,
      {
        body,
        method: 'PUT',
      },
    );
    const isSuccess = response.data.status === 'ok';

    Amp.track(Amp.events.submitPhoneNumber, {
      scope: Amp.element.scope.modal,
      phone_number_last_4_digits: newPhone.slice(-4),
      country_code: user.country,
      status: isSuccess ? 'success' : 'failure',
      error_message: isSuccess ? null : response.data.message,
    });

    if (isSuccess) {
      refreshUserProfiles();
      setShowPhoneNumberModal(false);
      setNotification({
        type: 'success',
        message: 'Phone number set!',
        timeout: '5000',
      });
    } else {
      setNotification({
        type: 'error',
        message: response.data.message || ErrorMessage.Unexpected,
        timeout: '5000',
      });
    }
  };

  return (
    <>
      <Modal
        classNameOverlay={styles.overlay}
        classNameContainer={styles.container}
        classNameContent={styles.content}
        backdropClose
        onClose={onClose}
        showCloseButton={showCloseButton}
        showMobileBorderRadius={true}
        floatCloseButton={true}
      >
        <div className={styles.main}>
          <p className={styles.title}>
            Stay Connected and Never Miss an Opportunity!
          </p>
          <p>
            Enter the best phone number to be contacted by casting directors,
            and our member support team. You will also receive important account
            info and alerts for the hottest casting calls!
          </p>
        </div>
        <Formik
          initialValues={{ phone: '' }}
          onSubmit={handleSubmit}
          validationSchema={validationSchema}
        >
          {({ isValid, dirty, isSubmitting, setFieldValue }) => (
            <Form className={styles.form}>
              <InputFormik
                name="phone"
                type="tel"
                onChange={(e) => onPhoneChange(e, setFieldValue)}
                placeholder="Phone number"
                prefix="+1"
              />
              <span className={styles['disclaimer']}>
                By providing your phone number, you agree to receive automated
                marketing text messages from allcasting at the phone number
                provided. Consent is not a condition of purchase. Message & data
                rates may apply. Reply STOP to opt-out. View our{' '}
                <span className={styles.link} onClick={toggleShowPrivacyPolicy}>
                  privacy policy
                </span>{' '}
                and{' '}
                <span className={styles.link} onClick={toggleShowTerms}>
                  terms of use
                </span>
                .
              </span>
              <Button
                className={styles.button}
                disabled={!isValid || !dirty || isSubmitting}
                color="blue"
                type="submit"
                label="Save"
                minWidth={'160px'}
                shadow={false}
              />
            </Form>
          )}
        </Formik>
      </Modal>
      {showTerms && (
        <Modal backdropClose onClose={toggleShowTerms}>
          <TermsOfUse />
        </Modal>
      )}
      {showPrivacyPolicy && (
        <Modal backdropClose onClose={toggleShowPrivacyPolicy}>
          <PrivacyPolicy onNavigation={toggleShowPrivacyPolicy} />
        </Modal>
      )}
    </>
  );
};

export default ModalPhoneNumber;
