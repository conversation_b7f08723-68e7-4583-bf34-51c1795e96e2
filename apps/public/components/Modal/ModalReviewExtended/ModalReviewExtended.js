import React, { memo, useEffect, useState } from 'react';
import styles from './ModalReviewExtended.module.scss';
import { Button, Input, Modal, StarRating } from '../../index';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useNotifications } from '../../../contexts/NotificationContext';
import ApiNoCache from '../../../services/apiNoCache';
import classNames from 'classnames';
import { Amp } from '../../../services/amp';
import { ErrorMessage } from '../../../constants/form';

const ModalReviewExtended = ({ onClose }) => {
  const { setNotification } = useNotifications();
  const [randomChosenTitle, setRandomChosenTitle] = useState('');
  const externalLink = randomChosenTitle?.includes('Google')
    ? 'https://g.page/r/CbUirZrKb7pNEAI/review'
    : 'https://www.trustpilot.com/evaluate/allcasting.com';

  const formik = useFormik({
    initialValues: {
      content: '',
      rating: 0,
    },
    onSubmit: async () => {
      const body = new FormData();

      body.append('content', formik.values.content);
      body.append('rating', String(formik.values.rating));

      const reviewResponse = (
        await ApiNoCache.clientGateway(`/reviews`, {
          method: 'POST',
          body,
        })
      ).data;

      if (reviewResponse.status !== 'ok') {
        setNotification({
          type: 'error',
          message: reviewResponse.message || ErrorMessage.Unexpected,
          timeout: '5000',
          showOnModal: true,
        });
      } else {
        onClose();
      }
    },
    validationSchema: Yup.object({
      content: Yup.string()
        .required(ErrorMessage.ReviewRequired)
        .max(500, ErrorMessage.MaxCharactersLatin.replace('X', '500')),
      rating: Yup.number().min(1, ErrorMessage.RatingRequired),
    }),
  });

  const { rating } = formik.values;

  const onRatingChange = (event) => {
    formik.setFieldValue('rating', event);
  };

  const handleExternalReview = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: `click ${randomChosenTitle} review`,
      scope: Amp.element.scope.castingCallItemPage,
      section: Amp.element.section.reviews,
    });
    window.open(externalLink);
  };

  useEffect(() => {
    setRandomChosenTitle(Math.random() < 0.5 ? 'Google' : 'Trustpilot');
  }, []);

  useEffect(() => {
    if (rating >= 4) {
      Amp.track(Amp.events.elementClicked, {
        name: `ask for ${randomChosenTitle} review`,
        scope: Amp.element.scope.castingCallItemPage,
        section: Amp.element.section.reviews,
      });
    }
  }, [rating]);

  return (
    <Modal
      backdropClose
      onClose={onClose}
      classNameContainer={styles['review-form-modal']}
      showMobileBorderRadius={false}
    >
      <form className={styles['review-form']} onSubmit={formik.handleSubmit}>
        <div className={styles['review-form-content']}>
          <div className={styles.header}>
            <div className={styles['dialog-label']}>Hey there,</div>
            <h1 className={styles['dialog-h1']}>Superstar!</h1>
            <div className={styles['dialog-text']}>
              Are you enjoying allcasting? Please leave a review to make your
              experience
              <br />
              <span>here even better!</span>
            </div>
          </div>
          <div className={styles['dialog-rate']}>
            <StarRating
              fixed={false}
              width={20}
              height={20}
              onChange={onRatingChange}
            />
          </div>
        </div>
        {!!rating && <span className={styles.underline}></span>}
        {!!rating && rating < 4 && (
          <div className={styles['review-form-content']}>
            <div className={styles['review-form-field']}>
              <Input
                name="content"
                placeholder="Type your review here"
                hint="Max 500 characters"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.content}
                isTouched={formik.touched.content}
                error={formik.errors.content}
                charCount={500}
              />
              {formik.errors.rating && (
                <div className={styles.error}>{formik.errors.rating}</div>
              )}
              <Button
                disabled={
                  (formik.errors.content && formik.touched.content) ||
                  (formik.errors.rating && formik.touched.rating)
                }
                type="submit"
                minWidth={'220px'}
                kind="secondary"
                label={'Review Now'}
              />
            </div>
          </div>
        )}
        {!!rating && rating >= 4 && (
          <div className={styles['review-form-content']}>
            <div className={styles['external-review']}>
              <div
                className={classNames(
                  styles['dialog-text'],
                  styles['heading-text'],
                )}
              >
                <b>We’re glad you loved our site!</b>
              </div>
              <div className={styles['dialog-text']}>
                Please, take a minute and share your experience with other
                actors <br />
                <span id="title">{' on ' + randomChosenTitle}</span>
              </div>
              <div className="dialog-btn-panel">
                <Button
                  type="submit"
                  minWidth={'220px'}
                  kind="primary"
                  color="greenblue"
                  onClick={handleExternalReview}
                  label={'Review on ' + randomChosenTitle}
                ></Button>
              </div>
              <div className="dialog-text">
                <span className={styles['text-alignment']}>
                  {'It’s quick and easy!  '}
                  {randomChosenTitle?.includes('Google')
                    ? 'You need only a Google account.'
                    : 'No registration required.'}
                </span>
              </div>
            </div>
          </div>
        )}
      </form>
    </Modal>
  );
};

export default memo(ModalReviewExtended);
