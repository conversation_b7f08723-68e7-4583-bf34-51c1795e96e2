@import '../../../styles/variables';
@import '../../../styles/mixins';

.header {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  font-weight: 300;
}

.review-form {
  display: flex;
  flex-flow: column nowrap;
  min-height: 100%;
  background: transparent;

  @include tablet {
    background: $white;
    min-height: unset;
    height: fit-content;
  }
}

.review-form-header {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $space-20;
}

.review-form-image-container {
  display: none;
  border-radius: 50%;
  margin: 0 auto;
  border: 1px solid $grey-60;
  position: relative;
  width: 87px;
  height: 87px;
  padding: $space-5;

  @include tablet {
    display: block;
  }
}

.review-form-image {
  position: relative;
  height: 100%;
  background-color: $grey-60;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.review-form-title {
  font-size: 30px;
  font-weight: 700;
  display: none;
  line-height: 2;

  @include tablet {
    display: initial;
  }
}

.review-form-rating-container {
  display: flex;
  align-items: center;
  font-weight: 300;
  font-size: 16px;
  gap: $space-15;
  flex-direction: column;
  width: 100%;
  background-color: $white;
  border-radius: 10px;
  padding: $space-20;

  @include tablet {
    justify-content: center;
    flex-direction: row;
    border-radius: unset;
    padding: 0;
  }
}

.review-form-content {
  background-color: $white;
  border-radius: 10px;

  .header {
    h1 {
      font-weight: 500;
    }
  }

  @include tablet {
    padding: 0;
    margin: 0;
    border-radius: unset;
  }
}

.review-form-field {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  position: relative;
  padding-top: $space-20;

  @include tablet {
    padding-top: $space-20;
  }

  & button {
    margin-top: $space-30;
  }
}

.review-form-actions {
  display: flex;
  align-items: center;
  justify-content: center;
}

.error {
  padding-top: $space-30;
  color: $red-60;
  text-align: center;
  font-size: 14px;
  font-weight: 300;
  position: absolute;
  margin: 0 auto;
  width: 100%;
}

.review-form-rating-title {
  width: 100%;
  text-align: center;
  font-size: 18px;
  font-weight: 700;

  &::after {
    content: '';
    display: block;
    width: 100%;
    height: 1px;
    margin: $space-20 auto 0;
    background-color: $grey-40;
  }

  @include tablet {
    font-size: 16px;
    font-weight: 300;
    width: initial;

    &::after {
      display: none;
    }
  }
}

.review-form-mobile-header {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  padding: $space-10 $space-20;
  border-bottom: 1px solid $grey-60;
  background-color: $white;

  @include tablet {
    display: none;
  }
}

.arrow-icon-container {
  color: $black;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.arrow-icon {
  transform: rotateY(180deg);
  filter: invert(65%) sepia(100%) saturate(3200%) hue-rotate(160deg)
    brightness(104%) contrast(99%);
}

.review-form-mobile-header-title {
  font-weight: 300;
  margin: 0;
  font-size: 16px;
}

.review-form-modal {
  @include tablet {
    max-width: 620px;
    border-radius: 10px;
  }
}

.dialog-top-type {
  &::before {
    content: '';
    display: block;
    width: 144px;
    height: 24px;
    margin: $space-10 auto 0;
  }

  &::after {
    content: '';
    display: block;
    margin: $space-10 auto $space-20;
    width: 87px;
    height: 2px;
    background: $grey-20;
    opacity: 0.5;
  }
}

.dialog-text {
  display: flex;
  flex-direction: column;
  text-align: center;
  margin: 18px 0 0;
  line-height: 1.75;
  width: 100%;
}

.dialog-btn-panel {
  margin-top: $space-30;
  margin-bottom: $space-30;
}

.dialog-rate {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: $space-20;
  padding-bottom: $space-25;

  @include tablet {
    padding-bottom: $space-10;
  }
}

.underline {
  display: flex;
  width: 100%;
  margin-top: $space-20;
  height: 2px;
  background: $grey-20;
  opacity: 1;
  border: 1px solid $grey-20;
}

.external-review {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  padding-bottom: 0;

  @include tablet {
    padding-top: $space-20;
  }

  & button {
    margin-top: $space-20;
    margin-bottom: $space-20;
  }
}

.dialog-h1 {
  margin-top: 0;
  margin-bottom: 0;
}

.text-alignment {
  display: flex;
  text-align: center;
}
