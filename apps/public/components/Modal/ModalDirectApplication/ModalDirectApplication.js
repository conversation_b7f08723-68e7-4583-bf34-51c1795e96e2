import { memo } from 'react';
import { Modal } from '../../index';
import styles from './ModalDirectApplication.module.scss';
import dayjs from 'dayjs';
import Image from 'next/image';
import { maskPhoneNumber } from '../../../utils/maskPhoneNumber';
import Link from 'next/link';
import { TYPE } from '../../../constants/castingCalls';

const ModalDirectApplication = ({
  onClose,
  type,
  website,
  phone,
  location,
  address,
  dates,
}) => {
  return (
    <Modal
      onClose={onClose}
      backdropClose
      classNameContainer={styles['direct-application-modal-container']}
      dataCy="direct-application-modal"
    >
      <div className={styles['direct-application-modal']}>
        {type === TYPE.Url && (
          <div className={styles['direct-application-container']}>
            <span className={styles['direct-application-title']}>
              Apply to Role
            </span>
            <span>If you like this role, follow</span>
            <div className={styles['direct-application-website-container']}>
              <Link
                prefetch={false}
                href={website}
                className={styles['direct-application-website-link']}
                target="_blank"
                rel="nofollow"
              >
                {website}
              </Link>
            </div>
            <span>to send your application</span>
          </div>
        )}
        {type === TYPE.Phone && (
          <div className={styles['direct-application-container']}>
            <span className={styles['direct-application-title']}>
              Apply to Role
            </span>
            <span>If you like this role, please call this number to apply</span>
            <div className={styles['direct-application-phone-container']}>
              <Link
                prefetch={false}
                href={`tel:${phone}`}
                className={styles['direct-application-phone-link']}
                target="_blank"
                rel="nofollow"
              >
                {maskPhoneNumber(phone)}
              </Link>
            </div>
          </div>
        )}
        {type === TYPE.Open && (
          <div className={styles['direct-application-container']}>
            <span className={styles['direct-application-title']}>
              Apply to Role
            </span>
            <span>
              In order to apply to this role, you will need to show up at the
              following time and location
            </span>
            <div className={styles['direct-application-details-container']}>
              <div className={styles['direct-application-dates-container']}>
                {dates.map((date, i) => (
                  <div
                    key={i}
                    className={styles['direct-application-date-container']}
                  >
                    <div
                      className={
                        styles['direct-application-calendar-container']
                      }
                    >
                      <Image
                        src={'/assets/icons/icon-calendar-1.svg'}
                        alt="icon"
                        width={15}
                        height={15}
                      />
                      <span>{dayjs(date.start).format('MM/DD/YYYY')}</span>
                    </div>
                    <div>
                      from {dayjs(date.start).format('h:mm A')} to{' '}
                      {dayjs(date.end).format('h:mm A')}
                    </div>
                  </div>
                ))}
              </div>
              <div className={styles['direct-application-location-container']}>
                <Image
                  className={styles['direct-application-location-icon']}
                  src={'/assets/icons/icon-pin-2.svg'}
                  alt="icon"
                  width={15}
                  height={15}
                />
                <span>
                  {address}, {location}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default memo(ModalDirectApplication);
