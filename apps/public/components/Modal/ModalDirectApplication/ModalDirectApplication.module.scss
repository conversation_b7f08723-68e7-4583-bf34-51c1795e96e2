@import '../../../styles/variables';

.direct-application-modal {
  display: flex;
  justify-content: center;
}

.direct-application-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: fit-content;
  align-items: center;
  font-weight: 300;
  gap: $space-15;
  text-align: center;
  max-width: 480px;
}

.direct-application-title {
  font-size: 24px;
  font-weight: 700;
}

.direct-application-website-container {
  background-color: $grey-10;
  padding: $space-20;
  text-align: left;
  width: 100%;
  display: flex;
  align-items: center;
}

.direct-application-phone-container {
  background-color: $grey-10;
  padding: $space-20;
  text-align: center;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.direct-application-website-link,
.direct-application-phone-link {
  color: inherit;
  position: relative;
  display: inline-block;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: $space-5;
  background: $gradient-light-blue no-repeat center 25px;
  background-size: calc(100% - 10px) 2px;
  transition:
    background-size 0.2s ease-out,
    background-position 0.2s ease-out;

  &:hover {
    text-decoration: none;
    background-size: 100% 31px;
    background-position: 0 1px;
  }
}

.direct-application-details-container {
  background-color: $grey-10;
  padding: $space-20;
  text-align: left;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: $space-10;
}

.direct-application-calendar-container {
  display: flex;
  gap: $space-10;
  align-items: center;
}

.direct-application-date-container {
  display: grid;
  grid-template-columns: 100px 1fr;
  font-size: 14px;
  font-weight: 300;
  line-height: 20px;
  grid-column-gap: $space-10;
  padding: $space-10 0;
}

.direct-application-location-container {
  display: flex;
  align-items: flex-start;
  gap: $space-10;
  font-size: 14px;
  font-weight: 300;
  line-height: 20px;
}

.direct-application-location-icon {
  margin-top: 3px;
}

.direct-application-modal-container {
  max-width: 620px;
  width: 100%;
}
