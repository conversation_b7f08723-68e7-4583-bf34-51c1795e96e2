@import '../../../styles/variables';
@import '../../../styles/mixins';

.modal-container {
  position: absolute;
  bottom: 0;
  padding: 0;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  width: 100%;

  @include tablet {
    position: relative;
    max-width: 560px;
    padding: $space-20 0 0;
    border-radius: 10px;
  }
}

.email-application {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: $space-20;
  padding: $space-20;

  @include tablet {
    padding: $space-20 $space-40 $space-40;
  }
}

.email-application-title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.email-application-title {
  font-size: 24px;
  font-weight: 700;

  @include tablet {
    font-size: 30px;
  }
}

.email-application-action-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.email-application-description {
  display: flex;
  flex-direction: column;
}

.email-application-note {
  display: flex;
  flex-direction: column;
  background-color: $grey-10;
  border-radius: 8px;
  padding: $space-15 $space-25;
  text-align: left;

  div {
    margin-top: $space-10;
  }
}
