import React, { memo } from 'react';
import { <PERSON><PERSON>, Modal } from '../../index';
import styles from './ModalEmailApplicationStatus.module.scss';
import Image from 'next/image';
import { isImageApproved } from '../../../utils/imageHelpers';

const ModalEmailApplicationStatus = ({ onClose, profile }) => {
  const { closeUpImage, rating, id } = profile;

  const isHeadshotValid = !!closeUpImage && isImageApproved(closeUpImage);
  const isRatingLow = Number(rating.replaceAll(',', '')) < 7000;
  const version = Math.floor(Math.random() * 3) + 1;

  let title = '✅Success! Your Application is Submitted!',
    description =
      'Your application has been successfully submitted and is now under review. Keep applying to increase your chances of landing the perfect gig!',
    note = null,
    label = null;

  if (!isHeadshotValid) {
    title = `Oops! You're Missing a Headshot!`;
    description =
      'A headshot is essential for casting directors to consider your application. Please upload one now to complete your submission.';
    note = (
      <>
        <b>Pro tip:</b> Profiles with complete details and a headshot get
        noticed way more often! Take a moment to update your profile and stand
        out.
      </>
    );
    label = 'UPLOAD HEADSHOT NOW';
  } else if (isRatingLow) {
    switch (version) {
      case 1:
        title = `✅Congratulations!`;
        description = `Your application has been successfully sent!`;
        note = (
          <>
            <b>Pro tip:</b> Talents with a profile rating of 7000+ get 80% more
            invitations to casting calls. Complete your profile, add videos, and
            showcase your experience to boost your chances!
          </>
        );
        label = 'IMPROVE PROFILE NOW';
        break;
      case 2:
        title = `✨Fantastic!`;
        description = `Your submission has been successfully sent!`;
        note = (
          <>
            <b>Want to stand out?</b> Profiles with a 7000+ rating get invited
            80% more often. Make sure your profile is fully completed with
            photos, videos, and details!
          </>
        );
        label = 'ENHANCE YOUR PROFILE NOW';
        break;
      default:
        title = `🎉Well Done!`;
        description = `Your application was sent!`;
        note = (
          <>
            <b>Pro tip:</b> Profiles with a 7000+ rating are 80% more likely to
            get callbacks! Ensure your profile is fully updated with your skills
            and experience to maximize your chances.
          </>
        );
        label = 'UPDATE PROFILE NOW';
    }
  }

  const onClick = () => {
    window.location.href = `${process.env.redirectTalentUrl}/profile/${id}/info`;
  };

  return (
    <Modal
      backdropClose
      onClose={onClose}
      classNameContainer={styles['modal-container']}
      showDefaultLayout={false}
      floatCloseButton
      dataCy="email-application-modal-status"
    >
      <div className={styles['email-application']}>
        {isHeadshotValid && (
          <Image
            src="/assets/icons/icon-mail.svg"
            alt="icon"
            width="53"
            height="45"
          />
        )}
        <div className={styles['email-application-title-container']}>
          <span className={styles['email-application-title']}>{title}</span>
        </div>
        <div className={styles['email-application-description']}>
          <span>{description}</span>
        </div>
        {note && (
          <div className={styles['email-application-note']}>
            <span>{note}</span>
          </div>
        )}
        {label && (
          <div className={styles['email-application-action-container']}>
            <Button
              onClick={onClick}
              label={label}
              kind="secondary"
              color="greenblue"
              minWidth="220px"
            />
          </div>
        )}
      </div>
    </Modal>
  );
};

export default memo(ModalEmailApplicationStatus);
