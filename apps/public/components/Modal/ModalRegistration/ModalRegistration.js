import { Modal, Registration } from '../../index';
import styles from './ModalRegistration.module.scss';
import { memo } from 'react';

const ModalRegistration = ({ onClose, showAgentRegistrationModal }) => {
  return (
    <Modal
      backdropClose
      onClose={onClose}
      showDefaultLayout={false}
      classNameContainer={styles['registration-modal']}
      hideMobileCloseButton
      showMobileBorderRadius={false}
      floatCloseButton
    >
      <Registration
        isModal
        closeModal={onClose}
        showAgent={showAgentRegistrationModal}
        isNewRegistrationFlow={false}
      >
        <h1>Sign up with allcasting</h1>
      </Registration>
    </Modal>
  );
};

export default memo(ModalRegistration);
