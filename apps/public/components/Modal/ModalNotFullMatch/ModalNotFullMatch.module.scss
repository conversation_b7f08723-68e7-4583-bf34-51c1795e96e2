@import '../../../styles/variables';
@import '../../../styles/mixins';

.content {
  text-align: center;

  @include tablet {
    padding: 0 $space-20;
  }
}

.title {
  font-weight: 700;
  font-size: 18px;
  line-height: 27px;
  margin-bottom: $space-35;

  @include tablet {
    font-size: 24px;
    line-height: 36px;
  }
}

.criteria-list {
  background: rgba($red-60, 0.06);
  border-radius: 5px;
  max-width: 420px;
  margin: 0 auto $space-20;
  display: flex;
  justify-content: center;
  padding: $space-35 $space-20;
  text-align: left;
}

.criteria {
  display: flex;
  flex-wrap: wrap;

  &:not(:last-child) {
    margin-bottom: $space-10;
  }

  strong {
    white-space: nowrap;
  }
}

.last-value {
  margin-right: 21px;
  position: relative;
  display: inline-block;
}

.icon {
  display: inline-block;
  position: absolute;
  right: -21px;
  top: 6.5px;
}

.value {
  color: $red-60;
  display: flex;
  gap: $space-15;
  flex-wrap: nowrap;
  align-items: center;
}

.link {
  color: $blue-100;
}

.action-text {
  padding: 0 $space-20;
}

.not-full-match-modal {
  width: 100%;
  border-radius: 5px;
  padding: $space-20 $space-15 $space-50;
  position: fixed;
  bottom: 0;

  @include tablet {
    position: relative;
    bottom: auto;
    max-width: 600px;
  }
}

.buttons {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: $space-20;
  margin-bottom: $space-20 * 1.5;

  @include tablet {
    flex-direction: row;
  }
}

.button {
  min-width: 220px;

  @include tablet {
    min-width: 180px;

    &:first-child {
      order: 2;
    }
  }
}
