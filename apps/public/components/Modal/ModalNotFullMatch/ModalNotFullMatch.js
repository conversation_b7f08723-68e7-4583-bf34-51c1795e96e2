import styles from './ModalNotFullMatch.module.scss';
import { Button, Modal } from '../../index';
import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import cn from 'classnames';

const ModalNotFullMatch = ({ onClose, criteria, onApply, showButtons }) => {
  return (
    <Modal
      backdropClose
      onClose={onClose}
      showAnimation={false}
      showMobileBorderRadius={false}
      showDefaultLayout={false}
      classNameContainer={styles['not-full-match-modal']}
      dataCy="not-full-match-modal"
    >
      <div className={styles.content}>
        <div className={styles.title}>
          Unfortunately your application does not match the requirements for
          this role.
        </div>
        <div className={styles['criteria-list']}>
          <div>
            {criteria.age && (
              <div className={styles.criteria}>
                <strong>Age:</strong>&nbsp;
                <span className={cn(styles.value, styles['last-value'])}>
                  {criteria.age} y.o.{' '}
                  <Image
                    className={styles.icon}
                    src="/assets/icons/icon-close-2.svg"
                    width={13}
                    height={12}
                    alt="icon"
                  />
                </span>
              </div>
            )}
            {criteria.ethnicity && (
              <div className={styles.criteria}>
                <strong>Ethnicity:</strong>&nbsp;
                <div className={styles.value}>
                  <span>
                    {criteria.ethnicity.map((ethnicity, i) => (
                      <span
                        className={
                          i === criteria.ethnicity.length - 1
                            ? styles['last-value']
                            : ''
                        }
                        key={i}
                      >
                        {ethnicity.name +
                          (i !== criteria.ethnicity.length - 1 ? ', ' : '')}
                        {i === criteria.ethnicity.length - 1 && (
                          <Image
                            className={styles.icon}
                            src="/assets/icons/icon-close-2.svg"
                            width={13}
                            height={12}
                            alt="icon"
                          />
                        )}
                      </span>
                    ))}
                  </span>
                </div>
              </div>
            )}
            {criteria.gender && (
              <div className={styles.criteria}>
                <strong>Gender:</strong>&nbsp;
                <span className={cn(styles.value, styles['last-value'])}>
                  {criteria.gender}{' '}
                  <Image
                    className={styles.icon}
                    src="/assets/icons/icon-close-2.svg"
                    width={13}
                    height={12}
                    alt="icon"
                  />
                </span>
              </div>
            )}
            {criteria.location && (
              <div className={styles.criteria}>
                <strong>Location:</strong>&nbsp;
                <span className={cn(styles.value, styles['last-value'])}>
                  {criteria.location}{' '}
                  <Image
                    className={styles.icon}
                    src="/assets/icons/icon-close-2.svg"
                    width={13}
                    height={12}
                    alt="icon"
                  />
                </span>
              </div>
            )}
          </div>
        </div>
        {showButtons && (
          <div className={styles.buttons}>
            <Button
              onClick={onApply}
              label={'Apply anyway'}
              kind="secondary"
              color="blue"
              className={styles.button}
              dataCy="apply-anyway-btn"
            />
            <Button
              onClick={onClose}
              label={'Cancel'}
              kind="primary"
              color="blue"
              className={styles.button}
            />
          </div>
        )}

        <div className={styles['action-text']}>
          Feel free to apply to other{' '}
          <Link href={'/castingcalls'} className={styles.link}>
            great casting calls
          </Link>{' '}
          in your area.
        </div>
      </div>
    </Modal>
  );
};

export default ModalNotFullMatch;
