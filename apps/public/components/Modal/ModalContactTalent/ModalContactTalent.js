import React, { memo } from 'react';
import { <PERSON><PERSON>, Modal } from '../../index';
import styles from './ModalContactTalent.module.scss';
import { useRouter } from 'next/router';

function ModalContactTalent({ onClose }) {
  const router = useRouter();
  const navigate = async () => {
    await router.push('/login');
  };

  return (
    <Modal
      backdropClose
      onClose={onClose}
      classNameContainer={styles['contact-talent-modal']}
    >
      <div className={styles.content}>
        <div className={styles.title}>Log in to your account</div>
        <p>Only active casting directors can contact talent.</p>
        <Button
          minWidth={'250px'}
          color={'green-gradient'}
          onClick={navigate}
          label="LOG IN"
        />
        <p>
          <a href={'/register/professional'}>Create an account</a>
        </p>
      </div>
    </Modal>
  );
}

export default memo(ModalContactTalent);
