import React, { memo } from 'react';
import styles from './PhotoAnalyzer.module.scss';
import { Button } from '../../../index';
import Image from 'next/image';
import { Amp } from '../../../../services/amp';
import useTrackElementActions from '../../../../utils/useTrackElementActions';

const PhotoAnalyzer = () => {
  const { onTrackClick } = useTrackElementActions({
    name: 'Photo analyzer popup',
    type: Amp.element.type.popup,
    context: Amp.element.context.ctaPopup,
    autoTrackEnabled: true,
  });

  const onClick = () => {
    onTrackClick();

    window.location.href = `${process.env.redirectTalentUrl}/upgrade`;
  };

  return (
    <div className={styles.container}>
      <div>
        <Image
          src="/assets/guide-unit-modals/upload-photos.webp"
          alt="Apply to casting calls"
          width={244}
          height={138}
          className={styles.image}
        />
      </div>
      <div className={styles.title}>Add More Photos and Get Feedback!</div>
      <div className={styles.description}>
        Time to kick your photos up a notch with our Photo Analyzer tool!
        Enhance your photos, impress directors, boost success. <br />
        Check it out now!
      </div>
      <Button label="Get Started" minWidth="220px" onClick={onClick} />
    </div>
  );
};

export default memo(PhotoAnalyzer);
