import React, { memo } from 'react';
import { Button } from '../../../index';
import styles from './CompCard.module.scss';
import Image from 'next/image';
import { Amp } from '../../../../services/amp';
import useTrackElementActions from '../../../../utils/useTrackElementActions';

const CompCard = () => {
  const { onTrackClick } = useTrackElementActions({
    name: 'Comp card popup',
    type: Amp.element.type.popup,
    context: Amp.element.context.ctaPopup,
    autoTrackEnabled: true,
  });

  const onClick = () => {
    onTrackClick();

    window.location.href = `${process.env.redirectTalentUrl}/upgrade`;
  };

  return (
    <div className={styles.container}>
      <Image
        src="/assets/guide-unit-modals/comp-card.webp"
        alt="Apply to casting calls"
        width={155}
        height={128}
        className={styles.image}
      />
      <div className={styles.title}>Industry-approved Comp Card</div>
      <div className={styles.description}>
        Easily create a professional comp card showcasing your best photos and
        including all essential information. <br />
        Casting directors are going to love it!
      </div>
      <Button label="Get Started" minWidth="220px" onClick={onClick} />
    </div>
  );
};

export default memo(CompCard);
