@import '../../../../styles/variables';
@import '../../../../styles/mixins';

.image {
  cursor: pointer;
  margin-bottom: $space-10;

  @include desktop {
    margin-bottom: 0;
  }
}

.container {
  padding: $space-40 $space-20;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.title {
  font-size: 24px;
  font-weight: 800;
  margin: $space-10 0;
  line-height: 1.3;
  text-align: center;
  cursor: pointer;

  @include desktop {
    font-size: 30px;
  }
}

.description {
  color: $black;
  margin-bottom: $space-30;
  line-height: 1.6;
  text-align: center;
}
