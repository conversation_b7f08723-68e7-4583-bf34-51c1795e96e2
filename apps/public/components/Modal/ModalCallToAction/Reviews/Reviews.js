import React, { memo } from 'react';
import styles from './Reviews.module.scss';
import { Button } from '../../../index';
import { Amp } from '../../../../services/amp';
import useTrackElementActions from '../../../../utils/useTrackElementActions';

const Reviews = () => {
  const { onTrackClick } = useTrackElementActions({
    name: 'Review popup',
    type: Amp.element.type.popup,
    context: Amp.element.context.ctaPopup,
    autoTrackEnabled: true,
  });

  const onClick = () => {
    onTrackClick();

    window.location.href = `${process.env.redirectTalentUrl}/upgrade`;
  };

  return (
    <div className={styles.container}>
      <div className={styles.title}>Real People, Real Success</div>
      <div className={styles.description}>
        Upgrade now for career-boosting heights!
      </div>
      <div className={styles.review}>
        <div className={styles['review-block']}>
          <p>
            Absolutely love the frequent posting! Plenty of opportunities for
            everyone. Got my first extra lead a week later. A OWN (Delihah)
            credit added to my portfolio.
          </p>
          <div className={styles['reviewer-photo']} />
        </div>
        <div className={styles.reviewer}>
          <span className={styles.name}>Keila Williams</span>
          <span>Talent</span>
        </div>
      </div>
      <Button label="Get Started" minWidth="220px" onClick={onClick} />
    </div>
  );
};

export default memo(Reviews);
