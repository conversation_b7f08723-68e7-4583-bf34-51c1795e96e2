@import '../../../../styles/variables';
@import '../../../../styles/mixins';

.image {
  cursor: pointer;
  margin-bottom: $space-10;

  @include desktop {
    margin-bottom: 0;
  }
}

.container {
  padding: $space-40 $space-20;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.title {
  font-size: 24px;
  font-weight: 800;
  margin: $space-10 0;
  line-height: 1.3;
  text-align: center;
  cursor: pointer;

  @include desktop {
    font-size: 30px;
  }
}

.description {
  color: $black;
  margin-bottom: $space-30;
  line-height: 1.6;
  text-align: center;
}

.review {
  margin: (-$space-5) 0 $space-25;

  @include desktop {
    margin: 3px 72px $space-30;
  }
}

.review-block {
  position: relative;
  border-radius: 10px;
  background: $grey-20;
  padding: $space-25 $space-20;

  @include desktop {
    padding: 32px $space-20 $space-35;
  }

  p {
    line-height: 1.6;
    font-size: 14px;
    font-weight: 300;

    @include desktop {
      line-height: 1.7;
      font-size: 16px;
    }
  }
}

.reviewer-photo {
  position: absolute;
  bottom: -50px;
  left: 6px;
  width: 64px;
  height: 64px;
  border: 7px solid $white;
  border-radius: 50%;
  background-position: 50% 50%;
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url('#{$assetUrl}/assets/homepage/reviews/Keila-Williams.jpeg');

  @include desktop {
    bottom: -55px;
    left: 8px;
    width: 74px;
    height: 74px;
  }
}

.reviewer {
  margin-left: 75px;
  font-size: 12px;
  font-weight: 300;
  line-height: 1.875;

  @include desktop {
    margin-left: 90px;
    font-size: 14px;
  }

  & .name {
    font-size: 14px;
    font-weight: 700;
    display: block;

    @include desktop {
      font-size: 16px;
    }
  }
}
