import React, { memo } from 'react';
import styles from './ModalCallToAction.module.scss';
import { Modal } from '../../index';
import DynamicSale from './DynamicSale/DynamicSale';
import PhotoAnalyzer from './PhotoAnalyzer/PhotoAnalyzer';
import CompCard from './CompCard/CompCard';
import Reviews from './Reviews/Reviews';
import { CTA_MODAL_TYPE, useCTAModal } from '../../../contexts/CTAModalContext';

const ModalCallToAction = () => {
  const { onClose, activeSlot } = useCTAModal();

  return (
    <>
      {activeSlot && (
        <Modal
          backdropClose
          onClose={onClose}
          showDefaultLayout={false}
          floatCloseButton
          classNameContainer={styles.container}
          classNameCloseButton={
            activeSlot.type === CTA_MODAL_TYPE.Apply
              ? styles['close-button']
              : ''
          }
        >
          <div>
            {activeSlot.type === CTA_MODAL_TYPE.Apply && <DynamicSale />}
            {activeSlot.type === CTA_MODAL_TYPE.PhotoAnalyzer && (
              <PhotoAnalyzer />
            )}
            {activeSlot.type === CTA_MODAL_TYPE.CompCard && <CompCard />}
            {activeSlot.type === CTA_MODAL_TYPE.Reviews && <Reviews />}
          </div>
        </Modal>
      )}
    </>
  );
};

export default memo(ModalCallToAction);
