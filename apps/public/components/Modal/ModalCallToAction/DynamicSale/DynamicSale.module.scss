@import '../../../../styles/variables';
@import '../../../../styles/mixins';

.image {
  width: 100%;
  cursor: pointer;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: auto;
}

.container {
  padding: 0 $space-20 $space-25;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.title {
  font-size: 30px;
  font-weight: 800;
  margin: $space-10 0;
  line-height: 1.3;
  text-align: center;
  cursor: pointer;

  @include desktop {
    font-size: 42px;
  }
}

.price {
  margin-bottom: $space-25;
  padding: 0 $space-5;
  background-color: #edddf0;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 150%;
  cursor: pointer;
}

.description {
  color: $black;
  margin-bottom: $space-30;
  line-height: 1.6;
  text-align: center;
}
