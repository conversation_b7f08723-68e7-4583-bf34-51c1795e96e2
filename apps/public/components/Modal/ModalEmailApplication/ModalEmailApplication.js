import React, { memo, useEffect, useRef, useState } from 'react';
import { Button, EmailProfilePreview, Modal } from '../../index';
import styles from './ModalEmailApplication.module.scss';
import { formatEmailURI } from '../../../utils/formatEmailURI';
import <PERSON><PERSON> from 'react-lottie-player';
import tutorial from '../../../public/assets/modal-email-application/email-application-tutorial.json';
import { useNotifications } from '../../../contexts/NotificationContext';
import Link from 'next/link';

const ModalEmailApplication = ({
  onClose,
  email,
  emailSubject,
  castingCallTitle,
  roleTitle,
  profile,
}) => {
  const [step, setStep] = useState(1);
  const emailLinkRef = useRef(null);
  const { setNotification } = useNotifications();

  useEffect(() => {
    return () => setNotification({ message: null });
  }, [setNotification]);

  const goToNextStep = () => {
    setStep(step + 1);
  };

  const applyToRole = () => {
    const currentEmailLinkRef = emailLinkRef.current;

    if (currentEmailLinkRef) {
      currentEmailLinkRef.click();
    }
  };

  const copyProfileToClipboard = () => {
    const template = document.getElementById('profile-preview-template');

    let range, selection;

    if (document.body.createTextRange) {
      range = document.body.createRange();
      range.moveToElementText(template);
      range.select();
    } else if (window.getSelection) {
      selection = window.getSelection();
      range = document.createRange();
      range.selectNodeContents(template);
      selection.removeAllRanges();
      selection.addRange(range);
    }

    document.execCommand('copy');
    window.getSelection().removeAllRanges();

    setNotification({
      type: 'success',
      message: 'Profile has been copied',
      timeout: '3000',
      showOnModal: true,
    });

    goToNextStep();
  };

  return (
    <>
      <Modal
        onClose={onClose}
        backdropClose
        classNameContainer={styles['email-application-modal']}
        dataCy="email-application-modal"
      >
        <div className={styles['email-application-step-counter']}>
          <span>STEP {step}/3</span>
        </div>
        {step === 1 && (
          <div className={styles['email-application-step']}>
            <div className={styles['email-application-step-title-container']}>
              <span>Please,</span>
              <span className={styles['email-application-step-title']}>
                Check your profile
              </span>
            </div>
            <div className={styles['email-application-description']}>
              <span>
                To apply to this role, you need to send an email with your
                digital profile.
              </span>
              <span>
                Digital profile standards: <b>main photos</b>, <b>appearance</b>
                , <b>contact info</b>.
              </span>
            </div>
            <div className={styles['email-application-action-container']}>
              <Button
                onClick={copyProfileToClipboard}
                label="Copy profile"
                kind="secondary"
                color="greenblue"
                minWidth="280px"
              />
            </div>
          </div>
        )}
        {step === 2 && (
          <div className={styles['email-application-step']}>
            <div className={styles['email-application-step-title-container']}>
              <span className={styles['email-application-step-title']}>
                Remember
              </span>
            </div>
            <span>
              You have to paste your digital profile in the email text.
            </span>
            <div className={styles['lottie-player-container']}>
              <Lottie
                className={styles['lottie-player']}
                loop
                animationData={tutorial}
                play
              />
            </div>
            <div className={styles['email-application-action-container']}>
              <Button
                onClick={goToNextStep}
                label="Next"
                kind="secondary"
                color="greenblue"
                minWidth="280px"
              />
            </div>
          </div>
        )}
        {step === 3 && (
          <div className={styles['email-application-step']}>
            <div className={styles['email-application-step-title-container']}>
              <span className={styles['email-application-step-title']}>
                Hot tip
              </span>
              <span>
                The casting director expects a clear and simple email subject
                line. If the subject line was specified in the casting call
                description, use exactly what was asked.
              </span>
            </div>
            <div className={styles['email-application-phone-container']}>
              <Link
                prefetch={false}
                href={formatEmailURI(
                  email,
                  emailSubject,
                  castingCallTitle,
                  roleTitle,
                )}
                ref={emailLinkRef}
                onClick={onClose}
                className={styles['email-application-phone-link']}
                target="_blank"
                rel="nofollow"
              >
                {email}
              </Link>
            </div>
            <div className={styles['email-application-action-container']}>
              <Button
                onClick={applyToRole}
                label="Apply"
                color="green-gradient"
                minWidth="280px"
              />
            </div>
          </div>
        )}
      </Modal>
      <div className={styles['email-application-profile-template']}>
        <EmailProfilePreview
          previewId="profile-preview-template"
          profile={profile}
        />
      </div>
    </>
  );
};

export default memo(ModalEmailApplication);
