@import '../../../styles/variables';

.email-application-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: $space-20;
}

.email-application-step-title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.email-application-step-title {
  font-size: 30px;
  font-weight: 700;
}

.email-application-action-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.email-application-step-counter {
  position: absolute;
  top: 20px;
  left: 20px;
  color: $grey-80;
  font-size: 14px;
  font-weight: 300;
}

.email-application-phone-container {
  background-color: $grey-10;
  padding: $space-20;
  text-align: center;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.email-application-phone-link {
  color: inherit;
  position: relative;
  display: inline-block;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: $space-5;
  background: $gradient-light-blue no-repeat center 25px;
  background-size: calc(100% - 10px) 2px;
  transition:
    background-size 0.2s ease-out,
    background-position 0.2s ease-out;

  &:hover {
    text-decoration: none;
    background-size: 100% 31px;
    background-position: 0 1px;
  }
}

.lottie-player-container {
  display: flex;
  width: 100%;
  justify-content: center;
}

.lottie-player {
  width: 600px;
  height: 300px;
}

.email-application-description {
  display: flex;
  flex-direction: column;
}

.email-application-profile-template {
  position: absolute;
  opacity: 0;
  z-index: 0;
}

.email-application-modal {
  max-width: 768px;
  width: 100%;
}
