import BreadcrumbsItem from '../BreadcrumbsItem/BreadcrumbsItem';
import styles from './Breadcrumbs.module.scss';
import cn from 'classnames';
import { useEffect, useRef } from 'react';
import { useAuth } from '../../contexts/AuthContext';

const Breadcrumbs = ({ crumbs }) => {
  const shadowStart = useRef();
  const shadowEnd = useRef();
  const scrollableBlock = useRef();
  const { isAuthenticated } = useAuth();

  const handleShadowEffect = () => {
    if (!scrollableBlock.current) return;
    const scrollPosition = scrollableBlock.current.scrollLeft;
    const width = scrollableBlock.current.offsetWidth;
    const scrollWidth = scrollableBlock.current.scrollWidth;

    if (scrollPosition > 0) {
      shadowStart.current.style.display = 'block';
    } else {
      shadowStart.current.style.display = 'none';
    }

    if (Math.ceil(scrollPosition + width) >= scrollWidth) {
      shadowEnd.current.style.display = 'none';
    } else {
      shadowEnd.current.style.display = 'block';
    }
  };

  useEffect(() => {
    handleShadowEffect();
  }, []);

  useEffect(() => {
    window.addEventListener('resize', handleShadowEffect);

    return () => window.removeEventListener('resize', handleShadowEffect);
  }, []);

  return (
    <div className={styles['breadcrumbs']}>
      <div
        className={styles['breadcrumbs-container']}
        ref={scrollableBlock}
        onScroll={handleShadowEffect}
      >
        <div className={styles['breadcrumb-list']}>
          <BreadcrumbsItem href={isAuthenticated ? '/castingcalls' : '/'} first>
            allcasting
          </BreadcrumbsItem>
          {crumbs.map(({ href, text }, i, arr) => (
            <BreadcrumbsItem
              key={i}
              last={i + 1 === arr.length}
              href={href}
              onExpand={handleShadowEffect}
            >
              {text}
            </BreadcrumbsItem>
          ))}
        </div>
      </div>
      <div
        className={cn(styles['shadow'], styles['start'])}
        ref={shadowStart}
      ></div>
      <div
        className={cn(styles['shadow'], styles['end'])}
        ref={shadowEnd}
      ></div>
    </div>
  );
};

export default Breadcrumbs;
