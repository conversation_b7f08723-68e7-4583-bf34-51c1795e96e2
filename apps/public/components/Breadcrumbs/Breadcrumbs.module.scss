@import 'styles/mixins';
@import 'styles/variables';

.breadcrumbs {
  position: relative;
}

.breadcrumbs-container {
  font-size: 12px;
  max-width: 100%;
  overflow-x: scroll;
  -ms-overflow-style: none;
  scrollbar-width: none;
  padding: $space-20 0 0;
  margin-bottom: $space-20;

  &::-webkit-scrollbar {
    display: none;
  }

  @include desktop {
    padding: $space-25 0;
    margin-bottom: 0;
  }
}

.breadcrumb-list {
  white-space: nowrap;
  width: max-content;
  display: flex;
  align-items: center;
}

.shadow {
  position: absolute;
  background: $gradient-breadcrumb-shadow;
  width: 80px;
  height: 100%;
  top: 0;

  &.start {
    transform: rotate(-180deg);
    left: 0;
    display: none;
  }

  &.end {
    right: 0;
    display: none;
  }
}
