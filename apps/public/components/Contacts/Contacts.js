import { memo } from 'react';
import styles from './Contacts.module.scss';
import { formatPhoneNumber } from '../../utils/formatPhoneNumber';

const supportDetails = {
  phone: '18006058590',
  email: '<EMAIL>',
  iosEmail: '<EMAIL>',
  androidEmail: '<EMAIL>',
  mailingAddress: `AllCasting 300 Delaware Ave, #210
        Wilmington, DE 19801`,
  workingTime: 'Monday-Friday 8am-5pm PST',
};

const supportDetailsConcierge = {
  phone: '18009135480',
  email: '<EMAIL>',
  mailingAddress: `AllCasting 300 Delaware Ave, #210
        Wilmington, DE 19801`,
  workingTime: 'Monday-Friday 8am-5pm PST',
};

const Contacts = () => {
  return (
    <section className={styles['contact-section']}>
      <span className={styles['main-title']}>Contact Support</span>
      <div className={styles['title-container']}>
        <span className={styles.title}>Mailing Address</span>
      </div>

      <div className={styles['contact-info-container']}>
        <span>{supportDetails.mailingAddress}</span>
      </div>

      <div className={styles['title-container']}>
        <span className={styles.title}>Talent Support</span>
      </div>

      <div className={styles['contact-info-container']}>
        <a
          href={`tel:+${supportDetails.phone}`}
          className={styles['contact-link']}
        >
          {formatPhoneNumber(supportDetails.phone)}
        </a>
        <span>{supportDetails.workingTime}</span>
        <a
          href={`mailto:${supportDetails.email}`}
          className={styles['contact-link']}
        >
          {supportDetails.email}
        </a>
      </div>

      <div className={styles['title-container']}>
        <span className={styles.title}>Casting Director Support</span>
      </div>

      <div className={styles['contact-info-container']}>
        <a
          href={`tel:+${supportDetailsConcierge.phone}`}
          className={styles['contact-link']}
        >
          {formatPhoneNumber(supportDetailsConcierge.phone)}
        </a>
        <span>{supportDetailsConcierge.workingTime}</span>
        <a
          href={`mailto:${supportDetailsConcierge.email}`}
          className={styles['contact-link']}
        >
          {supportDetailsConcierge.email}
        </a>
      </div>
    </section>
  );
};

export default memo(Contacts);
