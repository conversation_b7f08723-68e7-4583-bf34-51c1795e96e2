import cn from 'classnames';
import styles from './ArrowButton.module.scss';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';

export const useArrowButtons = (emblaApi) => {
  const [prevBtnDisabled, setPrevBtnDisabled] = useState(true);
  const [nextBtnDisabled, setNextBtnDisabled] = useState(true);

  const onPrevButtonClick = useCallback(() => {
    if (!emblaApi) return;
    emblaApi.scrollPrev();
  }, [emblaApi]);

  const onNextButtonClick = useCallback(() => {
    if (!emblaApi) return;
    emblaApi.scrollNext();
  }, [emblaApi]);

  const onSelect = useCallback((emblaApi) => {
    setPrevBtnDisabled(!emblaApi.canScrollPrev());
    setNextBtnDisabled(!emblaApi.canScrollNext());
  }, []);

  useEffect(() => {
    if (!emblaApi) return;

    onSelect(emblaApi);
    emblaApi.on('reInit', onSelect).on('select', onSelect);
  }, [emblaApi, onSelect]);

  return {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  };
};

export const ArrowButtonRound = ({
  onClick,
  disabled,
  direction = 'right', // right | left
}) => {
  return (
    <div className={cn(styles['container-round'], styles[direction])}>
      <button
        disabled={disabled}
        className={cn(styles['arrow-round'], styles[direction])}
        onClick={onClick}
      >
        <Image
          className={cn(styles['arrow-round-icon'], styles[direction])}
          src={'/assets/icons/icon-angle-2.svg'}
          width={10}
          height={20}
          alt="icon-arrow"
        />
      </button>
    </div>
  );
};

export const ArrowButton = ({
  onClick,
  disabled,
  direction = 'right', // right | left
  className = '',
}) => {
  return (
    <div className={cn(styles.container, styles[direction], styles[className])}>
      <button
        className={cn(styles.arrow, styles[direction])}
        disabled={disabled}
        onClick={onClick}
      >
        <Image
          className={cn(styles['arrow-icon'], styles[direction])}
          src={'/assets/icons/icon-angle-2.svg'}
          width={30}
          height={45}
          alt="icon-arrow"
        />
      </button>
    </div>
  );
};
