@import '../../styles/mixins';
@import '../../styles/variables';

// round arrow
.container-round {
  position: absolute;
  top: 0;
  height: 100%;
  width: 44px;
  display: flex;
  align-items: center;
  z-index: 2;

  &.right {
    right: -12px;
  }

  &.left {
    left: -12px;
  }
}

.arrow-round {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  border: none;
  border-radius: 50%;
  width: 42px;
  height: 42px;
  background-color: $white;
  box-shadow: $shadow-form-control;
  padding: 0;

  &:hover {
    box-shadow: $shadow-carousel-arrow-hover;
    transform: scale(1.1);
  }

  &:disabled {
    opacity: 0.8;
  }
}

.arrow-round-icon {
  &.left {
    transform: rotate(180deg);
    margin-right: 2px;
  }

  &.right {
    margin-left: 2px;
  }
}

// default arrow
.container {
  position: absolute;
  top: 0;
  height: 100%;
  width: 44px;
  display: flex;
  align-items: center;
  z-index: 2;

  &.right {
    right: 0;
  }

  &.left {
    left: 0;
  }
}

.arrow-icon {
  transition: all ease-in-out 0.3s;
  opacity: 0.6;

  &.left {
    transform: rotate(180deg);
  }
}

.arrow {
  appearance: none;
  touch-action: manipulation;
  margin: 0;
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: all ease-in-out 0.3s;
  width: 44px;
  display: flex;
  align-items: center;
  height: 100%;
  position: absolute;
  z-index: 2;
  top: 0;
  background-repeat: repeat-y;
  padding: 0;

  &.left {
    padding-left: 6px;
  }

  &.right {
    justify-content: flex-end;
    padding-right: 6px;
  }
}

.carousel-promos-mobile {
  .arrow-icon {
    width: 15px;
  }

  .arrow {
    filter: invert(1);

    &.left {
      background-image: $gradient-metallic-green-left;
    }

    &.right {
      background-image: $gradient-metallic-green-right;
    }
  }
}

.carousel-profile-gallery,
.carousel-profile-preview {
  .arrow-icon {
    filter: invert(1);
  }
}

.carousel-profile-preview {
  &.right {
    right: 85px;
  }

  .arrow {
    width: 130px;

    &.left {
      background-image: $gradient-black-left;
    }

    &.right {
      background-image: $gradient-black-right;
    }
  }
}

.carousel-promos {
  &.right {
    right: -40px;
  }

  &.left {
    left: -40px;
  }

  .arrow-icon {
    width: 15px;
  }

  .arrow {
    padding: 0;
    width: 40px;
    opacity: 0.6;
  }
}

.arrow:hover {
  .arrow-icon {
    opacity: 1;
  }
}

.opacity-arrow {
  width: calc(15% - 16px);
  justify-content: center;

  &:disabled {
    .arrow-icon {
      visibility: hidden;
    }
  }
}
