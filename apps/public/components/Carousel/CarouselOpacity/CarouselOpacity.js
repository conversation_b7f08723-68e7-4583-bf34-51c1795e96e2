import { memo, useCallback, useEffect, useRef } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import styles from './CarouselOpacity.module.scss';
import { ArrowButton, useArrowButtons } from '../ArrowButton';

const TWEEN_FACTOR_BASE = 0.84;

const getNumberWithinRange = (number, min, max) =>
  Math.min(Math.max(number, min), max);

const getTweenValue = (value, diffToTarget, tweenValue) => {
  switch (value) {
    case 2:
      return 2 - Math.abs(diffToTarget * tweenValue);
    default:
      return 1 - Math.abs(diffToTarget * tweenValue);
  }
};

const CarouselOpacity = ({ children, arrowNavigationEnabled = false }) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true });
  const tweenFactor = useRef(0);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = useArrowButtons(emblaApi);

  const setTweenFactor = useCallback((emblaApi) => {
    tweenFactor.current = TWEEN_FACTOR_BASE * emblaApi.scrollSnapList().length;
  }, []);

  const tweenOpacity = useCallback(
    (emblaApi, eventName = '') => {
      const engine = emblaApi.internalEngine();
      const scrollProgress = emblaApi.scrollProgress();
      const slidesInView = emblaApi.slidesInView();
      const isScrollEvent = eventName === 'scroll';

      emblaApi.scrollSnapList().forEach((scrollSnap, snapIndex) => {
        let diffToTarget = scrollSnap - scrollProgress;
        const slidesInSnap = engine.slideRegistry[snapIndex];

        slidesInSnap.forEach((slideIndex) => {
          if (isScrollEvent && !slidesInView.includes(slideIndex)) return;

          if (engine.options.loop) {
            engine.slideLooper.loopPoints.forEach((loopItem) => {
              const target = loopItem.target();

              if (slideIndex === loopItem.index && target !== 0) {
                const sign = Math.sign(target);

                if (sign === -1) {
                  diffToTarget = scrollSnap - (1 + scrollProgress);
                }

                if (sign === 1) {
                  diffToTarget = scrollSnap + (1 - scrollProgress);
                }
              }
            });
          }

          const tweenValue = getTweenValue(
            children.length,
            diffToTarget,
            tweenFactor.current,
          );
          const opacity = getNumberWithinRange(tweenValue, 0, 1);

          emblaApi.slideNodes()[slideIndex].style.opacity = opacity.toString();
          emblaApi.slideNodes()[slideIndex].style.pointerEvents =
            opacity < 0.99 && arrowNavigationEnabled ? 'none' : 'auto';
        });
      });
    },
    [arrowNavigationEnabled, children.length],
  );

  useEffect(() => {
    if (!emblaApi) return;

    setTweenFactor(emblaApi);
    tweenOpacity(emblaApi);
    emblaApi
      .on('reInit', setTweenFactor)
      .on('reInit', tweenOpacity)
      .on('scroll', tweenOpacity)
      .on('slideFocus', tweenOpacity);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [emblaApi, tweenOpacity]);

  return (
    <div className={styles.embla}>
      <div className={styles['embla-viewport']} ref={emblaRef}>
        <div className={styles['embla-container']}>
          {children.map((slide, index) => (
            <div className={styles['embla-slide']} key={index}>
              {slide}
            </div>
          ))}
        </div>
        {arrowNavigationEnabled && (
          <>
            <ArrowButton
              onClick={onPrevButtonClick}
              disabled={prevBtnDisabled}
              direction="left"
              className="opacity-arrow"
            />
            <ArrowButton
              onClick={onNextButtonClick}
              disabled={nextBtnDisabled}
              direction="right"
              className="opacity-arrow"
            />
          </>
        )}
      </div>
    </div>
  );
};

export default memo(CarouselOpacity);
