@import '../../styles/mixins';
@import '../../styles/variables';

.embla {
  position: relative;
  margin-left: auto;
  margin-right: auto;
}

.embla-viewport {
  overflow: hidden;
  width: 100%;
}

.embla-container {
  display: flex;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

.embla-slide {
  position: relative;
  flex: 0 0 100%;
  cursor: grab;

  &:active {
    cursor: grabbing;
  }
}

.paginator-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: $space-30;
}

// Custom overrides

.carousel-showcase {
  .embla-container {
    display: flex;
    flex-direction: row;
    height: auto;
    margin-left: calc(1rem * -1);
  }

  .embla-slide {
    min-width: 0;
    position: relative;
    flex: 0 0 44%;
    padding-left: 1rem;

    @include mobile {
      flex: 0 0 28%;
    }

    @include mobilexl {
      flex: 0 0 24%;
    }

    @include tablet {
      flex: 0 0 21%;
    }
  }

  .embla-slide-single {
    flex: 0 0 100%;
  }
}

.carousel-classroom {
  .embla-container {
    display: flex;
    flex-direction: row;
    height: auto;
  }

  .embla-slide {
    min-width: 0;
    position: relative;
    flex: 0 0 44%;

    @include mobile {
      flex: 0 0 28%;
    }

    @include mobilexl {
      flex: 0 0 24%;
    }
  }
}

.carousel-help {
  margin: 0 (-$space-20) (-$space-40);
}

.blog-carousel {
  margin: 0 (-$space-20) (-$space-40);
}

.carousel-quote {
  margin: 0 (-$space-20) (-$space-40);

  .embla-slide {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .paginator-container {
    padding-top: $space-20;
  }

  .arrow-navigation {
    display: none;

    > div {
      padding-bottom: $space-40;
    }

    @include desktop {
      display: initial;
    }
  }
}

.carousel-talent {
  margin: 0 (-$space-20) (-$space-40);

  .embla-container {
    display: flex;
    flex-direction: row;
    height: auto;
    gap: $space-5;
    width: 100%;
  }

  .embla-slide {
    display: flex;
    min-width: 0;
    position: relative;
    flex: 0 0 190px;
    justify-content: center;
  }

  .arrow-navigation {
    display: none;

    > div {
      padding-bottom: $space-40;
    }

    @include mobile {
      display: initial;
    }
  }
}

.carousel-profile-gallery {
  position: unset;

  .embla-slide {
    cursor: initial;
  }
}

.carousel-attachment-gallery {
  position: unset;

  .embla-slide {
    cursor: initial;
    display: flex;
    align-items: center;
  }
}

.carousel-promos {
  margin: 0 $space-40;
}

.carousel-reviews-blog {
  .embla-container {
    display: flex;
    flex-direction: row;
    font-weight: 300;
    justify-content: stretch;
  }

  .embla-slide {
    display: flex;
    align-self: stretch;
    flex: 0 0 100%;
    padding: $space-20 $space-10;

    & a {
      font-weight: 300;
      text-decoration: none;
    }

    @include tablet {
      flex: 0 0 50%;
    }
  }

  .arrow-navigation {
    display: none;

    & img {
      margin: auto;
    }

    @include tablet {
      display: initial;
    }
  }
}

.carousel-reviews-info {
  .embla-container {
    display: flex;
    flex-direction: row;
    font-weight: 300;
    justify-content: stretch;
  }

  .embla-slide {
    display: flex;
    align-self: stretch;
    flex: 0 0 100%;
    padding: $space-20 $space-10;

    & a {
      font-weight: 300;
      text-decoration: none;
    }

    @include tablet {
      flex: 0 0 33.33%;
    }
  }

  .arrow-navigation {
    display: none;

    & img {
      margin: auto;
    }

    @include tablet {
      display: initial;
    }
  }
}

.carousel-imagelinks {
  .embla-container {
    display: flex;
    flex-direction: row;
    height: auto;
    justify-content: stretch;
  }

  .embla-slide {
    display: flex;
    align-self: stretch;
    flex: 0 0 100%;
    padding: $space-20 $space-10;

    & a {
      text-decoration: none;
      min-width: 100%;
    }

    @include mobilexl {
      flex: 0 0 50%;
    }

    @include desktop {
      flex: 0 0 25%;
    }
  }

  .arrow-navigation {
    display: none;

    & img {
      margin: auto;
    }

    @include tablet {
      display: initial;
    }
  }
}
