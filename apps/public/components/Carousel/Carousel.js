import React, { memo } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import styles from './Carousel.module.scss';
import Autoplay from 'embla-carousel-autoplay';
import cn from 'classnames';
import { DotButton, useDotButton } from './DotButton';
import { ArrowButton, ArrowButtonRound } from './ArrowButton';
import { useArrowButtons } from './ArrowButton';

const Carousel = ({
  draggable = true,
  delay = 5000, // autoplay next slide delay in milliseconds
  playOnInit = false, // start autoplay on init
  loop = false, // infinite loop
  slidesToScroll = 'auto', // number of slides grouped together (auto | number)
  dragFree = false, // drag free
  startIndex = 0, // initial slide index
  enablePagination = false,
  enableArrowNavigation = false,
  arrowType = 'default', // default | round
  className = '',
  children,
}) => {
  const [viewportRef, emblaApi] = useEmblaCarousel(
    {
      dragFree,
      loop,
      slidesToScroll,
      startIndex,
      draggable,
      speed: 10,
    },
    [
      Autoplay({
        rootNode: (emblaRoot) => emblaRoot.parentElement,
        delay,
        stopOnMouseEnter: true,
        stopOnInteraction: false,
        playOnInit,
      }),
    ],
  );

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = useArrowButtons(emblaApi);

  return (
    <>
      <div className={cn(styles.embla, styles[className])}>
        <div className={styles['embla-viewport']} ref={viewportRef}>
          <div className={styles['embla-container']}>
            {children.map((slide, index) => (
              <div
                className={cn(styles['embla-slide'], {
                  [styles['embla-slide-single']]: children.length === 1,
                })}
                key={index}
              >
                {slide}
              </div>
            ))}
          </div>
          {enableArrowNavigation && (
            <div className={styles['arrow-navigation']}>
              {arrowType === 'round' ? (
                <>
                  <ArrowButtonRound
                    onClick={onPrevButtonClick}
                    disabled={prevBtnDisabled}
                    direction="left"
                  />
                  <ArrowButtonRound
                    onClick={onNextButtonClick}
                    disabled={nextBtnDisabled}
                    direction="right"
                  />
                </>
              ) : (
                <>
                  <ArrowButton
                    onClick={onPrevButtonClick}
                    disabled={prevBtnDisabled}
                    direction="left"
                    className={className}
                  />
                  <ArrowButton
                    onClick={onNextButtonClick}
                    disabled={nextBtnDisabled}
                    direction="right"
                    className={className}
                  />
                </>
              )}
            </div>
          )}
        </div>
      </div>
      {enablePagination && (
        <div className={styles['paginator-container']}>
          {scrollSnaps.map((_, index) => (
            <DotButton
              key={index}
              selected={index === selectedIndex}
              onClick={() => onDotButtonClick(index)}
            />
          ))}
        </div>
      )}
    </>
  );
};

export default memo(Carousel);
