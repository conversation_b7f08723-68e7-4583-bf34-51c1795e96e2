@import '../../styles/mixins';
@import '../../styles/variables';

.payment-label {
  display: flex;
  font-weight: 700;
  font-size: 12px;
  padding: 7px 8px;
  text-align: right;
  border-radius: 40px;
  background: $red-60;
  color: $white;
  line-height: 1;

  &.inline {
    display: inline-flex;

    &:not(&:last-child) {
      margin-right: 8px;
    }

    &:not(&:first-child) {
      margin-left: 8px;
    }
  }
}
