import styles from './PaymentLabel.module.scss';
import React, { memo } from 'react';
import cn from 'classnames';
import { formatCurrency } from '../../utils/formatCurrency';

const PaymentLabel = ({
  paymentAmount,
  paymentPeriod,
  paymentCurrency,
  inline = false,
}) => (
  <span
    className={cn(styles['payment-label'], {
      [styles.inline]: inline,
    })}
  >
    {paymentAmount
      ? `$${
          formatCurrency(paymentAmount) +
          (paymentCurrency === 'CAD' ? ' CAD' : '')
        }/${paymentPeriod.toLowerCase()}`
      : 'TFP'}
  </span>
);

export default memo(PaymentLabel);
