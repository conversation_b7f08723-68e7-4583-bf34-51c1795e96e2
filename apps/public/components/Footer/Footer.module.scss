@import '../../styles/variables';
@import '../../styles/mixins';

.footer {
  display: flex;
  flex-direction: column;
  position: relative;
  background:
    url('#{$assetUrl}/assets/footer/triangles.svg') no-repeat right bottom,
    $gradient-port-gore $violet-80;
  background-size: auto 100%;
  padding: $space-20 0 $space-15;
  width: 100%;

  @include tablet {
    padding: $space-55 0 $space-15;
  }

  &.with-sidebar {
    @include xlarge {
      padding-left: $space-40;
      padding-right: $space-40;
    }
  }
}

.content {
  width: 100%;
  max-width: $content-max-width;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  flex-flow: row wrap;
}

.menu {
  flex-wrap: nowrap;
  margin-bottom: $space-50;
  display: none;

  @include tablet {
    display: flex;
    flex-grow: 1;
  }
}

.section {
  display: block;
  flex-wrap: nowrap;
  padding: 0 $space-20 $space-20;

  @include tablet {
    display: flex;
    margin-bottom: $space-50;
    padding: 0;
  }
}

.menu-item strong {
  display: block;
  line-height: 60%;
  margin-bottom: $space-25;
  font-size: 18px;
  font-weight: 700;
  color: $white;
}

.menu-item a {
  color: $blue-100;
  font-size: 16px;
  font-weight: 400;
  line-height: 1;

  &::after {
    content: '\2022';
    display: inline-block;
    margin: 0 8px;
    color: $grey-80;
    font-weight: 300;
    pointer-events: none;
  }

  &:last-child::after {
    content: '';
  }
}

.social {
  display: flex;
  width: 100%;
  justify-content: center;
  position: relative;
  align-items: center;
  text-align: center;
  margin: $space-30 0;
  order: 99;

  @include tablet {
    padding: 0 $space-20;
    order: unset;
    margin-bottom: $space-40;
    margin-top: 0;
  }

  @include desktop {
    padding: 0;
  }

  &::before {
    border-bottom: 1px solid rgba(255, 255, 255, 20%);
    content: '';
    flex-grow: 1;
    height: 1px;
    align-content: baseline;
    margin-right: 7px;
    display: none;

    @include tablet {
      display: block;
      order: 0;
    }
  }

  &::after {
    border-bottom: 1px solid rgba(255, 255, 255, 20%);
    content: '';
    flex-grow: 1;
    height: 1px;
    align-content: baseline;
    margin-left: 7px;
    display: none;

    @include tablet {
      display: block;
    }
  }
}

.social a {
  height: 35px;
  width: 35px;
  display: inline-block;
  margin: 0 7px;
  line-height: 0.5;

  &:hover {
    opacity: 0.7;
  }
}

.part {
  display: flex;
  flex-wrap: nowrap;
  justify-items: center;
}

.menu-item {
  flex: auto;
}

.part:first-child {
  flex: 30%;
  min-width: 215px;
  align-items: baseline;
  justify-content: left;
  margin-bottom: $space-20;

  @include tablet {
    justify-content: center;
    margin-bottom: 0;
  }
}

.part:last-child {
  flex: 70%;

  @include tablet {
    margin-right: 6%;
  }
}

.logo-small {
  display: inline-block;
  width: 93px;
  color: $white;

  @include tablet {
    display: none;
  }
}

.logo-big {
  display: none;
  color: $white;

  @include tablet {
    display: inline-block;
  }
}

.footer p {
  color: $grey-80;
  font-size: 14px;
  line-height: 24px;
  font-weight: 300;
  margin-bottom: $space-15;
}

.navigation {
  display: flex;
  flex-flow: row wrap;
  align-items: flex-start;
  overflow-wrap: break-word;
  justify-content: space-between;
  max-width: $content-max-width;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}

.navigation-content {
  box-sizing: border-box;
  display: block;
  max-width: 100%;
  padding-left: $space-20;
  padding-right: $space-20;
  font-size: 16px;
  font-weight: 300;
  line-height: 1.875;
  flex: 1 1 100%;
  min-width: 0;

  @include desktop {
    padding-left: 0;
    padding-right: 0;
  }
}

.navigation-content-links {
  display: block;
  background-color: $white;
  box-shadow: $shadow-card-container;
  padding: $space-20 $space-20 14px;
  color: $black;

  @include tablet {
    display: flex;
    padding: $space-20 $space-40 14px;
  }
}

.navigation-content-links p {
  font-size: 16px;
  font-weight: 300;
  line-height: 1.875;
  color: $black;
  margin: 0;
  opacity: 1;
  text-align: center;

  @include tablet {
    text-align: right;
  }
}

.navigation-content-links hr {
  display: block;
  height: 1px;
  background-color: $grey-80;
  opacity: 0.15;
  margin: 0 0 $space-20;
  border: none;
}

.navigation-content-links ul {
  display: flex;
  line-height: 1;
  text-transform: lowercase;
  list-style: none;
  margin: 0 0 $space-15;
  padding: 0;
  justify-content: center;
  flex-wrap: wrap;

  @include tablet {
    justify-content: left;
    flex-grow: 1;
  }
}

.navigation-content-links ul li {
  &::after {
    content: '\2022';
    padding: 0 3px;
    color: $grey-60;
    pointer-events: none;
    display: inline-block;
  }

  &:last-child::after {
    content: '';
  }
}

.legal-content-links-action {
  color: inherit;
  position: relative;
  display: inline-block;
  line-height: 28px;
  font-weight: 600;
  padding: 0 $space-5;
  background: $gradient-light-blue no-repeat center (28px - 3);
  background-size: 90% 2px;
  transition:
    background-size 0.2s ease-out,
    background-position 0.2s ease-out;
  cursor: pointer;

  &:hover {
    background-size: 100% (28px + 6);
    background-position: 0 1px;
    text-decoration: none;
  }
}
