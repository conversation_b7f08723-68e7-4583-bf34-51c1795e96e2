@import '../../styles/variables';

.profile-overview {
  display: flex;
  flex-direction: column;
  gap: $space-20;
}

.profile-overview-item-title {
  font-weight: 700;
  font-size: 18px;
  display: flex;
  align-items: center;
}

.profile-overview-item {
  line-height: 30px;
  font-weight: 300;
}

.profile-overview-list-item {
  line-height: 30px;
  font-weight: 300;
}

.profile-overview-list-item::after {
  content: '\2022';
  display: inline-block;
  padding-left: 0.25em;
  padding-right: 0.25em;
  opacity: 0.5;
  font-size: 1rem;
  height: 1rem;
  line-height: 1rem;
}

.profile-overview-list-item:last-of-type::after {
  content: '';
}

.profile-overview-icon-list {
  display: flex;
  gap: $space-20;
  flex-wrap: wrap;
  margin-top: $space-15;
  justify-content: flex-start;
}

.profile-overview-badge-list {
  display: flex;
  gap: $space-10;
  flex-wrap: wrap;
  margin-top: $space-15;
}

.profile-overview-badge {
  border-radius: 14px;
  background-color: $grey-20;
  padding: $space-5 $space-20;
  display: flex;
  align-items: center;
  text-transform: capitalize;
  font-weight: 300;
}

.profile-overview-badge-hidden {
  display: none;
}

.load-more-btn {
  cursor: pointer;
  color: $blue-100;
  font-weight: 400;
  text-decoration: none;
  background-color: transparent;
  border: none;
  font-size: 16px;
}

.profile-overview-item-container {
  scroll-margin-top: 80px;
}

.link {
  color: $blue-100;
  font-weight: 400;
  cursor: pointer;
}

.contacts,
.about {
  font-weight: 300;
}
