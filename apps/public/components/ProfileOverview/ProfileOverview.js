import { memo, useState } from 'react';
import styles from './ProfileOverview.module.scss';
import Image from 'next/image';
import { useProfileContext } from '../../contexts/ProfileContext';
import cn from 'classnames';
import { useAuth } from '../../contexts/AuthContext';

const ProfileOverview = ({ fontSize = '14px', contactTalentCallback }) => {
  const {
    age,
    ethnicity,
    gender,
    height,
    weight,
    eyeColor,
    hairColor,
    hipSize,
    dressSize,
    bust,
    cupSize,
    categories,
    skills,
    socialNetworks,
    resume,
    isAgent,
  } = useProfileContext();
  const { isAuthenticated } = useAuth();

  const [skillsLoaded, setSkillsLoaded] = useState(false);
  const tryContact = (event) => {
    if (!isAuthenticated) {
      contactTalentCallback(event);
    }
  };
  const renderTalent = () => {
    return (
      <>
        {categories?.length > 0 && (
          <div
            id="categories"
            className={styles['profile-overview-item-container']}
          >
            <span className={styles['profile-overview-item-title']}>
              Interested in:
            </span>
            <div className={styles['profile-overview-item-list']}>
              {categories.map(({ id, title }) => (
                <span key={id} className={styles['profile-overview-list-item']}>
                  {title}
                </span>
              ))}
            </div>
          </div>
        )}
        {age && (
          <div id="age" className={styles['profile-overview-item-container']}>
            <span className={styles['profile-overview-item-title']}>
              Age Range:
            </span>
            <span className={styles['profile-overview-item']}>
              {age < 2 ? 0 : age - 2}-{age + 2}
            </span>
          </div>
        )}

        {ethnicity && (
          <div
            id="ethnicity"
            className={styles['profile-overview-item-container']}
          >
            <span className={styles['profile-overview-item-title']}>
              Ethnicity:
            </span>
            <span className={styles['profile-overview-item']}>{ethnicity}</span>
          </div>
        )}

        {(gender ||
          height ||
          weight ||
          eyeColor ||
          hairColor ||
          hipSize ||
          dressSize ||
          bust ||
          cupSize) && (
          <div
            id="appearance"
            className={styles['profile-overview-item-container']}
          >
            <span className={styles['profile-overview-item-title']}>
              Appearance:
            </span>

            <div className={styles['profile-overview-list']}>
              {gender && (
                <span className={styles['profile-overview-list-item']}>
                  {gender}
                </span>
              )}

              {height && (
                <span className={styles['profile-overview-list-item']}>
                  {height} <strong>height</strong>
                </span>
              )}

              {weight && (
                <span className={styles['profile-overview-list-item']}>
                  {weight} <strong>weight</strong>
                </span>
              )}

              {eyeColor && (
                <span className={styles['profile-overview-list-item']}>
                  {eyeColor} <strong>eyes</strong>
                </span>
              )}

              {hairColor && (
                <span className={styles['profile-overview-list-item']}>
                  {hairColor} <strong>hair</strong>
                </span>
              )}

              {hipSize && (
                <span className={styles['profile-overview-list-item']}>
                  {hipSize} <strong>hip size</strong>
                </span>
              )}

              {dressSize !== null && (
                <span className={styles['profile-overview-list-item']}>
                  {dressSize} <strong>dress size</strong>
                </span>
              )}

              {bust && (
                <span className={styles['profile-overview-list-item']}>
                  {bust} <strong>chest/bust</strong>
                </span>
              )}

              {cupSize && gender === 'Female' && (
                <span className={styles['profile-overview-list-item']}>
                  {cupSize} <strong>cup size</strong>
                </span>
              )}
            </div>
          </div>
        )}

        {socialNetworks?.length > 0 && (
          <div
            id="social"
            className={styles['profile-overview-item-container']}
          >
            <span className={styles['profile-overview-item-title']}>
              Social Profiles:
            </span>
            <div className={styles['profile-overview-icon-list']}>
              {socialNetworks.map(({ id, network, full_url }) => (
                <a
                  key={id}
                  href={isAuthenticated ? full_url : '#'}
                  rel="noopener noreferrer"
                  target="_blank"
                  onClick={tryContact}
                >
                  <Image
                    src={`/assets/icons/social-network/icon-${network}-1.svg`}
                    width={35}
                    height={35}
                    alt=""
                  />
                </a>
              ))}
            </div>
          </div>
        )}

        {skills?.length > 0 && (
          <div
            id="skills"
            className={styles['profile-overview-item-container']}
          >
            <span className={styles['profile-overview-item-title']}>
              Skills:
            </span>
            <div className={styles['profile-overview-badge-list']}>
              {skills.map(({ id, title }, key) => (
                <div
                  key={id}
                  className={cn(
                    styles['profile-overview-badge'],
                    key > 9 && !skillsLoaded
                      ? styles['profile-overview-badge-hidden']
                      : '',
                  )}
                >
                  {title}
                </div>
              ))}
              {skills?.length > 10 && !skillsLoaded && (
                <button
                  className={styles['load-more-btn']}
                  onClick={() => setSkillsLoaded(true)}
                >
                  + Load more
                </button>
              )}
            </div>
          </div>
        )}
      </>
    );
  };

  const renderAgent = () => {
    return (
      <>
        {categories?.length > 0 && (
          <div
            id="categories"
            className={styles['profile-overview-item-container']}
          >
            <span className={styles['profile-overview-item-title']}>
              Categories:
            </span>
            <div className={styles['profile-overview-item-list']}>
              {categories.map(({ id, title }) => (
                <span key={id} className={styles['profile-overview-list-item']}>
                  {title}
                </span>
              ))}
            </div>
          </div>
        )}
        {resume && (
          <div id="about" className={styles['profile-overview-item-container']}>
            <span className={styles['profile-overview-item-title']}>
              About:
            </span>
            <div className={styles.about}>{resume}</div>
          </div>
        )}
      </>
    );
  };

  return (
    <div className={styles['profile-overview']} style={{ fontSize }}>
      {isAgent ? renderAgent() : renderTalent()}
    </div>
  );
};

export default memo(ProfileOverview);
