import Link from 'next/link';
import IconArrow from '/public/assets/icons/icon-angle-6.svg';
import styles from './BreadcrumbsItem.module.scss';
import { useEffect, useRef, useState } from 'react';
import { useViewport } from '../../utils/useViewport';

const BreadcrumbsItem = ({
  children,
  href,
  first = false,
  last = false,
  onExpand,
}) => {
  const { width } = useViewport();
  const [isMobile, setIsMobile] = useState(width < 1024);
  const crumbLink = useRef();
  const [expanded, setExpanded] = useState(true);
  const [crumbWidth, setCrumbWidth] = useState(0);

  useEffect(() => {
    if (width) {
      setIsMobile(width < 1024);
    }
  }, [width]);

  const collapse = () => {
    if (crumbLink.current.offsetWidth) {
      setCrumbWidth(crumbLink.current.offsetWidth + 2);
    }
    crumbLink.current.style.width = 0;
    setExpanded(false);
  };

  const expand = () => {
    if (crumbLink.current) {
      setExpanded(true);
      crumbLink.current.style.width = crumbWidth + 'px';
      onExpand();
    }
  };

  useEffect(() => {
    if (!first && !last && isMobile) {
      collapse();
    } else if (!isMobile && !expanded) {
      expand();
    }
  }, [first, last, isMobile]);

  if (last) {
    return <span className={styles['end-crumb']}>{children}</span>;
  }

  return (
    <>
      {!first && !expanded && (
        <span className={styles['expand']} onClick={expand}>
          ...
        </span>
      )}
      <Link href={href} className={styles['link']} ref={crumbLink}>
        {children}
      </Link>
      <IconArrow className={styles['delimiter']} />
    </>
  );
};

export default BreadcrumbsItem;
