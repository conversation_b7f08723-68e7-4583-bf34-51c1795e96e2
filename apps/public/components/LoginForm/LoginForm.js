import { memo, useState, useEffect } from 'react';
import styles from './LoginForm.module.scss';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import {
  Button,
  SocialButtons,
  HeaderMobile,
  Input,
  Loading,
  PasswordInput,
} from '../index';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';
import { useAnalytics } from 'use-analytics';
import {
  GTM_ACTIONS,
  GTM_CATEGORIES,
  GTM_EVENTS,
} from '../../constants/analytics';
import ApiNoCache from '../../services/apiNoCache';
import cn from 'classnames';
import { CookieService } from '../../services/cookieService';
import Link from 'next/link';
import { useSale } from '../../contexts/SaleContext';
import { Amp } from '../../services/amp';
import { OneTrustService } from '../../services/onetrust';
import { ErrorMessage } from '../../constants/form';
import * as Sentry from '@sentry/nextjs';

const LoginForm = ({
  children,
  isModal,
  closeModal = () => {},
  openRegistrationModal = () => {},
}) => {
  const [loading, setLoading] = useState(false);
  const [redirecting, setRedirecting] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();
  const { refreshUserProfiles, accountLevelVerify } = useAuth();
  const { track } = useAnalytics();
  const { refreshSale } = useSale();

  const formik = useFormik({
    initialValues: {
      email: '',
      password: '',
    },
    onSubmit: async () => {
      setLoading(true);

      const response = await login();
      const authenticationToken = response.authenticationToken;
      const volatileToken = response.volatileToken;
      const tokenExpires = response.tokenExpires;
      const accountId = response.id;
      const profiles = response.links?.profiles?.items || [];
      const profile = profiles.length ? profiles[0] : {};
      const city = profile.links?.location?.links?.city?.slug || '';
      const userType = profile.rel || 'talent';
      const profileId = profile.id || null;

      if (
        response.status !== 'error' &&
        authenticationToken &&
        volatileToken &&
        tokenExpires
      ) {
        if (router.query?.developer) {
          CookieService.setDeveloperCookie('true');
        }

        if (router.asPath.includes('/premium')) {
          CookieService.setRedirectCookie(`/checkout?action=329`);
        }

        if (router.asPath.includes('/lifetime')) {
          CookieService.setRedirectCookie(`/checkout?action=343`);
        }

        track(GTM_EVENTS.interaction, {
          target: GTM_CATEGORIES.login,
          action: GTM_ACTIONS.login,
          label: userType,
        });

        setRedirecting(true);

        CookieService.setAuthenticationCookie(authenticationToken);
        CookieService.setVolatileCookie(volatileToken);
        CookieService.setExpiresCookie(tokenExpires);
        CookieService.setAccountCookie(accountId);
        CookieService.setUserTypeCookie(userType);
        CookieService.setProfileCookie(profileId);
        CookieService.deleteFirstTimeVisitorCookie();
        Sentry.setUser({ id: accountId, type: userType });

        await refreshSale();
        await accountLevelVerify();
        await refreshUserProfiles(accountId);
        await OneTrustService.checkConsent();
        await navigate(userType === 'talent', city);

        formik.resetForm();
        setError('');
        setRedirecting(false);

        if (isModal) {
          closeModal();
        }
      } else {
        Amp.track(Amp.events.invalidEventOccurred, {
          name: `login failed`,
        });

        setError(response.message);
      }

      setLoading(false);
    },
    validationSchema: Yup.object({
      email: Yup.string().required(ErrorMessage.EmailRequired),
      password: Yup.string().required(ErrorMessage.PasswordLoginRequired),
    }),
  });

  useEffect(() => {
    if (error) {
      setError('');
    }
  }, [formik.values]);

  const handleNavigateBack = async () => {
    await router.push('/');
  };

  const login = async () => {
    const { email, password } = formik.values;

    return (
      await ApiNoCache.clientAPIRoute(`/login`, {
        method: 'POST',
        body: JSON.stringify({ email, password }),
      })
    ).data;
  };

  const navigate = async (isTalent, city) => {
    const redirectPath = CookieService.getRedirectCookie();

    if (isTalent && redirectPath) {
      window.location.href = `${process.env.redirectTalentUrl}${redirectPath}`;
    } else if (isTalent) {
      await router.push(`/castingcalls${city ? `?city=${city}` : ``}`);
    } else {
      CookieService.deleteRedirectCookie();
      window.location.href = process.env.redirectProUrl;
    }
  };

  const onRegister = async () => {
    if (isModal) {
      closeModal();
      openRegistrationModal();
    } else {
      await router.push('/register');
    }
  };

  return (
    <>
      <HeaderMobile
        marginOverride={!isModal}
        onClose={isModal ? closeModal : handleNavigateBack}
      >
        {redirecting ? <h1>Authentication</h1> : <>{children}</>}
      </HeaderMobile>
      <div className={styles['login-form-container']}>
        <div className={styles['title-container']}>
          <div className={styles['title']}>
            {redirecting ? (
              <>
                <h1>Authentication</h1>
                <p>It will take a moment, please stand by.</p>
              </>
            ) : (
              <>{children || <h1>Login allcasting</h1>}</>
            )}
          </div>
          {!redirecting && (
            <div className={cn(styles['socials-container'], styles.desktop)}>
              <SocialButtons isLogin />
              <span className={styles['socials-or']}>or</span>
            </div>
          )}
          <div className={styles['login-action-container-top']}>
            {redirecting ? (
              <>
                <p>It will take a moment, please stand by.</p>
              </>
            ) : (
              <>
                <span>Not a member?</span>
                <span className={styles.link} onClick={onRegister}>
                  Join allcasting now
                </span>
              </>
            )}
          </div>
        </div>
        {redirecting ? (
          <Loading />
        ) : (
          <form
            className={styles.form}
            onSubmit={formik.handleSubmit}
            data-cy="login-form"
          >
            <div className={styles['form-controls']}>
              <div className={cn(styles['socials-container'], styles.mobile)}>
                <SocialButtons isLogin />
                <span className={styles['socials-or']}>or</span>
              </div>
              <Input
                className={styles.input}
                name="email"
                placeholder="Email"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.email}
                isTouched={formik.touched.email}
                error={formik.errors.email}
                dataCy="login-email-input"
              />
              <PasswordInput
                name="password"
                value={formik.values.password}
                error={formik.errors.password}
                isTouched={formik.touched.password}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                dataCy="login-password-input"
              />
              {error && (
                <small
                  className={styles['error-message']}
                  data-cy="login-form-error"
                >
                  {error}
                </small>
              )}
            </div>
            <div className={styles['login-action-container-bottom']}>
              {loading ? (
                <Loading minHeight="40px" padding="0" />
              ) : (
                <div className={styles['btn-box']}>
                  <Button
                    className={styles.button}
                    color="orange"
                    disabled={!(formik.isValid && formik.dirty)}
                    type="submit"
                    label="Log In"
                    minWidth={'220px'}
                    dataCy="login-submit-btn"
                  />
                </div>
              )}
              <Link
                href={'/auth/forgot-password'}
                className={styles.link}
                onClick={closeModal}
                data-cy="login-form-forgot-password-btn"
              >
                Forgot password?
              </Link>
              <div className={styles['desktop-footer']}>
                Not a member?{' '}
                <span className={styles.link} onClick={onRegister}>
                  Join now
                </span>
              </div>
            </div>
          </form>
        )}
      </div>
    </>
  );
};

export default memo(LoginForm);
