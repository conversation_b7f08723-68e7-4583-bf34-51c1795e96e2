@import '../../styles/variables';
@import '../../styles/mixins';

.login-form-container {
  padding-bottom: $space-20;
  box-shadow: unset;
  border-radius: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  background-color: $white;

  @include desktop {
    width: 430px;
    box-shadow: $shadow-card-container;
    border-radius: 10px;
    padding: $space-40 $space-40 $space-30;
  }
}

.form {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 $space-10;
  position: relative;

  @include desktop {
    gap: $space-20;
  }
}

.form-controls {
  width: 100%;
  padding: $space-20 $space-20 $space-60;
  box-shadow: $shadow-form-control;
  border-radius: 3px;
  display: flex;
  flex-direction: column;
  gap: $space-20;

  @include desktop {
    box-shadow: unset;
    border-radius: 0;
    padding: 0 0 $space-10;
  }
}

.input {
  width: 100%;
}

.link {
  color: $blue-100;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.error-message {
  color: $red-60;
  text-align: center;
  font-size: 14px;
  font-weight: 300;
}

.login-action-container-bottom {
  display: flex;
  flex-direction: column;
  gap: $space-20;
  align-items: center;
  margin-top: -$space-20;

  .button {
    box-shadow: $shadow-btn-orange;
  }

  .button:disabled {
    opacity: 0.5;
  }

  @include desktop {
    margin-top: 0;
  }
}

.btn-box {
  background: $white;
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: $grey-10;
  padding: $space-15 $space-20;
  margin-left: -$space-20;
  margin-right: -$space-20;

  span {
    font-weight: 300;
  }

  @include desktop {
    background-color: transparent;
    margin-left: 0;
    margin-right: 0;
    padding: 0 $space-20;
  }
}

.title {
  display: none;

  h1 {
    margin: 0;
    text-align: center;
    font-size: 30px;
  }

  @include desktop {
    display: initial;
  }
}

.login-action-container-top {
  display: flex;
  flex-direction: column;
  align-items: center;

  @include desktop {
    display: none;
    padding-top: 0;
  }
}

.socials-container {
  flex-direction: column;
  gap: $space-20;
  padding-top: $space-30;
  align-items: center;
  width: 100%;

  .socials-or {
    color: $grey-80;
    font-weight: 300;
    font-size: 14px;
  }

  &.mobile {
    display: flex;
    padding-top: $space-10;

    @include desktop {
      display: none;
    }
  }

  &.desktop {
    display: none;

    @include desktop {
      display: flex;
    }
  }
}

.desktop-footer {
  display: none;

  @include desktop {
    display: block;
  }
}
