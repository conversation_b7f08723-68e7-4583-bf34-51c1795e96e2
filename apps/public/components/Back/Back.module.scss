@import '../../styles/mixins';
@import '../../styles/variables';

.back {
  border-bottom: 1px solid #aaa;
  cursor: pointer;
  display: block;
  margin: 0 (-$space-20);
  color: $black;
  font-weight: 300;
  font-size: 16px;
  text-align: center;
  padding: 12px 0;
  line-height: 1.4;
  position: relative;

  &::before {
    content: '';
    background: url('#{$assetUrl}/assets/icons/icon-angle-5.svg') center center
      no-repeat;
    background-size: cover;
    transform: rotate(180deg);
    height: 15px;
    width: 8px;
    position: absolute;
    left: 20px;
    top: 15px;
  }

  @include desktop {
    color: $blue-100;
    font-weight: 400;
    font-size: 14px;
    font-style: italic;
    text-align: left;
    padding: 0;
    margin: 0;
    border-bottom: none;

    &::before {
      content: '';
      background: url('#{$assetUrl}/assets/icons/icon-arrow-2.svg') left center
        no-repeat;
      transform: none;
      padding: $space-10 13px;
      position: relative;
      left: 0;
      top: 0;
    }
  }
}

.full {
  display: none;

  @include desktop {
    display: inline;
  }
}

.short {
  display: inline;

  @include desktop {
    display: none;
  }
}
