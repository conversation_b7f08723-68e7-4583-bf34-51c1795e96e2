import React, { useState, useEffect, useRef, memo } from 'react';
import styles from './YoutubePlayer.module.scss';
import cn from 'classnames';
import { Loading } from '../index';

const YoutubePlayer = ({ videoId, videoClassName = '' }) => {
  const [lazyLoad, setLazyLoad] = useState(false);
  const videoRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting) {
        setLazyLoad(true);
        observer.disconnect();
      }
    });

    observer.observe(videoRef.current);

    return () => {
      if (videoRef.current) {
        // eslint-disable-next-line react-hooks/exhaustive-deps
        observer.unobserve(videoRef.current);
      }
    };
  }, []);

  return (
    <div ref={videoRef}>
      {lazyLoad ? (
        <iframe
          className={cn(styles.placeholder, videoClassName)}
          src={`https://www.youtube.com/embed/${videoId}`}
        />
      ) : (
        <div className={cn(styles.placeholder, videoClassName)}>
          <Loading color="white" minHeight="100%" />
        </div>
      )}
    </div>
  );
};

export default memo(YoutubePlayer);
