import React from 'react';
import styles from './BoldShiftText.module.scss';
import cn from 'classnames';

// Use for text to change font-weight without causing layout shift
const BoldShiftText = ({
  children,
  className = undefined,
  boldWeight = 600,
  ...props
}) => {
  return (
    <div className={cn(styles.wrapper, className)} {...props}>
      <div>{children}</div>

      <div
        className={styles.placeholder}
        style={{ fontWeight: boldWeight }}
        aria-hidden="true"
      >
        {children}
      </div>
    </div>
  );
};

export default BoldShiftText;
