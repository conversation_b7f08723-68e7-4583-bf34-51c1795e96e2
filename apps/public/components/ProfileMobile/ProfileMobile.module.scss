@import '../../styles/variables';

.profile-section-mobile {
  display: flex;
  flex-direction: column;
  padding-bottom: $space-40;
}

.profile-content {
  padding-top: $space-20;
  display: flex;
  flex-direction: column;
  gap: $space-20;
}

.photo-container {
  position: relative;
}

.profile-contact {
  position: absolute;
  width: 100%;
  z-index: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: $white;
  padding: $space-20 0;
  gap: $space-10;
  background-image: $gradient-black-top;
  bottom: 0;
}

.profile-rating {
  display: flex;
  align-items: center;
  gap: 3px;
}

.star {
  filter: invert(71%) sepia(97%) saturate(1417%) hue-rotate(359deg)
    brightness(101%) contrast(104%);
}

.profile-rating-count {
  display: inline-block;
  vertical-align: middle;
  font-weight: 300;
  font-size: 16px;
}

.profile-name {
  display: flex;
  align-items: baseline;
  font-size: 30px;
  font-weight: 700;
  line-height: 1.2;
  position: relative;
  word-break: break-word;
  padding: 0 $space-20;
  text-align: center;
}

.separator {
  display: block;
  height: 1px;
  background-color: $grey-80;
  opacity: 0.15;
  margin: 0;
  border: none;
}

.id-container {
  background-color: $grey-20;
  text-transform: uppercase;
  padding: 0 $space-20;
  display: flex;
  align-items: center;
  font-size: 14px;
  line-height: 30px;
  font-weight: 300;
}

.video {
  width: 100%;
  height: 100%;
  display: block;
  border: none;
}

.menu-container {
  background-color: $violet-80;
  background-image: $gradient-port-gore;
  display: flex;
  justify-content: center;
  gap: $space-10;
  padding: $space-20;
  position: sticky;
  top: 0;
  z-index: 3;
  height: 54px;

  &.is-agent {
    top: 55px;
  }

  &.is-upgrade {
    top: 65px;
  }
}

.menu-item {
  color: $white;
  font-weight: 700;
  line-height: 1.1;
  opacity: 0.5;
  font-size: 16px;
  text-decoration: none;

  &:hover {
    opacity: 1;
    text-decoration: none;
  }
}

.profile-overview-container {
  padding: 0 $space-20;
}

.profile-title {
  font-size: 30px;
  margin: 0;
  scroll-margin-top: 80px;
  padding: $space-20;
}

.profile-credits {
  display: flex;
  flex-direction: column;
  gap: $space-20;
  padding: 0 $space-20;
}

.back-icon-container {
  position: absolute;
  left: 20px;
  display: flex;
}

.back-icon {
  transform: rotate(180deg);
}

.profile-header {
  display: flex;
  justify-content: center;
  padding: $space-15 $space-20;
  position: sticky;
  top: 0;
  align-items: center;
  z-index: 2;
  background-color: $white;
  border-bottom: 1px solid $grey-60;

  h1 {
    font-size: 16px;
    margin: 0;
    font-weight: 400;
    padding: 0 $space-30;
  }
}

.media {
  scroll-margin-top: 80px;
  display: flex;
  flex-direction: column;
  gap: $space-20;
}

.casting-calls {
  padding: 0 $space-20;
}

.profile-btn-container {
  width: 100%;
  display: flex;
  justify-content: center;
  padding-bottom: $space-20;
}

.embla-slide-img {
  display: block;
  width: 100%;
  object-fit: cover;
}

.breadcrumbs {
  padding: 0 $space-20;
}

.audio-slide {
  padding: 0 $space-45;
}

.profile-subtitle {
  font-weight: 700;
  font-size: 16px;
  padding: 0 $space-20;
  margin-top: 0;
}

.audio {
  padding: 0 $space-20;
}

.placeholder-image-container {
  position: relative;
  padding-top: 133.124%;
  display: flex;
  flex-flow: row wrap;
  align-items: flex-end;
  background-color: $grey-10;
  color: $white;
  text-align: center;
}

.placeholder-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  padding-top: inherit;
  background-size: auto 70%;
  background-position: 50% 100%;
  background-repeat: no-repeat;
  opacity: 0.3;

  &.female {
    background-image: url('#{$assetUrl}/assets/placeholders/female-head.svg');
  }

  &.male {
    background-image: url('#{$assetUrl}/assets/placeholders/male-head.svg');
  }

  &.director {
    background-image: url('#{$assetUrl}/assets/placeholders/casting-director.svg');
    background-position: center;
  }
}

.image {
  width: 100%;
}

.video-mobile {
  width: 100%;
  display: block;
  border: none;
  height: calc((100vw - 40px) / 2);
}
