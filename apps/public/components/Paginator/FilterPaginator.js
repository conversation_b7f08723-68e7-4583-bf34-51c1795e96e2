import { memo } from 'react';
import styles from '../Paginator/Paginator.module.scss';
import Pagination from '@mui/material/Pagination';
import Stack from '@mui/material/Stack';
import { PaginationItem } from '@mui/material';
import FilterService from '@entertech/filter-service';
import { useViewport } from '../../utils/useViewport';
import { isInViewPort } from '../../utils/isInViewPort';

const FilterPaginator = ({
  total,
  perPage = 24,
  handleChange,
  filters = {},
  seo = {},
  urlPrefix = '',
}) => {
  const page = FilterService.generatePageFromFilter(filters);
  const count = Math.ceil(total / perPage);
  const { width } = useViewport();

  const getSiblings = () => {
    if (isInViewPort(width, 'xsmall', 'max')) {
      return 0;
    } else if (isInViewPort(width, 'mobile', 'max')) {
      return 1;
    } else {
      return 2;
    }
  };

  if (total < perPage) return <></>;

  return (
    <Stack spacing={2} className={styles.pagination}>
      <Pagination
        hideNextButton={page === count}
        hidePrevButton={page === 1}
        count={count}
        onChange={handleChange}
        renderItem={(item) => {
          const link =
            `/${urlPrefix}/` +
            FilterService.generateUrlFromFilter(filters, seo, item.page);

          return (
            <PaginationItem
              component={'a'}
              href={link.replace(/\/+$/, '')}
              {...item}
            />
          );
        }}
        page={page}
        sx={{
          '& .MuiSvgIcon-root': {
            color: '#320c58',
          },
          '& .MuiButtonBase-root.Mui-selected': {
            backgroundColor: '#5f0c78',
            color: '#fff',
          },
        }}
        siblingCount={getSiblings()}
        boundaryCount={isInViewPort(width, 'mobile', 'max') ? 0 : 1}
      />
    </Stack>
  );
};

export default memo(FilterPaginator);
