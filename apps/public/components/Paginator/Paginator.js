import { memo } from 'react';
import styles from './Paginator.module.scss';
import Pagination from '@mui/material/Pagination';
import Stack from '@mui/material/Stack';
import { PaginationItem } from '@mui/material';
import { isInViewPort } from '../../utils/isInViewPort';
import { useViewport } from '../../utils/useViewport';
import Link from 'next/link';

const Paginator = ({
  page,
  total,
  perPage = 24,
  handleChange,
  prefixUrl = '',
  prefixPage = '',
  prefetch = false,
  scroll = true,
}) => {
  const { width } = useViewport();
  const count = Math.ceil(total / perPage);

  const getSiblings = () => {
    if (isInViewPort(width, 'xsmall', 'max')) {
      return 0;
    } else if (isInViewPort(width, 'mobile', 'max')) {
      return 1;
    } else {
      return 2;
    }
  };

  if (total < perPage) return <></>;

  return (
    <Stack spacing={2} className={styles.pagination}>
      <Pagination
        hideNextButton={page === count}
        hidePrevButton={page === 1}
        count={count}
        onChange={handleChange}
        renderItem={(item) => (
          <PaginationItem
            component={Link}
            prefetch={prefetch}
            scroll={scroll}
            href={`${prefixUrl}${
              item.page > 1 ? prefixPage + item.page : ''
            }`.replace(/\/+$/, '')}
            {...item}
          />
        )}
        page={page}
        sx={{
          '& .MuiSvgIcon-root': {
            color: '#320c58',
          },
          '& .MuiButtonBase-root.Mui-selected': {
            backgroundColor: '#5f0c78',
            color: '#fff',
          },
        }}
        siblingCount={getSiblings()}
        boundaryCount={isInViewPort(width, 'mobile', 'max') ? 0 : 1}
      />
    </Stack>
  );
};

export default memo(Paginator);
