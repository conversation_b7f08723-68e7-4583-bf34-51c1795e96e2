@import '../../styles/mixins';
@import '../../styles/variables';

.sidebar-container {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  overflow: hidden auto;
  z-index: 1500;
  transition: all 0.3s;
  transform: translate(100%);
  width: 100%;
  background-color: $black;
  background-image: $gradient-port-gore;

  @include xsmall {
    width: 320px;
  }
}

.sidebar-header {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  color: $white;
  font-size: 18px;
  font-weight: 700;
  line-height: 1.33;
  padding-top: $space-40;
  gap: $space-15;
}

.rating-container {
  display: flex;
  position: relative;
  justify-content: center;
  align-items: flex-end;
  flex-direction: row;
}

.rating {
  position: absolute;
  left: 100px;
  display: flex;
  align-items: center;
  gap: $space-5;
  color: $white;
  font-weight: 300;
  font-size: 16px;
  line-height: 1;
}

.star {
  filter: invert(71%) sepia(97%) saturate(1417%) hue-rotate(359deg)
    brightness(101%) contrast(104%);
  margin-bottom: 2px;
}

.profile-rating {
  display: flex;
  flex-direction: column;
  gap: $space-10;
}

.sidebar-open {
  transform: translate(0);
}

.sidebar {
  max-width: 320px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  padding-top: $space-20;
  padding-bottom: $space-20;
}

.link-button {
  min-width: 200px;
  background-color: $green-100;
  background-image: $gradient-btn-green-gradient;
  background-size: 200% auto;
  background-position: 0 0;
  transition: all 0.2s ease-in-out;
  border: none;
  border-radius: 19px;
  cursor: pointer;
  display: inline-block;
  font-size: 14px;
  font-weight: 700;
  line-height: 1.15;
  padding: 12px;
  text-align: center;
  text-transform: uppercase;
  color: $black;
}

.button {
  border-radius: 50%;
  border: 1px solid $grey-80;
  width: 105px;
  height: 105px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: $white;
  transition: all 0.2s ease-in-out;
  font-weight: 700;
  font-size: 18px;
  cursor: pointer;

  &:hover {
    border-color: $white;
    transition: all 0.2s ease-in-out;
    text-decoration: none;
  }

  .profile-img {
    width: 94px;
    height: 94px;
    border-radius: 50%;
    object-fit: cover;
  }
}

.edit-profile {
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3;
  padding: 0;
  max-height: 25px;

  @include desktop {
    display: none;
  }
}

.logo-wrap {
  padding-bottom: $space-15;
}

.logo-link {
  display: inline-block;
  filter: invert(1);
}

.link {
  display: block;
  color: $blue-100;
  font-size: 16px;
  font-weight: 400;
  text-transform: lowercase;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.close-btn {
  background-color: transparent;
  border: none;
  position: absolute;
  top: 20px;
  left: 20px;
  opacity: 0.5;
  cursor: pointer;
}

.action-button {
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 38px;
  height: 38px;
  line-height: 38px;
  font-size: 14px;
  background-position: left 6px center;
  background-repeat: no-repeat;
  padding-left: 49px;
  background-size: 26px 28px;
  text-transform: none;
  font-weight: 500;
  margin: 0;
}

.action-buttons {
  display: flex;
  flex-direction: row;
  margin: 0;
  gap: $space-20;
  padding: $space-20 0 0 $space-20;
  width: 100%;
}

.action-button-settings {
  background-image: url('#{$assetUrl}/assets/icons/icon-settings-2.svg');
}

.action-button-logout {
  background-image: url('#{$assetUrl}/assets/icons/icon-logout-2.svg');
}

.subscribe-button-container {
  display: flex;
  justify-content: center;

  button {
    color: $black;
  }
}

.profile-name {
  padding: 0 $space-20;
  text-align: center;
}

.title {
  background-color: rgba($white, 0.05);
  text-transform: uppercase;
  line-height: 34px;
  padding: 0 $space-20;
  color: $grey-40;
  font-size: 14px;
  font-weight: 300;
  box-shadow: $shadow-mobile-sidebar-menu-item;
}

.container {
  display: flex;
  flex-direction: column;

  &:first-of-type {
    :last-child {
      border-bottom: none;
    }
  }
}

.menu-links {
  display: flex;
  flex-direction: column;
  padding-left: $space-40;
}

.menu-link {
  color: $blue-100;
  text-transform: lowercase;
  border-bottom: 1px solid $grey-100;
  line-height: 45px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: $space-20;

  &.hidden {
    display: none;
  }
}
