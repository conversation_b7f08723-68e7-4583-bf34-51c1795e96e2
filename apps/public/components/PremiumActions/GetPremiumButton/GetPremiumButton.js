import React, { memo } from 'react';
import { Button } from '../../index';
import { Amp } from '../../../services/amp';
import { useModalContext } from '../../../contexts/ModalContext';

const GetPremiumButton = ({
  label = '',
  minWidth = '',
  color = '',
  shadow = false,
  isAuthenticated,
}) => {
  const { onLoginModalOpen } = useModalContext();

  const onClick = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'get premium',
      scope: Amp.element.scope.premiumPage,
      type: Amp.element.type.button,
    });

    if (isAuthenticated) {
      window.location.href = `${process.env.redirectTalentUrl}/checkout?action=329`;
    } else {
      onLoginModalOpen();
    }
  };

  return (
    <div>
      <Button
        label={label}
        minWidth={minWidth}
        color={color}
        onClick={onClick}
        shadow={shadow}
      />
    </div>
  );
};

export default memo(GetPremiumButton);
