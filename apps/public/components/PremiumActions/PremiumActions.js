import React, { memo, useEffect, useState } from 'react';
import { <PERSON>ton, Modal, PremiumActionList } from '../index';
import styles from './PremiumActions.module.scss';
import { Amp } from '../../services/amp';
import cn from 'classnames';
import { useLiveChat } from '../../contexts/LiveChatContext';
import { LIVE_CHAT_VISIBILITY } from '../../constants/liveChat';

const PremiumActions = ({ isMobile = false }) => {
  const [isPremiumActionsShow, setPremiumActionsShow] = useState(false);
  const [isHidden, setHidden] = useState(false);
  const { visibility } = useLiveChat();

  useEffect(() => {
    if (isMobile) {
      setHidden(visibility === LIVE_CHAT_VISIBILITY.Maximized);
    }
  }, [visibility]);

  const closePremiumActions = () => {
    setPremiumActionsShow(false);
  };
  const openPremiumActions = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'premium actions',
      scope: Amp.element.scope.global,
      section: Amp.element.section.premiumActions,
      type: Amp.element.type.button,
    });

    setPremiumActionsShow(true);
  };

  const togglePremiumActions = () => {
    if (isPremiumActionsShow) {
      closePremiumActions();
    } else {
      openPremiumActions();
    }
  };

  return (
    <>
      <div
        className={cn(styles['premium-actions-section'], {
          [styles.hidden]: isHidden,
        })}
      >
        {isMobile ? (
          <Button
            onClick={openPremiumActions}
            label={''}
            kind="primary"
            color="orange"
            className={cn(styles['premium-actions-btn'], styles['mobile-view'])}
          />
        ) : (
          <Button
            onClick={togglePremiumActions}
            label={'Premium'}
            kind="primary"
            color="orange"
            minWidth={'200px'}
            className={cn(
              styles['premium-actions-btn'],
              styles['mobile-hidden'],
            )}
          />
        )}

        {isPremiumActionsShow && (
          <>
            {isMobile ? (
              <Modal
                backdropClose
                showCloseButton={false}
                onClose={closePremiumActions}
                classNameContainer={styles['premium-actions-container']}
                showDefaultLayout={false}
              >
                <PremiumActionList onClose={closePremiumActions} />
              </Modal>
            ) : (
              <div
                className={cn(
                  styles['premium-actions-container'],
                  styles['dropdown-view'],
                )}
              >
                <PremiumActionList onClose={closePremiumActions} />
              </div>
            )}
          </>
        )}
      </div>
    </>
  );
};

export default memo(PremiumActions);
