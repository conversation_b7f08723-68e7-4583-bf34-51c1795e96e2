@import '../../styles/variables';
@import '../../styles/mixins';

.premium-actions-section {
  position: sticky;
  bottom: 0;
  z-index: 100;

  @include desktop {
    position: relative;
  }

  &.hidden {
    display: none;
  }

  .premium-actions-btn {
    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
      content: url('#{$assetUrl}/assets/icons/icon-star-1.svg');
      display: inline-block;
      width: 1.2em;
      margin-right: 8px;
    }

    &.mobile-hidden {
      display: none;

      @include desktop {
        display: flex;
      }
    }

    &.desktop-hidden {
      @include desktop {
        display: none;
      }
    }

    &.mobile-view {
      position: absolute;
      top: -140px;
      right: 8px;
      padding: 17px;

      &::before {
        width: 28px;
        height: 28px;
        margin-right: 0;
        line-height: 0;
      }

      @include desktop {
        display: none;
      }
    }
  }
}

.premium-actions-container {
  border-radius: 10px;
  border: 1px solid $grey-10;
  overflow: hidden;

  &.dropdown-view {
    position: absolute;
    top: $space-50;
    left: 0;
    background-color: $white;
    width: max-content;
  }
}
