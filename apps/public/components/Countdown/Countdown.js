import styles from './Countdown.module.scss';
import { memo, useEffect, useState } from 'react';
import { formatTime } from '../../utils/formatTime';
import cn from 'classnames';
import { useInterval } from '../../utils/useInterval';

const Countdown = ({
  expirationTime,
  horizontalLayout = false,
  onCountdownEnd,
  loweredOpacity,
  loweredFontWeight,
}) => {
  const [countdownSettings, setCountdownSettings] = useState(null);
  const [currentTimeLeft, setCurrentTimeLeft] = useState(
    expirationTime - Math.floor(Date.now() / 1000),
  );

  useInterval(() => {
    if (currentTimeLeft === 0) {
      onCountdownEnd();
    }

    setCurrentTimeLeft(currentTimeLeft - 1);
  }, 1000);

  useEffect(() => {
    setCountdownSettings(formatTime(currentTimeLeft));
  }, [currentTimeLeft]);

  return (
    <>
      {countdownSettings && (
        <div
          className={cn(styles['countdown'], {
            [styles['countdown-horizontal']]: horizontalLayout,
          })}
        >
          <div className={styles['time-container']}>
            <span
              className={cn(styles['time-digit'], {
                [styles['lowered-font-weight']]: loweredFontWeight,
              })}
            >
              {countdownSettings.days}
            </span>
            <span
              className={cn(styles['time-title'], {
                [styles['lowered-opacity']]: loweredOpacity,
              })}
            >
              days
            </span>
          </div>
          <span className={styles['time-separator']}>:</span>
          <div className={styles['time-container']}>
            <span
              className={cn(styles['time-digit'], {
                [styles['lowered-font-weight']]: loweredFontWeight,
              })}
            >
              {countdownSettings.hours}
            </span>
            <span
              className={cn(styles['time-title'], {
                [styles['lowered-opacity']]: loweredOpacity,
              })}
            >
              hours
            </span>
          </div>
          <span className={styles['time-separator']}>:</span>
          <div className={styles['time-container']}>
            <span
              className={cn(styles['time-digit'], {
                [styles['lowered-font-weight']]: loweredFontWeight,
              })}
            >
              {countdownSettings.minutes}
            </span>
            <span
              className={cn(styles['time-title'], {
                [styles['lowered-opacity']]: loweredOpacity,
              })}
            >
              {horizontalLayout ? 'min' : 'minutes'}
            </span>
          </div>
          <span className={styles['time-separator']}>:</span>
          <div className={styles['time-container']}>
            <span
              className={cn(styles['time-digit'], {
                [styles['lowered-font-weight']]: loweredFontWeight,
              })}
            >
              {countdownSettings.seconds}
            </span>
            <span
              className={cn(styles['time-title'], {
                [styles['lowered-opacity']]: loweredOpacity,
              })}
            >
              {horizontalLayout ? 'sec' : 'seconds'}
            </span>
          </div>
        </div>
      )}
    </>
  );
};

export default memo(Countdown);
