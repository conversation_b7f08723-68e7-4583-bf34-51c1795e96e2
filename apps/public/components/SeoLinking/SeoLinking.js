import { Button } from '../index';
import styles from './seoLinking.module.scss';

function SeoLinking({ seoCrossLinking, title = true }) {
  const crossLinkingValues = seoCrossLinking || {};

  const fixUrl = (url) => {
    const urlWithoutDomain = url.replace(/^.*\/\/[^\/]+/, '');

    return process.env.baseUrl + urlWithoutDomain;
  };

  const handleClick = (event, url) => {
    /**
     * Avoiding router as easy solution for reloading filter
     * values from server side to get location section formed correctly.
     */
    if (window) {
      event.preventDefault();
      window.location.href = url;
    }
  };

  return (
    <>
      {Object.keys(crossLinkingValues).length > 0 && (
        <div className={styles['linking-block']}>
          {title && (
            <div className={styles['linking-title']}>Popular searches:</div>
          )}
          <div className={styles['linking-btn-box']}>
            {crossLinkingValues.map((data, index) => (
              <span key={index}>
                <Button
                  type="link"
                  kind="secondary"
                  color="midnight"
                  className={styles['linking-btn']}
                  label={data.anchor}
                  href={fixUrl(data.linking)}
                  onClick={(event) => handleClick(event, fixUrl(data.linking))}
                />
              </span>
            ))}
          </div>
        </div>
      )}
    </>
  );
}

export default SeoLinking;
