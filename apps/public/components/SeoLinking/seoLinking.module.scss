@import '../../styles/mixins';
@import '../../styles/variables';

.linking-block {
  padding-top: 23px;

  @include desktop {
    padding: 18px $space-35 0;
  }
}

.linking-title {
  text-align: center;
  font-size: 14px;
  color: $black;
  font-weight: 700;
  margin-bottom: 18px;
}

.linking-btn-box {
  display: flex;
  flex-flow: row wrap;
  justify-content: center;
  gap: 16px 8px;

  & span a {
    text-decoration: none;
  }
}
