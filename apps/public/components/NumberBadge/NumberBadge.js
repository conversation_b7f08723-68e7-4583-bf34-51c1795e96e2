import cn from 'classnames';
import styles from './NumberBadge.module.scss';

const NumberBadge = ({
  number = 0,
  color = 'blue',
  className = '',
  size = 18,
}) => {
  const sizepx = `${size}px`;

  return (
    <span
      className={cn(className, styles.badge, styles[color])}
      style={{ height: sizepx, width: sizepx, lineHeight: sizepx }}
    >
      {number > 99 ? 99 : number}
    </span>
  );
};

export default NumberBadge;
