import { memo, useEffect, useState } from 'react';
import styles from './ResetPasswordForm.module.scss';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { Button, Loading, PasswordInput } from '../index';
import ApiNoCache from '../../services/apiNoCache';
import { ErrorMessage, PASSWORD_REGEX } from '../../constants/form';

const ResetPasswordForm = ({ token }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [passwordReset, setPasswordReset] = useState(false);
  const formik = useFormik({
    initialValues: {
      password: '',
      passwordConfirmation: '',
    },
    onSubmit: async () => {
      setLoading(true);

      const response = await reset();

      setLoading(false);

      if (response.status !== 'error') {
        formik.resetForm();
        setError('');
        setPasswordReset(true);
      } else {
        setError(response.message);
      }
    },
    validationSchema: Yup.object({
      password: Yup.string()
        .required(ErrorMessage.PasswordRequired)
        .matches(PASSWORD_REGEX, ErrorMessage.PasswordPattern)
        .min(8, ErrorMessage.PasswordPattern),
      passwordConfirmation: Yup.string().test(
        'passwords-match',
        ErrorMessage.PasswordMatch,
        (value) => formik.values.password === value,
      ),
    }),
  });

  useEffect(() => {
    if (error) {
      setError('');
    }
  }, [formik.values]);

  const reset = async () => {
    const { password } = formik.values;

    return (
      await ApiNoCache.clientAPIRoute(`/password/reset`, {
        method: 'POST',
        body: JSON.stringify({ token, password }),
      })
    ).data;
  };

  return (
    <>
      {passwordReset ? (
        <div className={styles['form-sent']}>
          <h1>New password created</h1>
          <p>
            Your new password has been saved. <br />
            You can now login to your account.
          </p>
          <Button
            className={styles.button}
            color="blue"
            type="submit"
            label="Login"
            minWidth={'220px'}
            onClick={() => {
              window.location.href = `/login`;
            }}
          />
        </div>
      ) : (
        <div className={styles['reset-form-container']}>
          <div className={styles['title-container']}>
            <div className={styles['title']}>
              <h1>Create your new password</h1>
            </div>
          </div>
          <form className={styles.form} onSubmit={formik.handleSubmit}>
            <div className={styles['form-controls']}>
              <PasswordInput
                className={styles.input}
                name="password"
                placeholder="Password"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.password}
                isTouched={formik.touched.password}
                error={formik.errors.password}
              />
              <PasswordInput
                className={styles.input}
                name="passwordConfirmation"
                placeholder="Confirm password"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.passwordConfirmation}
                isTouched={formik.touched.passwordConfirmation}
                error={formik.errors.passwordConfirmation}
              />
              {error && (
                <small className={styles['error-message']}>{error}</small>
              )}
            </div>
            <div className={styles['reset-action-container-bottom']}>
              {loading ? (
                <Loading minHeight="40px" padding="0" />
              ) : (
                <div className={styles['btn-box']}>
                  <Button
                    className={styles.button}
                    color="blue"
                    disabled={!(formik.isValid && formik.dirty)}
                    type="submit"
                    label="Confirm"
                    minWidth={'220px'}
                  />
                </div>
              )}
            </div>
          </form>
        </div>
      )}
    </>
  );
};

export default memo(ResetPasswordForm);
