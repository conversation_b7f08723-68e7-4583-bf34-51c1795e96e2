@import '../../styles/mixins';
@import '../../styles/variables';

.role {
  border: 1px dashed $grey-60;
  position: relative;
  margin-bottom: $space-40;
  padding: $space-20;
  scroll-margin-top: 80px;
  word-break: break-word;
}

.role-button-container {
  position: absolute;
  bottom: -17px;
  left: -120px;
  margin-left: 50%;
  background-color: $white;

  @include desktop {
    right: 20px;
    left: auto;
    margin-left: 0;
  }
}

.title {
  color: $black;
  font-size: 18px;
  font-weight: 700;
  line-height: 1.3;
  margin-bottom: $space-15;
  display: block;
}

.gender {
  color: $grey-100;
  font-size: 14px;
  font-weight: 300;

  &::after {
    content: '•';
    padding: 7px $space-10;
    font-size: 1rem;
    color: $grey-60;
  }
}

.age {
  color: $grey-100;
  font-size: 14px;
  font-weight: 300;
}

.ethnicity {
  display: block;
  padding-top: $space-5;
  color: $grey-100;
  font-size: 14px;
  font-weight: 300;
}

.role-match-container {
  background-image: $gradient-green-blue;
  color: transparent;
  background-clip: text;
  font-weight: 300;
  font-size: 16px;
  display: flex;
  gap: $space-10;
  align-items: center;
}

.role-match-icon {
  filter: invert(86%) sepia(11%) saturate(2467%) hue-rotate(87deg)
    brightness(93%) contrast(83%);
}

.role-criteria-mismatch {
  color: $red-60;
}
