import styles from './Role.module.scss';
import React, { useState, forwardRef, useImperativeHandle } from 'react';
import Button from '../Button/Button';
import { useAnalytics } from 'use-analytics';
import {
  GTM_EVENTS,
  GTM_CATEGORIES,
  GTM_ACTIONS,
} from '../../constants/analytics';
import Image from 'next/image';
import cn from 'classnames';
import { useAuth } from '../../contexts/AuthContext';
import { useNotifications } from '../../contexts/NotificationContext';
import {
  ModalDirectApplication,
  ModalEmailApplication,
  ModalEmailApplicationStatus,
} from '../index';
import ApiNoCache from '../../services/apiNoCache';
import { Amp } from '../../services/amp';
import ModalNotFullMatch from '../Modal/ModalNotFullMatch/ModalNotFullMatch';
import { useRouter } from 'next/router';
import { ErrorMessage } from '../../constants/form';
import { isImageApproved } from '../../utils/imageHelpers';
import { TYPE } from '../../constants/castingCalls';

const Role = forwardRef(
  (
    {
      id,
      title,
      gender,
      age,
      allEthnicitiesAccepted,
      ethnicities,
      description,
      isExpired = false,
      isFullMatch = false,
      criteria,
      attended = false,
      dates,
      email,
      emailSubject,
      castingCallId,
      castingCallType,
      castingCallWebsite,
      castingCallLocation,
      castingCallAddress,
      castingCallPhone,
      castingCallTitle,
      isProRegistered,
      profile,
      onEasyApplySuccess,
    },
    ref,
  ) => {
    const [showDirectApplicationModal, setShowDirectApplicationModal] =
      useState(false);
    const [showEmailApplicationModal, setShowEmailApplicationModal] =
      useState(false);
    const [
      showEmailApplicationStatusModal,
      setShowEmailApplicationStatusModal,
    ] = useState(false);
    const [applied, setApplied] = useState(attended);
    const [showNotFullMatchModal, setShowNotFullMatchModal] = useState(false);
    const [loading, setLoading] = useState(false);

    const { track } = useAnalytics();
    const { userType, profileId, accountLevel } = useAuth();
    const { setNotification } = useNotifications();
    const router = useRouter();

    const showApplyButton = !isExpired && userType !== 'agent';
    const isEmailEasyApplication =
      isProRegistered && castingCallType === TYPE.Email;
    const isEasyApplication = castingCallType === TYPE.Web;
    const isEasyApplicationSent =
      (isEasyApplication || isEmailEasyApplication) && applied;

    const trackAmpApplyClick = (name = 'apply for role') => {
      Amp.track(Amp.events.elementClicked, {
        name: name,
        scope: Amp.element.scope.castingCallItemPage,
        section: Amp.element.section.role,
        type: Amp.element.type.button,
        casting_call_id: castingCallId,
        role_id: id,
      });
    };

    const applyToRole = async () => {
      track(GTM_EVENTS.interaction, {
        target: GTM_CATEGORIES.castingcall,
        action: GTM_ACTIONS.apply,
        castingcallId: castingCallId,
        roleId: id,
      });

      const isTalent = userType === 'talent';
      const isPaidOrDelayed = !!accountLevel?.isPaidOrDelayed;

      if (isTalent && isPaidOrDelayed) {
        trackAmpApplyClick();
        if (!isFullMatch) {
          toggleShowNotFullMatchModal();

          return;
        }

        await apply();
      } else if (isTalent) {
        trackAmpApplyClick('unlock apply for role');

        await trackRoleBasicApplication();

        window.location.href = `${process.env.redirectTalentUrl}/upgrade`;
      } else {
        trackAmpApplyClick();
        await router.push('/register');
      }
    };

    useImperativeHandle(ref, () => ({
      async applyToRole() {
        await applyToRole();
      },
    }));

    const closeDirectApplicationModal = () => {
      setShowDirectApplicationModal(false);
    };

    const closeEmailApplicationModal = () => {
      setShowEmailApplicationModal(false);
    };

    const closeEmailApplicationStatusModal = () => {
      setShowEmailApplicationStatusModal(false);
    };

    const postRoleApplication = async () => {
      if (applied) return;

      setLoading(true);

      const body = new FormData();

      body.append('profiles[]', profileId);

      const { status, message, manual_apply } = await (
        await ApiNoCache.clientGateway(`/roles/${id}/candidates`, {
          method: 'POST',
          body,
        })
      ).data;

      const isError = status !== 'ok';

      if (!isError) {
        setApplied(true);

        if (isEasyApplication || isEmailEasyApplication) {
          onEasyApplySuccess();
        }

        if (isEmailEasyApplication) {
          openEmailApplicationStatusModal();
        }

        if (isEasyApplication) {
          setNotification({
            type: 'success',
            message: 'Your application has been sent',
            timeout: '5000',
          });
        }

        if (manual_apply) {
          openEmailApplicationModal();
        } else {
          setNotification({
            type: 'success',
            message: 'Your application has been sent',
            timeout: '5000',
          });
        }
      }

      if (isError && (isEasyApplication || isEmailEasyApplication)) {
        setNotification({
          type: 'error',
          message: message || ErrorMessage.Unexpected,
          timeout: '5000',
        });
      }

      setLoading(false);
    };

    const trackRoleBasicApplication = async () => {
      await ApiNoCache.clientGateway(`/roles/${id}/candidates/basic`, {
        method: 'POST',
      });
    };

    const openEmailApplicationModal = () => {
      setShowEmailApplicationModal(true);
    };

    const openEmailApplicationStatusModal = () => {
      setShowEmailApplicationStatusModal(true);
    };

    const toggleShowNotFullMatchModal = () => {
      setShowNotFullMatchModal(!showNotFullMatchModal);
    };

    const applyEasy = async () => {
      await postRoleApplication();
    };

    const applyDirect = async () => {
      await postRoleApplication();

      setShowDirectApplicationModal(true);
    };

    const applyEmail = async () => {
      await postRoleApplication();
    };

    const applyEmailEasy = async () => {
      const { closeUpImage } = profile;

      if (closeUpImage && isImageApproved(closeUpImage)) {
        await postRoleApplication();
      } else {
        openEmailApplicationStatusModal();
      }
    };

    const apply = async () => {
      if (isEasyApplication) {
        await applyEasy();
      } else if (isEmailEasyApplication) {
        await applyEmailEasy();
      } else if (castingCallType === TYPE.Email && !isProRegistered) {
        await applyEmail();
      } else {
        await applyDirect();
      }
    };

    return (
      <div id={`role-${id}`} className={styles.role} data-cy="role">
        <span className={styles.title}>{title}</span>
        {isFullMatch && (
          <div className={styles['role-match-container']}>
            <Image
              className={styles['role-match-icon']}
              src={'/assets/icons/icon-checkmark-1.svg'}
              alt="icon"
              width={19}
              height={16}
            />
            <span>Full match</span>
          </div>
        )}
        <span
          className={cn(styles.gender, {
            [styles['role-criteria-mismatch']]:
              userType === 'talent' && !criteria.gender,
          })}
        >
          {gender}
        </span>
        <span
          className={cn(styles.age, {
            [styles['role-criteria-mismatch']]:
              userType === 'talent' && !criteria.age,
          })}
        >
          {age} y.o.
        </span>
        <span
          className={cn(styles.ethnicity, {
            [styles['role-criteria-mismatch']]:
              userType === 'talent' &&
              !criteria.ethnicity &&
              !allEthnicitiesAccepted,
          })}
        >
          {allEthnicitiesAccepted
            ? 'All ethnicities'
            : ethnicities.map((ethnicity, key) => (
                <span key={key}>{ethnicity.name} </span>
              ))}
        </span>
        <p>{description}</p>
        {showApplyButton && (
          <div className={styles['role-button-container']}>
            <Button
              kind={userType === 'talent' ? 'secondary' : 'primary'}
              label={isEasyApplicationSent ? 'Application sent' : 'Apply'}
              minWidth="240px"
              onClick={applyToRole}
              disabled={isEasyApplicationSent || loading}
              dataCy="role-apply-btn"
            />
          </div>
        )}
        {showDirectApplicationModal && (
          <ModalDirectApplication
            onClose={closeDirectApplicationModal}
            type={castingCallType}
            website={castingCallWebsite}
            location={castingCallLocation}
            address={castingCallAddress}
            phone={castingCallPhone}
            dates={dates || []}
          />
        )}
        {showEmailApplicationModal && (
          <ModalEmailApplication
            profile={profile}
            onClose={closeEmailApplicationModal}
            email={email}
            emailSubject={emailSubject}
            castingCallTitle={castingCallTitle}
            roleTitle={title}
          />
        )}
        {showEmailApplicationStatusModal && (
          <ModalEmailApplicationStatus
            profile={profile}
            onClose={closeEmailApplicationStatusModal}
          />
        )}
        {showNotFullMatchModal && (
          <ModalNotFullMatch
            showButtons={castingCallType !== TYPE.Email}
            onClose={toggleShowNotFullMatchModal}
            criteria={{
              gender: !criteria.gender ? gender : null,
              age: !criteria.age ? age : null,
              ethnicity: !criteria.ethnicity ? ethnicities : null,
              location: !criteria.location ? castingCallLocation : null,
            }}
            onApply={async () => {
              toggleShowNotFullMatchModal();
              await apply();
            }}
          />
        )}
      </div>
    );
  },
);

Role.displayName = 'Role';

export default Role;
