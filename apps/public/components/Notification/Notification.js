import { memo, useEffect } from 'react';
import styles from './Notification.module.scss';
import IconClose from '../../public/assets/icons/icon-close-1.svg';
import cn from 'classnames';
import { Modal } from '../index';

const Notification = ({
  type = 'info',
  message,
  children,
  onClose,
  timeout = false,
  displayModal = false,
}) => {
  useEffect(() => {
    if (timeout) {
      const timer = setTimeout(() => {
        onClose();
      }, timeout);

      return () => clearTimeout(timer);
    }
  }, [onClose, timeout]);

  return (
    <>
      {displayModal ? (
        <Modal backdropClose onClose={onClose}>
          {message || children}
        </Modal>
      ) : (
        <div className={cn(styles.container, styles[type])}>
          <div className={styles.content}>{message || children}</div>
          {onClose && (
            <div className={styles.close} onClick={onClose}>
              <IconClose />
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default memo(Notification);
