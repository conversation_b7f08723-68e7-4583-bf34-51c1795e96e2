import React, { memo } from 'react';
import styles from './Loading.module.scss';
import classNames from 'classnames';

const Loading = ({ padding = '', minHeight = '40px', color = 'midnight' }) => {
  return (
    <div
      className={styles.container}
      style={{
        minHeight,
        padding,
      }}
    >
      <div className={classNames(styles.bouncer, styles[`${color}`])} />
      <div className={classNames(styles.bouncer, styles[`${color}`])} />
      <div className={classNames(styles.bouncer, styles[`${color}`])} />
      <div className={classNames(styles.bouncer, styles[`${color}`])} />
      <div className={classNames(styles.bouncer, styles[`${color}`])} />
    </div>
  );
};

export default memo(Loading);
