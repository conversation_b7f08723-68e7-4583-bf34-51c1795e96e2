@import 'styles/variables';

.categories-wrapper {
  display: flex;
  flex: 1;
  min-width: 0;
  gap: 8px;
  flex-wrap: wrap;
}

.category-list {
  display: flex;
  min-width: 0;
  gap: 8px;
  flex: 1;

  &.collapse {
    .categories-wrapper {
      flex-wrap: nowrap;
      overflow: hidden;
    }
  }
}

.category-item {
  display: inline-block;
  padding: 0 $space-10;
  line-height: 23px;
  border: 1px solid $grey-20;
  border-radius: 40px;
  font-weight: 300;
  font-size: 12px;
  color: $grey-80;
  white-space: nowrap;

  &.main {
    border-color: $grey-80;
    font-weight: 500;
  }
}

.link {
  &:hover {
    text-decoration: none;
    border-color: $grey-100;
  }
}
