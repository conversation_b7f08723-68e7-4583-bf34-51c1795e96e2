import styles from './CategoryList.module.scss';
import cn from 'classnames';
import { createRef, useEffect, useRef, useState } from 'react';
import Link from 'next/link';
import { CC_CATEGORY } from '../../constants/castingCalls';

const CategoryList = ({
  mainCategory,
  additionalCategories = [],
  collapse = true,
  clickable = false,
}) => {
  const containerElement = useRef();
  const counterElement = useRef();
  const gapWidth = 8;
  const counterElementStartWidth = 35;
  const [visibleCategories, setVisibleCategories] = useState([
    ...additionalCategories,
  ]);
  const [itemWidths, setItemWidths] = useState([]);
  const [hiddenCount, setHiddenCount] = useState(0);
  const [refsLoading, setRefsLoading] = useState(true);
  const itemElementRefs = useRef([]);

  itemElementRefs.current = additionalCategories.map(
    (_, i) => itemElementRefs.current[i] ?? createRef(),
  );

  const getWidths = () => {
    if (!itemWidths.length) {
      const widths = [];

      itemElementRefs.current.forEach((item) => {
        if (item.current) {
          widths.push(item.current.clientWidth);
        }
      });
      setItemWidths(widths);
    }
  };

  const calculateVisibleCategories = () => {
    const counterElementWidth = counterElement.current
      ? counterElement.current.clientWidth
      : counterElementStartWidth;
    const containerWidth =
      containerElement.current.clientWidth - (counterElementWidth + gapWidth);

    const calculatedVisibleCategories = [...additionalCategories];

    let containerScrollWidth = itemWidths.reduce(
      (prev, curr) => prev + curr + gapWidth,
      0,
    );

    while (containerWidth < containerScrollWidth) {
      calculatedVisibleCategories.pop();
      containerScrollWidth -=
        itemWidths[calculatedVisibleCategories.length] + gapWidth;
    }

    setHiddenCount(
      additionalCategories.length - calculatedVisibleCategories.length,
    );
    setVisibleCategories([...calculatedVisibleCategories]);
  };

  const handleResize = () => {
    if (additionalCategories?.length && collapse && containerElement.current) {
      calculateVisibleCategories();
    }
  };

  useEffect(() => {
    setVisibleCategories([...additionalCategories]);
  }, [additionalCategories]);

  useEffect(() => {
    if (collapse) {
      const observer = new ResizeObserver(() => {
        handleResize();
      });

      observer.observe(containerElement.current);

      return () => {
        observer.disconnect();
      };
    }
  }, [containerElement.current?.offsetWidth]);

  useEffect(() => {
    if (additionalCategories.length && collapse) {
      getWidths();
      handleResize();
    } else if (!additionalCategories.length && collapse) {
      setVisibleCategories([]);
    }
  }, [additionalCategories, refsLoading, itemWidths]);

  useEffect(() => {
    if (
      additionalCategories.length &&
      refsLoading &&
      itemElementRefs.current[0].current &&
      collapse
    ) {
      setRefsLoading(false);
    }
  }, [itemElementRefs.current]);

  return (
    <div
      className={cn(styles['category-list'], collapse && styles['collapse'])}
    >
      {mainCategory && collapse && clickable && (
        <Link
          className={cn(styles['category-item'], styles['main'], styles.link)}
          href={`/castingcalls/${
            CC_CATEGORY[mainCategory.name.toLowerCase()] || ''
          }`}
        >
          {mainCategory.name}
        </Link>
      )}
      {mainCategory && collapse && !clickable && (
        <div className={cn([styles['category-item'], styles['main']])}>
          {mainCategory.name}
        </div>
      )}
      <div className={styles['categories-wrapper']} ref={containerElement}>
        {mainCategory && !collapse && clickable && (
          <Link
            className={cn(styles['category-item'], styles['main'], styles.link)}
            href={`/castingcalls/${
              CC_CATEGORY[mainCategory.name.toLowerCase()] || ''
            }`}
          >
            {mainCategory.name}
          </Link>
        )}
        {mainCategory && !collapse && !clickable && (
          <div className={cn([styles['category-item'], styles['main']])}>
            {mainCategory.name}
          </div>
        )}
        {visibleCategories.map((category, i) =>
          clickable ? (
            <Link
              key={category.id}
              className={cn(styles['category-item'], styles.link)}
              ref={itemElementRefs.current[i]}
              href={`/castingcalls/${
                CC_CATEGORY[category.name.toLowerCase()] || ''
              }`}
            >
              {category.name}
            </Link>
          ) : (
            <div
              key={category.id}
              className={styles['category-item']}
              ref={itemElementRefs.current[i]}
            >
              {category.name}
            </div>
          ),
        )}
        {hiddenCount > 0 && (
          <div className={styles['category-item']} ref={counterElement}>
            +{hiddenCount}
          </div>
        )}
      </div>
    </div>
  );
};

export default CategoryList;
