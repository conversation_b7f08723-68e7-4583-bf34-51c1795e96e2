@import '../../styles/variables';
@import '../../styles/mixins';

.mobile-header {
  position: sticky;
  top: 0;
  width: 100%;
  padding: $space-15 $space-20;
  display: grid;
  grid-template-columns: 0.2fr 1fr 0.2fr;
  background-color: $white;
  z-index: $z-index-mobile-header;
  border-bottom: 1px solid $grey-60;

  &.show-only-title {
    grid-template-columns: 1fr;
  }

  &.margin-override {
    margin: 0 (-$space-20);
    width: unset;
  }

  .title {
    text-align: center;
    font-weight: 300;

    h1 {
      margin: 0;
      font-size: 16px;
      color: $black;
      line-height: 1.5;
      font-weight: 300;
      text-align: center;
    }
  }

  @include desktop {
    display: none;
  }

  &.hide-on-tablet {
    @include tablet {
      display: none;
    }
  }
}

.icon-container {
  display: flex;
  align-items: center;
}

.icon {
  transform: rotate(180deg);
  cursor: pointer;
}

.link {
  color: $blue-100;
  font-weight: 400;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
