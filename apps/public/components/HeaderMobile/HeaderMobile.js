import { memo } from 'react';
import styles from './HeaderMobile.module.scss';
import Image from 'next/image';
import cn from 'classnames';
import Link from 'next/link';

const HeaderMobile = ({
  onClose,
  hideOnTablet = false,
  showIcon = true,
  showLink = false,
  marginOverride = false,
  children,
  href,
  label = '',
}) => {
  return (
    <div
      className={cn(styles['mobile-header'], {
        [styles['hide-on-tablet']]: hideOnTablet,
        [styles['show-only-title']]: !showIcon,
        [styles['margin-override']]: marginOverride,
      })}
    >
      {showIcon && (
        <div onClick={onClose} className={styles['icon-container']}>
          <Image
            className={styles.icon}
            src={'/assets/icons/icon-angle-5.svg'}
            width={16}
            height={16}
            alt=""
          />
        </div>
      )}
      <div className={styles.title}>{children}</div>
      {showLink && (
        <Link href={href} className={styles.link}>
          {label}
        </Link>
      )}
    </div>
  );
};

export default memo(HeaderMobile);
