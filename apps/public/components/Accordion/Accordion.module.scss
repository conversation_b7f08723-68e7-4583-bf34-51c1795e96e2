@import 'styles/variables';

.container {
  position: sticky;
  top: 71px;
  border-radius: 0 0 $space-10 $space-10;
  box-shadow: $shadow-accordion;
}

.accordion {
  display: flex;
  flex-direction: column;
  min-width: 320px;
  height: fit-content;
  padding-bottom: $space-10;
  max-height: 610px;
  overflow: auto;
}

.accordion-content-item {
  display: flex;
  align-items: center;
  padding: 0 $space-20 0 $space-40;
  line-height: 30px;
  font-size: 14px;
  cursor: pointer;
  font-weight: 300;

  &:hover {
    background-color: $grey-10;
    text-decoration: none;
  }
}

.accordion-btn-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: $space-30 $space-20;
  border-top: 1px solid $grey-20;
}
