import { memo } from 'react';
import { AccordionItem, Button } from '../index';
import styles from './Accordion.module.scss';
import Link from 'next/link';

const Accordion = ({
  accordionItems,
  showButton = true,
  buttonLabel,
  buttonMinWidth,
  onButtonClick,
}) => {
  return (
    <div className={styles.container}>
      <div className={styles.accordion}>
        {accordionItems.map(({ title, items }) => (
          <AccordionItem key={title} title={title}>
            {items.map(({ title, selector }) => (
              <Link
                key={title}
                href={selector}
                scroll={false}
                className={styles['accordion-content-item']}
              >
                {title}
              </Link>
            ))}
          </AccordionItem>
        ))}
      </div>
      {showButton && (
        <div className={styles['accordion-btn-container']}>
          <Button
            onClick={onButtonClick}
            label={buttonLabel}
            minWidth={buttonMinWidth}
            shadow={false}
          />
        </div>
      )}
    </div>
  );
};

export default memo(Accordion);
