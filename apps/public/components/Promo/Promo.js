import { memo } from 'react';
import { ArticleBanner } from '../index';
import styles from './Promo.module.scss';
import Image from 'next/image';
import cn from 'classnames';
import { useAnalytics } from 'use-analytics';
import {
  GTM_EVENTS,
  GTM_CATEGORIES,
  GTM_ACTIONS,
} from '../../constants/analytics';
import Link from 'next/link';
import Carousel from '../Carousel/Carousel';
import { Amp } from '../../services/amp';
import { getPromoImages, getReducedPromos } from '../../utils/promoHelper';
import { ARTICLE_CATEGORY } from '../../constants/articles';

const Promo = ({ promos = [] }) => {
  const { track } = useAnalytics();

  const fixLink = (link) => {
    let fixed = link.replace('castingcalls', 'castingcall');

    if (!fixed.startsWith('/')) {
      fixed = '/' + fixed;
    }

    // ToDo: Use new categories statically
    // maybe backend link fixing task
    fixed = fixed
      .replace(
        `/${ARTICLE_CATEGORY.lessons}/`,
        `/blog/${ARTICLE_CATEGORY.auditionTips}/`,
      )
      .replace(
        `/${ARTICLE_CATEGORY.news}/`,
        `/blog/${ARTICLE_CATEGORY.industryNews}/`,
      );

    return fixed;
  };

  const formattedPromos = promos.map((promo) => ({
    ...promo,
    link: fixLink(promo.link),
    ...getPromoImages(promo.images),
  }));

  const trackBanner = (href) => {
    track(GTM_EVENTS.interaction, {
      target: GTM_CATEGORIES.banner,
      action: GTM_ACTIONS.click,
      label: href,
    });
    Amp.track(Amp.events.topSliderBannerClicked, {
      banner_href: href,
    });
  };

  return (
    <>
      <ArticleBanner />
      {promos.length > 0 && (
        <>
          <div className={styles.mobile} data-cy="promo-slider">
            <Carousel
              className="carousel-promos-mobile"
              loop
              slidesToScroll={1}
              enableArrowNavigation
            >
              {formattedPromos.map(
                ({ link, name, mobileImageHref, url }, index) => (
                  <Link
                    href={url || link}
                    key={index}
                    onClick={() => trackBanner(mobileImageHref)}
                    className={styles['slider-slide']}
                    prefetch={false}
                    target={url ? '_blank' : '_self'}
                  >
                    <div key={index} className={styles['slider-slide-item']}>
                      <Image
                        src={mobileImageHref}
                        priority
                        alt={name}
                        fill
                        unoptimized
                      />
                    </div>
                  </Link>
                ),
              )}
            </Carousel>
          </div>
          <div className={styles.desktop} data-cy="promo-slider">
            <Carousel
              className="carousel-promos"
              enableArrowNavigation
              slidesToScroll={1}
              delay={3000}
              loop
              playOnInit
            >
              {getReducedPromos(formattedPromos, 5).map((item, i) => (
                <div key={i} className={styles['slider-slide']}>
                  {item.map(({ link, name, desktopImageHref, url }, index) => (
                    <Link
                      key={index}
                      href={url || link}
                      onClick={() => trackBanner(desktopImageHref)}
                      className={cn(styles['slider-slide-item'], {
                        [styles['slider-slide-item-middle']]: index === 2,
                      })}
                      prefetch={false}
                      target={url ? '_blank' : '_self'}
                    >
                      <Image
                        src={desktopImageHref}
                        priority
                        alt={name}
                        fill
                        unoptimized
                      />
                    </Link>
                  ))}
                </div>
              ))}
            </Carousel>
          </div>
        </>
      )}
    </>
  );
};

export default memo(Promo);
