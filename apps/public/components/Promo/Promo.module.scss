@import '../../styles/mixins';
@import '../../styles/variables';

.slider-slide {
  display: grid;
  grid-template-rows: repeat(2, 1fr);
  grid-template-columns: 1fr 2.06fr 1fr;
  grid-gap: $space-20;
  overflow: hidden;
}

.slider-slide-item {
  position: relative;
  overflow: hidden;
  background-color: $grey-10;

  &:nth-child(1) {
    grid-row-start: 1 / 3;
    grid-column-start: 2 / 3;
    padding-top: 54.3%;
  }

  img {
    min-width: 100%;
    min-height: 100%;
    width: auto;
    height: auto;
    transition: all 0.3s ease-in-out;
  }

  &:hover {
    img {
      transform: scale(1.1);
    }
  }
}

.slider-slide-item-middle {
  grid-row: 1 / 3;
  grid-column: 2 / 3;
}

.desktop {
  display: none;

  @include tablet {
    margin-top: $space-20;
    display: block;
  }
}

.mobile {
  margin-top: $space-20;

  .slider-slide {
    display: block;
  }

  @include tablet {
    display: none;
  }
}

.article-banner + .mobile {
  padding-top: $space-20;
}
