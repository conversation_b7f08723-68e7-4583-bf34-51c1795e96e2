@import '../../../styles/variables';

.switch-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 300;
  gap: $space-5;

  &:has(.switch-input:checked) {
    font-weight: 700;
  }
}

.switch {
  position: relative;
  display: inline-block;
  width: 36px;
  height: 14px;
}

.switch-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-slider {
  position: absolute;
  cursor: pointer;
  inset: 0;
  background-color: $white;
  box-shadow: $shadow-switch;
  transition: 0.4s;
  border-radius: 34px;

  &::before {
    position: absolute;
    content: '';
    height: 24px;
    width: 24px;
    left: 0;
    bottom: -5px;
    background-color: $white;
    box-shadow: $shadow-switch;
    transition: 0.4s;
    border-radius: 50%;
  }

  &:hover {
    background-color: $green-100;
    background-image: $gradient-green-blue;
    box-shadow: none;
  }
}

.switch-input:checked + .switch-slider {
  background-color: $green-100;
  background-image: $gradient-green-blue;
  box-shadow: none;
}

.switch-input:checked + .switch-slider::before {
  transform: translateX(14px);
  box-shadow: $shadow-switch-active;
  background-color: $white;
}
