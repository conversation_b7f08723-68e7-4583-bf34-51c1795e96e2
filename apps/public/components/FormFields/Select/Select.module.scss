@import '../../../styles/variables';

@keyframes errorSlide {
  from {
    height: 0;
    opacity: 0;
    transform: translate3d(0, -15%, 0);
  }

  to {
    height: auto;
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes growOut {
  0% {
    transform: scale(0);
  }

  100% {
    transform: scale(1);
  }
}

.select-container {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: 50px;
  justify-content: flex-end;
}

.focus-border {
  width: 100%;
  position: absolute;
  bottom: -1px;

  &::before {
    display: block;
    border-bottom: 3px solid $black;
    content: '';
    transition: transform 300ms cubic-bezier(0, 0, 0.2, 1) 0ms;
    transform: scaleX(0);
  }
}

.select-trigger {
  width: 10px;
  height: 6px;
  background: url('#{$assetUrl}/assets/icons/icon-caret-down.svg') center center
    no-repeat;
  background-size: cover;
  justify-self: flex-end;
  transition: all 200ms ease-in-out;

  &.open {
    transform: rotate(180deg);
  }
}

.select-field {
  width: 100%;
  border-bottom: 1px solid $grey-80;
  padding: 22.5px 8px 4px 0;
  font-size: 20px;
  cursor: pointer;
  line-height: 1.1;
  background-color: $white;
  background-image: $gradient-ghost-white;
  align-items: center;
  position: relative;
  display: flex;

  &.open {
    .focus-border::before {
      transform: scaleX(1);
    }
  }

  &.error {
    border-color: $red-80;

    .focus-border::before {
      border-color: $red-80;
    }

    .select-trigger {
      filter: invert(41%) sepia(95%) saturate(4651%) hue-rotate(344deg)
        brightness(106%) contrast(91%);
    }
  }

  &:hover,
  &:has(.select:focus) {
    .focus-border::before {
      transform: scaleX(1);
    }
  }
}

.select {
  appearance: none;
  background-color: transparent;
  border: none;
  padding: 0 2px 0 0;
  margin: 0;
  width: 100%;
  font-size: 16px;
  cursor: inherit;
  outline: none;
  text-overflow: ellipsis;
  font-weight: 700;
  text-transform: capitalize;

  &:disabled {
    color: $black;
  }
}

.label {
  position: absolute;
  left: 0;
  margin: auto;
  cursor: pointer;
  font-size: 16px;
  font-weight: 300;
  color: $black;
  transform-origin: left;
  transition: all 100ms ease-in-out;
  line-height: 0;

  &.floating-label {
    font-size: 14px;
    font-weight: 600;
    top: 4px;
  }
}

.options {
  min-width: 100%;
  position: absolute;
  top: 50px;
  background-color: white;
  border: 1px solid $grey-40;
  max-height: 256px;
  overflow: auto;
  overflow-x: hidden;
  z-index: 9;
  animation: growOut 200ms ease-in-out forwards;
  transform-origin: top center;
  border-radius: 0 0 10px 10px;
  box-shadow: $shadow-box-dropdown;
}

.option {
  font-weight: 300;
  font-size: 14px;
  padding: 7px $space-10;
  min-height: 30px;
  cursor: pointer;

  &:hover,
  &:focus {
    background: $grey-10;
    outline: none;
  }
}

.selected-option {
  color: $black;
  font-weight: 600;
  background-color: $grey-10;

  &:focus {
    background-color: $grey-20;
  }

  &:hover {
    background-color: $grey-20;
  }
}

.select-error {
  color: $red-80;

  .select {
    color: $red-80;
  }

  .label {
    color: $red-80;
  }

  .option {
    color: $black;
  }
}

.error-message {
  position: absolute;
  top: 54px;
  align-items: center;
  animation: errorSlide 200ms ease-in-out;
  color: $red-80;
  display: flex;
  font-size: 12px;
  font-weight: 300;
  text-align: initial;
  width: 100%;
  z-index: 1;
  line-height: 1;
}

.select-container.slim {
  height: auto;

  .select-field {
    padding: 0;
    background-color: transparent;
    background-image: none;
  }

  .label {
    font-weight: 600;

    &.floating-label {
      display: none;
    }
  }

  .options {
    top: 25px;
  }
}
