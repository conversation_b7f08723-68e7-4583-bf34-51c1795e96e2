import { useField } from 'formik';
import { PasswordInput } from '../index';
import React, { memo, useEffect } from 'react';

const PasswordInputFormik = (props) => {
  const { name, ...restProps } = props;
  const [{ onChange, onBlur, value }, { error, touched }, { setTouched }] =
    useField(name);

  // Autofill validation trigger
  useEffect(() => {
    if (value && !touched) {
      setTouched(true).catch((reason) => {
        console.error(`Field validation failed: ${reason}`);
      });
    }
  }, [value, touched, setTouched]);

  return (
    <PasswordInput
      name={name}
      value={value}
      error={error}
      isTouched={touched}
      onChange={onChange}
      onBlur={onBlur}
      {...restProps}
    />
  );
};

export default memo(PasswordInputFormik);
