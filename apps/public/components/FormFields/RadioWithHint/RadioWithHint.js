import styles from './RadioWithHint.module.scss';
import { Radio } from '../../index';

const RadioWithHint = ({ name, value, label, checked, onChange, hint }) => {
  return (
    <div>
      <label className={styles.container} htmlFor={value}>
        <Radio
          name={name}
          value={value}
          color="midnight"
          checked={checked}
          onChange={onChange}
        />
        <span>{label}</span>
      </label>
      {hint && <div className={styles.hint}>{hint}</div>}
    </div>
  );
};

export default RadioWithHint;
