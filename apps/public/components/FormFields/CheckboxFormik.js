import { useField } from 'formik';
import { Checkbox } from '../index';
import React, { memo, useEffect } from 'react';

const CheckboxFormik = (props) => {
  const { name, children, ...restProps } = props;
  const [{ onChange, onBlur, value }, { error, touched }, { setTouched }] =
    useField(name);

  // Autofill validation trigger
  useEffect(() => {
    if (value && !touched) {
      setTouched(true).catch((reason) => {
        console.error(`Field validation failed: ${reason}`);
      });
    }
  }, [value, touched, setTouched]);

  return (
    <Checkbox
      name={name}
      value={value}
      error={error}
      isTouched={touched}
      onChange={onChange}
      onBlur={onBlur}
      {...restProps}
    >
      {children}
    </Checkbox>
  );
};

export default memo(CheckboxFormik);
