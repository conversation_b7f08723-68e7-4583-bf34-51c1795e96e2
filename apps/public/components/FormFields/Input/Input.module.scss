@import '../../../styles/variables';

@keyframes errorSlide {
  from {
    height: 0;
    opacity: 0;
    transform: translate3d(0, -15%, 0);
  }

  to {
    height: auto;
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.container {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
}

.prefix {
  font-size: 1em;
  font-weight: 700;
  line-height: normal;
  padding-top: 16px;
  display: flex;
  align-items: center;
}

.field {
  border-bottom: 1px solid $grey-80;
  box-sizing: border-box;
  display: flex;
  height: 50px;
  overflow: hidden;
  position: relative;
  width: 100%;

  &:focus-within {
    border-bottom: 3px solid $violet-60;
  }
}

.input {
  border: 0;
  font-size: 1em;
  font-weight: 700;
  outline: none;
  padding: 16px 0 0;
  width: 100%;
}

.input:focus + label {
  transform: scale(0.7) translate3d(0, -40%, 0);
}

.label {
  align-items: flex-end;
  bottom: 0;
  color: grey;
  display: flex;
  line-height: 2em;
  font-weight: 300;
  left: 0;
  margin: auto;
  pointer-events: none;
  position: absolute;
  top: 0;
  transform-origin: left;
  transition: transform 100ms ease-in-out;

  &.float {
    transform: scale(0.7) translateY(-50%);
  }

  &.required {
    ::after {
      content: ' *';
      color: $red-60;
    }
  }
}

.hint {
  align-items: center;
  color: $grey-80;
  justify-content: space-between;
  display: flex;
  padding-top: 3px;
  font-size: 12px;
  font-weight: 300;
  text-align: initial;
  width: 100%;
  position: absolute;
  bottom: -20px;
}

.counter {
  color: $black;
}

.error-message {
  position: absolute;
  top: 54px;
  align-items: center;
  animation: errorSlide 200ms ease-in-out;
  color: $red-80;
  display: flex;
  font-size: 12px;
  font-weight: 300;
  text-align: initial;
  width: 100%;
  z-index: 1;
  line-height: 1;
}

.input-error,
.prefix-error,
.label-error {
  color: $red-80;
}

.field-error {
  border-bottom: 1px solid $red-80;
}

.theme-dark {
  .field {
    &:focus-within {
      border-bottom: 3px solid grey;
    }
  }

  .hint,
  .counter {
    color: grey;
  }
}
