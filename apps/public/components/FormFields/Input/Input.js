import cn from 'classnames';
import React, { memo } from 'react';
import styles from './Input.module.scss';

function Input({
  type = 'text',
  onChange = () => {},
  value = '',
  name,
  onBlur,
  onFocus,
  error,
  hint,
  isTouched,
  placeholder,
  className,
  autoComplete,
  charCount,
  onKeyDown,
  onClick,
  styleTheme = 'light',
  required,
  dataCy,
  prefix,
}) {
  const showError = isTouched && error;

  return (
    <div
      className={cn(styles.container, className, styles[`theme-${styleTheme}`])}
    >
      <div className={cn(styles.field, showError && styles['field-error'])}>
        {!!value.toString().length && prefix && (
          <div
            className={cn(styles.prefix, showError && styles['prefix-error'])}
          >
            {prefix}&nbsp;
          </div>
        )}
        <input
          autoComplete={autoComplete}
          className={cn(styles.input, showError && styles['input-error'])}
          name={name}
          id={name}
          type={type}
          onChange={onChange}
          value={value}
          onBlur={onBlur}
          onFocus={onFocus}
          onKeyDown={onKeyDown}
          data-cy={dataCy}
          onClick={onClick}
        />

        <label
          htmlFor={name}
          className={cn(styles.label, {
            [styles.float]: value.toString().length,
            [styles.required]: required,
            [styles['label-error']]: showError,
          })}
        >
          <span>{placeholder}</span>
        </label>
      </div>

      <div className={styles.hint}>
        {hint && !showError && <span>{hint}</span>}
        {charCount && !showError && (
          <span className={styles.counter}>
            {value.length} / {Intl.NumberFormat().format(charCount)}
          </span>
        )}
      </div>

      {showError && typeof error !== 'boolean' && (
        <div className={styles['error-message']}>{error}</div>
      )}
    </div>
  );
}

export default memo(Input);
