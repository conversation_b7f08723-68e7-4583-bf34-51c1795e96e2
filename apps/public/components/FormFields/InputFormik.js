import React, { memo, useEffect } from 'react';
import { useField } from 'formik';

import Input from './Input/Input';

const InputFormik = (props) => {
  const { name } = props;
  const [{ onChange, onBlur, value }, { error, touched }, { setTouched }] =
    useField(name);

  // Autofill validation trigger
  useEffect(() => {
    if (value && !touched) {
      setTouched(true).catch((reason) => {
        console.error(`Field validation failed: ${reason}`);
      });
    }
  }, [value, touched, setTouched]);

  return (
    <Input
      type="text"
      onBlur={onBlur}
      onChange={onChange}
      value={value}
      error={error}
      isTouched={touched}
      {...props}
    />
  );
};

export default memo(InputFormik);
