import { memo } from 'react';
import styles from './Radio.module.scss';
import cn from 'classnames';

const Radio = ({
  readOnly = false,
  color = 'emerald',
  checked,
  name,
  value,
  onChange,
}) => {
  return (
    <label className={cn(styles['radio-input-label'], styles[color])}>
      <input
        className={styles['radio-input']}
        readOnly={readOnly}
        type="radio"
        id={value}
        name={name}
        value={value}
        onChange={onChange}
        checked={checked}
      />
      <span className={cn(styles['radio-input-checkmark'], styles[color])} />
    </label>
  );
};

export default memo(Radio);
