@import '../../../styles/variables';

.radio-input-label {
  position: relative;
  cursor: pointer;
  user-select: none;
  border: 1px solid;
  padding: 1px;
  width: 14px;
  height: 14px;
  border-radius: 50%;

  &:has(input[type='radio']:checked) {
    &.emerald {
      border-color: $grey-40;
    }

    &.midnight {
      border-color: $violet-60;
    }

    &.white {
      border-color: $white;
    }
  }
}

.radio-input-checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 14px;
  width: 14px;

  &::after {
    content: '';
    position: absolute;
    display: none;
    top: 1.5px;
    left: 1.5px;
    width: 9px;
    height: 9px;
    border-radius: 50%;
  }

  &.emerald::after {
    background-color: $green-80;
  }

  &.midnight::after {
    background-color: $violet-60;
  }

  &.white::after {
    background-color: $white;
  }
}

.radio-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;

  &:checked ~ .radio-input-checkmark::after {
    display: block;
  }
}
