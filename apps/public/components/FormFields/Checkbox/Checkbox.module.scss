@import '../../../styles/variables';

.container {
  display: flex;
  position: relative;
  padding-left: 24px;
  cursor: pointer;
  font-size: 22px;
  user-select: none;
  align-content: center;
}

.container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  left: 0;
  background-color: $white;
  border: 1px solid $grey-80;
  width: 16px;
  height: 16px;
}

.checkmark::after {
  content: '';
  position: absolute;
  display: none;
}

.container .checkmark::after {
  left: 4px;
  top: 0;
  width: 3px;
  height: 8px;
  border: solid $white;
  border-top-left-radius: 5px;
  border-width: 0 2.5px 2.5px 0;
  transform: rotate(45deg);
}

.error {
  .checkmark {
    border: 1px solid $red-80;
  }
}

.container input:checked ~ .checkmark {
  background-color: $violet-60;
  border: 1px solid transparent;
}

.container input:checked ~ .checkmark::after {
  display: block;
}

.checkbox-container {
  height: 24px;
  display: inline-flex;
  align-items: center;
}

.container.theme-dark input ~ .checkmark {
  background: $violet-80;
  border: 1px solid grey;
  width: 20px;
  height: 20px;
}

.container.theme-dark input ~ .checkmark::after {
  top: 3px;
  left: 6px;
}
