import { memo } from 'react';
import styles from './Checkbox.module.scss';
import cn from 'classnames';

const Checkbox = ({
  children,
  onChange,
  value,
  name,
  error,
  onBlur,
  isTouched,
  styleTheme = 'light',
}) => {
  const showError = isTouched && error;

  return (
    <label
      className={cn(styles.container, styles[`theme-${styleTheme}`], {
        [styles.error]: showError,
      })}
    >
      <div className={styles['checkbox-container']}>
        <input
          name={name}
          id={name}
          type="checkbox"
          onChange={onChange}
          checked={value}
          onBlur={onBlur}
        />
        <span className={styles.checkmark}></span>
      </div>
      {children}
    </label>
  );
};

export default memo(Checkbox);
