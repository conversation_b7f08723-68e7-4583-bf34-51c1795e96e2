import { memo, useState } from 'react';
import styles from './ForgotPasswordForm.module.scss';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import { Button, InputFormik, Loading } from '../index';
import ApiNoCache from '../../services/apiNoCache';
import Link from 'next/link';
import { EMAIL_REGEX, ErrorMessage } from '../../constants/form';

const ForgotPasswordForm = ({ children }) => {
  const [error, setError] = useState('');
  const [passwordSent, setPasswordSent] = useState(false);
  const [email, setEmail] = useState('');

  const sendForgotPasswordEmail = async (email) => {
    return (
      await ApiNoCache.clientAPIRoute(`/password/forgot`, {
        method: 'POST',
        body: JSON.stringify({ email }),
      })
    ).data;
  };

  const onSubmit = async (values) => {
    const response = await sendForgotPasswordEmail(values.email);

    if (response.status !== 'error') {
      setPasswordSent(true);
      setEmail(values.email);
    } else {
      setError(response.message);
    }
  };

  const validationSchema = Yup.object({
    email: Yup.string()
      .required(ErrorMessage.EmailRequired)
      .matches(EMAIL_REGEX, ErrorMessage.EmailPattern),
  });

  const resend = async () => {
    setEmail('');
    setPasswordSent(false);
  };

  const onFocus = () => {
    setError('');
  };

  return (
    <>
      {passwordSent ? (
        <div className={styles['form-sent']}>
          <h1 data-cy="password-recovery-email-sent">Email sent</h1>
          <p>
            We have sent you an email at <span>{email}</span>. <br />
            Please, check your inbox or spam folder. If you didn&apos;t receive
            this email, you can resend it.
          </p>
          <Button
            className={styles.button}
            color="blue"
            type="submit"
            label="Login"
            minWidth="220px"
            onClick={() => {
              window.location.href = `/login`;
            }}
          />
          <p>
            <span className={styles.link} onClick={resend}>
              Resend password
            </span>
          </p>
        </div>
      ) : (
        <Formik
          initialValues={{ email: '' }}
          onSubmit={onSubmit}
          validationSchema={validationSchema}
        >
          {({ isValid, dirty, isSubmitting }) => (
            <div className={styles['forgot-form-container']}>
              <div className={styles['title']}>
                {children || <h1>Login allcasting</h1>}
              </div>
              <Form className={styles.form}>
                <div className={styles['form-controls']}>
                  <InputFormik
                    className={styles.input}
                    name="email"
                    placeholder="Your email"
                    data-cy="password-recovery-email-input"
                    onFocus={onFocus}
                  />
                  {error && (
                    <small
                      className={styles['error-message']}
                      data-cy="password-recovery-error"
                    >
                      {error}
                    </small>
                  )}
                </div>
                <div className={styles['forgot-action-container-bottom']}>
                  {isSubmitting ? (
                    <Loading minHeight="40px" padding="0" />
                  ) : (
                    <div className={styles['btn-box']}>
                      <Button
                        className={styles.button}
                        color="blue"
                        disabled={!(isValid && dirty)}
                        type="submit"
                        label="Send Email"
                        minWidth="220px"
                        dataCy="recover-password-btn"
                      />
                    </div>
                  )}
                  <Link href={'/login'} className={styles.link}>
                    Actually, I remember it
                  </Link>
                </div>
              </Form>
            </div>
          )}
        </Formik>
      )}
    </>
  );
};

export default memo(ForgotPasswordForm);
