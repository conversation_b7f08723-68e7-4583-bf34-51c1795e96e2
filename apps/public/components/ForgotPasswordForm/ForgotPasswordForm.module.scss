@import '../../styles/variables';
@import '../../styles/mixins';

.forgot-form-container {
  padding: 0;
  box-shadow: unset;
  border-radius: 0;
  display: flex;
  flex-direction: column;
  gap: $space-20;
  width: 100%;

  @include desktop {
    width: 430px;
    box-shadow: $shadow-card-container;
    border-radius: 10px;
    padding: $space-40 $space-40 $space-30;
  }
}

.form {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 $space-10;
  position: relative;

  @include desktop {
    gap: $space-20;
  }
}

.form-controls {
  width: 100%;
  padding: $space-20 $space-20 $space-60;
  box-shadow: $shadow-form-control;
  border-radius: 3px;
  display: flex;
  flex-direction: column;
  gap: $space-20;

  @include desktop {
    box-shadow: unset;
    border-radius: 0;
    padding: 0 0 10px;
  }
}

.input {
  width: 100%;
}

.link {
  color: $blue-100;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.error-message {
  color: $red-60;
  text-align: center;
  font-size: 14px;
  font-weight: 300;
}

.forgot-action-container-bottom {
  display: flex;
  flex-direction: column;
  gap: $space-20;
  align-items: center;
  margin-top: -$space-20;

  .button:disabled {
    opacity: 0.5;
  }

  @include desktop {
    margin-top: 0;
  }
}

.btn-box {
  background-color: $white;
}

.title {
  display: none;

  @include desktop {
    display: initial;
  }
}

.forgot-action-container-top {
  display: flex;
  flex-direction: column;
  align-items: center;

  @include desktop {
    display: none;
  }
}

.form-sent {
  max-width: 440px;
  margin: 0 auto;
  text-align: center;
}

.form-sent > h1 {
  font-weight: 900;
}

.form-sent p span {
  color: #06c2ff;
  font-weight: 400;
}

.forgot-form-container .title h1 {
  font-weight: 700;
}
