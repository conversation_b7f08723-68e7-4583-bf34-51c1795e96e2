import { memo, useCallback, useEffect, useRef, useState } from 'react';
import styles from './AudioPlayer.module.scss';
import {
  AudioPlayerProgressBar,
  AudioPlayerTimer,
  AudioPlayerVolumeControl,
  AudioPlayerPlayControl,
  AudioPlayerTrack,
} from '../index';

const AudioPlayer = ({ url, isMobileFromUserAgent }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [timeProgress, setTimeProgress] = useState(0);
  const [duration, setDuration] = useState(0);

  const audioRef = useRef();
  const progressBarRef = useRef();
  const playAnimationRef = useRef();

  const repeat = useCallback(() => {
    const currentAudioRef = audioRef?.current;

    if (currentAudioRef) {
      const currentTime = currentAudioRef.currentTime;

      setTimeProgress(currentTime);
      progressBarRef.current.value = currentTime;
      progressBarRef.current.style.setProperty(
        '--range-progress',
        `${Math.ceil((progressBarRef.current.value / duration) * 100)}%`,
      );

      playAnimationRef.current = requestAnimationFrame(repeat);
    }
  }, [audioRef, duration, progressBarRef, setTimeProgress]);

  useEffect(() => {
    if (isPlaying) {
      audioRef.current.play();
    } else {
      audioRef.current.pause();
    }

    playAnimationRef.current = requestAnimationFrame(repeat);
  }, [isPlaying, audioRef, repeat]);

  const stop = () => {
    setIsPlaying(false);
  };

  const start = () => {
    setIsPlaying(true);
  };

  return (
    <div className={styles['audio-player']}>
      <AudioPlayerPlayControl
        audioRef={audioRef}
        isPlaying={isPlaying}
        start={start}
        stop={stop}
      />
      <AudioPlayerProgressBar
        progressBarRef={progressBarRef}
        audioRef={audioRef}
      />
      <AudioPlayerVolumeControl
        audioRef={audioRef}
        isMobileFromUserAgent={isMobileFromUserAgent}
      />
      <AudioPlayerTimer timeProgress={timeProgress} />
      <AudioPlayerTrack
        url={url}
        audioRef={audioRef}
        onEnded={stop}
        setDuration={setDuration}
        progressBarRef={progressBarRef}
      />
    </div>
  );
};

export default memo(AudioPlayer);
