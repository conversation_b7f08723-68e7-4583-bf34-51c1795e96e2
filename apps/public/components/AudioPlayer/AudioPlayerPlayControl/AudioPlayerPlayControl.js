import { memo } from 'react';
import Image from 'next/image';
import styles from './AudioPlayerPlayControl.module.scss';

const AudioPlayerPlayControl = ({ isPlaying, start, stop }) => {
  return (
    <>
      {isPlaying ? (
        <Image
          className={styles.icon}
          onClick={stop}
          src="/assets/icons/icon-pause.svg"
          alt="pause"
          width={30}
          height={30}
        />
      ) : (
        <Image
          className={styles.icon}
          onClick={start}
          src="/assets/icons/icon-play.svg"
          alt="play"
          width={30}
          height={30}
        />
      )}
    </>
  );
};

export default memo(AudioPlayerPlayControl);
