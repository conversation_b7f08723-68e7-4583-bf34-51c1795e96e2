/* stylelint-disable */
@import '../../../styles/variables';

.volume-slider-container {
  border-radius: 10px;
  display: flex;
  align-items: center;
  width: 100%;

  input[type='range'],
  input[type='range']::-webkit-slider-runnable-track,
  input[type='range']::-webkit-slider-thumb {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    font: inherit;
    cursor: pointer;
    margin: 0;
  }
}

.volume-slider {
  width: 100%;
  height: 8px;
  background-color: $grey-40;
  border-radius: 4px;
  overflow: hidden;
}

.volume-slider::-webkit-slider-thumb {
  width: 0;
  background-color: transparent;
  border-radius: 0;
  position: relative;
  box-shadow: $shadow-audio-player-progress;
}

.volume-slider::-moz-range-thumb {
  width: 0;
  background-color: transparent;
  border-radius: 0;
  box-shadow: $shadow-audio-player-progress;
  border: none;
}
