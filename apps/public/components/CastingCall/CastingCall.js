import styles from './CastingCall.module.scss';
import React, { memo, useEffect, useState } from 'react';
import Image from 'next/image';
import cn from 'classnames';
import { useViewport } from '../../utils/useViewport';
import { isInViewPort } from '../../utils/isInViewPort';
import dayjs from 'dayjs';
import Link from 'next/link';
import { CategoryList, PaymentLabel } from '../index';
import { VIEW_MODE } from '../../constants/castingCalls';
import { CASTING_CALL_LIST_ITEM } from '../../constants/dataCy';

const CastingCall = ({
  mainCategory,
  additionalCategories = [],
  id,
  title,
  description,
  location,
  rolesCount = 0,
  expires = 'soon',
  isOnline = false,
  isEasyToApply = false,
  hot = false,
  mode = VIEW_MODE.List,
  viewed = false,
  applied = false,
  type,
  paymentAmount,
  paymentPeriod,
  paymentCurrency,
}) => {
  const { width } = useViewport();
  const imageSlug = mainCategory.name.toLowerCase().replace(/[\W_]+/g, '');
  const image = `/assets/icons/categories/icon-${imageSlug}.svg`;

  const getCurrentView = () => {
    if (isInViewPort(width, 'tablet', 'min')) {
      return mode;
    } else {
      return VIEW_MODE.Grid;
    }
  };

  const [currentView, setCurrentView] = useState(getCurrentView());

  useEffect(() => {
    setCurrentView(getCurrentView());
  }, [width, mode]);

  return (
    <Link
      href={`/castingcall/${id}`}
      className={cn(
        styles.element,
        currentView === VIEW_MODE.Grid ? styles.grid : styles.list,
        {
          [styles.viewed]: viewed,
        },
      )}
      data-cy={CASTING_CALL_LIST_ITEM}
    >
      {currentView === VIEW_MODE.List && (
        <>
          <span className={cn(styles.icon, styles[`icon-${imageSlug}`])}>
            <Image src={image} width={20} height={20} alt={title} />
          </span>
          <div className={cn(styles.line, styles.first)}>
            <span className={styles.title}>
              {title}
              {process.env.showCastingCallType === 'true' && (
                <span> ({type})</span>
              )}
            </span>
            <div className={styles['badge-container']}>
              {isEasyToApply && (
                <span className={cn(styles.badge, styles['easy-apply-badge'])}>
                  easy apply
                </span>
              )}
              {isOnline && (
                <span className={cn(styles.badge, styles['online-badge'])}>
                  online audition
                </span>
              )}
            </div>
          </div>
          <span className={cn(styles.line, styles.second)}>
            <span className={styles.info}>
              {(paymentAmount || paymentPeriod === 'TFP') && (
                <PaymentLabel
                  paymentAmount={paymentAmount}
                  paymentPeriod={paymentPeriod}
                  paymentCurrency={paymentCurrency}
                  inline
                />
              )}
              <span>
                {hot && (
                  <>
                    <span className={styles.type}>HOT</span>
                    <span className={styles['dot-divider']}></span>
                  </>
                )}
              </span>
              <span>
                <span className={styles.location}>{location}</span>
                <span className={cn(styles.expires, styles['dot-divider'])}>
                  Expires on {dayjs(expires).format('M/D/YYYY')}
                </span>
              </span>
            </span>
          </span>
          <div className={cn(styles.line, styles.second)}>
            <p className={styles.description}>{description}</p>
          </div>
          <div
            className={cn(styles.line, styles.second, styles['category-list'])}
          >
            <CategoryList
              mainCategory={mainCategory}
              additionalCategories={additionalCategories}
            />
          </div>
          <span className={cn(styles.line, styles.second)}>
            <div className={styles.data}>
              <span className={styles.roles}>
                {rolesCount} role{rolesCount > 1 ? 's' : ''} available
              </span>
              {viewed && !applied && (
                <div className={cn(styles.badge, styles['viewed-badge'])}>
                  seen
                </div>
              )}

              {applied && (
                <div className={cn(styles.badge, styles['applied-badge'])}>
                  applied
                </div>
              )}
            </div>
          </span>
        </>
      )}
      {currentView === VIEW_MODE.Widget && (
        <>
          <span className={cn(styles.icon, styles[`icon-${imageSlug}`])}>
            <Image src={image} width={20} height={20} alt={title} />
          </span>
          <div className={cn(styles.line, styles.first)}>
            <span className={styles.title}>
              {title}
              {process.env.showCastingCallType === 'true' && (
                <span> ({type})</span>
              )}
            </span>
            <div className={styles['badge-container']}>
              {isEasyToApply && (
                <span className={cn(styles.badge, styles['easy-apply-badge'])}>
                  easy apply
                </span>
              )}
              {isOnline && (
                <span className={cn(styles.badge, styles['online-badge'])}>
                  online audition
                </span>
              )}
            </div>
          </div>
          <div
            className={cn(styles.line, styles.second, styles['widget-info'])}
          >
            <div className={styles.info}>
              {(paymentAmount || paymentPeriod === 'TFP') && (
                <PaymentLabel
                  paymentAmount={paymentAmount}
                  paymentPeriod={paymentPeriod}
                  paymentCurrency={paymentCurrency}
                  inline
                />
              )}
              <span className={styles.category}>{mainCategory.name}</span>
              <span className={styles.location}>{location}</span>
              <span className={styles.expires}>
                Expires on {dayjs(expires).format('M/D/YYYY')}
              </span>
            </div>
            <div className={styles.roles}>
              {rolesCount} role{rolesCount > 1 ? 's' : ''} available
            </div>
          </div>
        </>
      )}
      {currentView === VIEW_MODE.Grid && (
        <>
          <span className={cn(styles.line, styles.header)}>
            <span className={cn(styles.icon, styles[`icon-${imageSlug}`])}>
              <Image src={image} width={20} height={20} alt={title} />
            </span>
            <section className={styles['header-right']}>
              <section>
                {hot && (
                  <>
                    <span className={styles.type}>HOT</span>
                    <span className={styles['dot-divider']}></span>
                  </>
                )}
                <span className={styles.expires}>
                  Expires on {dayjs(expires).format('M/D/YYYY')}
                </span>
              </section>
              <section>
                <span className={styles.location}>
                  {location
                    .replace('United States', 'US')
                    .replace('Canada', 'CA')}
                </span>
                {(paymentAmount || paymentPeriod === 'TFP') && (
                  <span className={styles['grid-payment-badge']}>
                    <PaymentLabel
                      paymentAmount={paymentAmount}
                      paymentPeriod={paymentPeriod}
                      paymentCurrency={paymentCurrency}
                      inline
                    />
                  </span>
                )}
              </section>
            </section>
            {viewed && !applied && (
              <div className={cn(styles.badge, styles['viewed-badge'])}>
                seen
              </div>
            )}
            {applied && (
              <div className={cn(styles.badge, styles['applied-badge'])}>
                applied
              </div>
            )}
          </span>
          <span className={cn(styles.line, styles.grow)}>
            <span className={styles.title}>{title}</span>
          </span>
          <div className={styles.line}>
            <div>
              <p className={styles.description}>{description}</p>
            </div>
          </div>
          <div className={cn(styles.line, styles['category-list'])}>
            <CategoryList
              mainCategory={mainCategory}
              additionalCategories={additionalCategories}
            />
          </div>
          <span className={cn(styles.line, styles.badges)}>
            <span className={styles.roles}>
              {rolesCount} role{rolesCount > 1 ? 's' : ''} available
            </span>
            {isEasyToApply && (
              <span className={cn(styles.badge, styles['easy-apply-badge'])}>
                easy apply
              </span>
            )}
            {isOnline && (
              <span className={cn(styles.badge, styles['online-badge'])}>
                online audition
              </span>
            )}
          </span>
        </>
      )}
    </Link>
  );
};

export default memo(CastingCall);
