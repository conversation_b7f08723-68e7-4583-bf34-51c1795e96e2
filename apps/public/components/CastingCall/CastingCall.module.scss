@import '../../styles/mixins';
@import '../../styles/variables';

.type {
  color: $red-60;
  font-weight: 700;
  text-transform: uppercase;
  white-space: nowrap;
}

.category {
  color: $grey-100;
  font-weight: 300;
  font-size: 12px;
  white-space: nowrap;

  &::after {
    content: '';
    padding-left: 0.25em;
    padding-right: 0.25em;
    color: $grey-60;
    font-weight: 400;
    margin-right: 3px;
    margin-left: 3px;

    @include tablet {
      content: '\2022';
    }
  }
}

.location {
  white-space: nowrap;
  color: $grey-100;
  font-weight: 300;

  &::before {
    display: inline-block;
    background: url('#{$assetUrl}/assets/icons/icon-pin-2.svg') center center
      no-repeat;
    background-size: cover;
    content: '';
    width: 8px;
    height: 11px;
    line-height: 0;
    margin-right: $space-5;
  }
}

.dot-divider {
  &::before {
    content: '\2022';
    padding-left: 8px;
    padding-right: 8px;
    color: $grey-60;
    font-weight: 400;
  }
}

.grid-payment-badge {
  display: inline-block;
  margin-left: 8px;
  margin-top: 3px;
}

.roles {
  color: $blue-100;
  font-size: 14px;
  cursor: pointer;
  font-weight: 400;
  white-space: nowrap;
  display: none;

  @include tablet {
    display: inline;
  }
}

.expires {
  color: $grey-100;
  font-weight: 300;
}

.badge-container {
  margin-bottom: $space-10;
  display: flex;
  align-items: center;
  gap: $space-5;
}

.badge {
  border-radius: 3em;
  background-color: transparent;
  font-size: 12px;
  text-transform: lowercase;
  font-weight: 400;
  line-height: 17px;
  padding: 0 $space-10;
  border: 1px solid currentcolor;
  height: 20px;
  white-space: nowrap;
  margin-left: auto;
  margin-top: $space-5;
  float: left;
  display: flex;

  @include tablet {
    margin-top: 0;
  }

  &.easy-apply-badge {
    border-color: $green-80;
    color: $green-80;
  }

  &.online-badge {
    border-color: $violet-60;
    color: $violet-60;
  }

  &.viewed-badge {
    background-color: $grey-60;
    color: $white;
  }

  &.applied-badge {
    background-color: $green-80;
    color: $white;
  }
}

.list .title {
  @include tablet {
    font-size: 18px;
    margin-left: $space-50;
    margin-top: $space-5;
    margin-bottom: $space-10;
  }

  @include desktop {
    margin-left: 0;
    margin-top: 0;
    margin-bottom: 0;
  }
}

.element .title {
  display: block;
  font-size: 18px;
  font-weight: 700;
  transition: all 0.1s ease-in-out;
  flex-grow: 1;
  margin-bottom: $space-10;
  word-break: break-word;
  color: $black;

  &:hover {
    color: $blue-100;
  }
}

.element {
  position: relative;
  text-decoration: none;
  background: $white;
  box-shadow: $shadow-casting-call;
  border-radius: 3px;
  padding: $space-10 $space-20 $space-20;
  color: $black;
  cursor: pointer;
  transition: all 0.1s ease-in-out;
  display: flex;
  flex-flow: column nowrap;
  justify-content: space-between;
  margin-bottom: $space-20;
  min-width: 0;

  &:hover {
    box-shadow: $shadow-casting-call-hover;
    text-decoration: none;

    .title {
      color: $blue-100;
    }
  }

  .description {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    margin: 0 0 $space-20;
    flex-grow: 1;
    /* stylelint-disable-next-line value-no-vendor-prefix */
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;

    @include tablet {
      margin: $space-20 0;
    }

    &:empty {
      display: none;
    }
  }

  &.grid {
    text-decoration: none;

    .description {
      margin-top: 0;
      -webkit-line-clamp: 4;
    }
  }
}

.element.list {
  text-decoration: none;

  @include desktop {
    padding-left: $space-35;
  }
}

.element .icon {
  background-image: $gradient-icon-orange;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  min-width: 40px;
  align-items: center;
  justify-content: center;
  position: absolute;
  display: none;

  @include desktop {
    left: -20px;
    top: 0;
  }

  &.icon-acting {
    background-image: $gradient-icon-acting;
    display: flex;
  }

  &.icon-musicvideos {
    background-image: $gradient-icon-music-videos;
    display: flex;
  }

  &.icon-dancing {
    background-image: $gradient-icon-dancing;
    display: flex;
  }

  &.icon-modeling {
    background-image: $gradient-icon-modeling;
    display: flex;
  }

  &.icon-promotionalmodels {
    background-image: $gradient-icon-promotional-models;
    display: flex;
  }

  &.icon-realitytv {
    background-image: $gradient-icon-reality-tv;
    display: flex;
  }

  &.icon-singing {
    background-image: $gradient-icon-singing;
    display: flex;
  }

  &.icon-theater {
    background-image: $gradient-icon-theater;
    display: flex;
  }

  &.icon-voiceover {
    background-image: $gradient-icon-voiceover;
    display: flex;
  }

  &.icon-pageant {
    background-image: $gradient-icon-pageant;
    display: flex;
  }

  &.icon-other {
    background-image: $gradient-icon-other;
    display: flex;
  }

  &.icon-extras {
    background-image: $gradient-icon-extras;
    display: flex;
  }

  &.icon-commercials {
    background-image: $gradient-icon-commercials;
    display: flex;
  }

  &.icon-contentcreators {
    background-image: $gradient-icon-content-creators;
    display: flex;
  }

  &.icon-influencers {
    background-image: $gradient-icon-influencers;
    display: flex;
  }
}

.line {
  display: flex;

  @include tablet {
    margin-bottom: $space-10;
  }
}

.info {
  font-size: 12px;
  display: flex;
  margin-right: $space-20;
  flex-flow: column nowrap;
  align-items: center;

  @include tablet {
    flex-flow: row nowrap;
  }
}

.data {
  margin-left: auto;
  display: none;
  flex-wrap: nowrap;
  align-items: center;
  width: 100%;

  @include tablet {
    display: flex;
  }
}

.first {
  order: 2;
  flex-direction: column;

  @include tablet {
    order: 1;
    flex-direction: row;
  }
}

.second {
  order: 1;
  margin-left: $space-50;
  margin-top: 2px;
  margin-bottom: $space-10;
  height: 30px;

  @include tablet {
    order: 2;
    margin-left: 0;
    margin-top: 0;
    margin-bottom: 0;
    height: auto;
  }
}

.widget-info {
  flex-direction: row;
  justify-content: space-between;
  width: 100%;

  .category {
    margin-right: 2px;
  }
}

.category-list {
  margin-bottom: $space-15;
}

.grid.element {
  flex-basis: 49%;
  margin: 0 0 $space-20;

  @include xlarge {
    width: 100%;
    margin-right: $space-20;
  }
}

.grid .icon {
  left: 0;
  margin-right: $space-10;
  position: relative;
}

.grid .line {
  @include desktop {
    margin-bottom: 0;
  }

  &.grow {
    flex-grow: 1;
  }
}

.grid .header {
  margin-bottom: 8px;

  .header-right {
    display: flex;
    flex-direction: column;
    line-height: 1;
    justify-content: space-around;
    font-size: 12px;
  }
}

.grid .category-list {
  margin-bottom: $space-20;
}

.grid .line .title {
  font-size: 16px;
  margin-left: 0;
}

.grid .line .left {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.grid .line .right {
  display: flex;
  flex-direction: column;
  margin-left: auto;
  justify-content: flex-end;
}

.grid .line .roles {
  margin-left: 0;
  margin-right: auto;
  font-size: 12px;
  display: inline;
}

.grid .badges {
  align-items: center;
}

.grid .line .badge {
  margin: 0 0 0 $space-10;
}

.element.viewed {
  opacity: 0.5;

  .icon {
    filter: grayscale(100%);
  }

  .badge {
    opacity: 1;
  }
}
