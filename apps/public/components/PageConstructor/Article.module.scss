@import '../../styles/mixins';
@import '../../styles/variables';

.container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin: auto;
  background: $white;
  box-shadow: $shadow-article-container;
  color: initial;
  font-weight: initial;
  max-width: 400px;
  text-decoration: none !important; /* stylelint-disable-line declaration-no-important */

  @include tablet {
    gap: 32px;
    flex-direction: row;
    max-width: 1240px;
  }
}

.image {
  position: relative;
  background:
    url('#{$assetUrl}/assets/footer/triangles.svg') no-repeat right bottom,
    $gradient-port-gore $violet-80;
  background-size: auto 100%;
  display: flex;
  align-items: center;
  line-height: 0;
  flex-shrink: 0;
  min-height: 40px;

  @include tablet {
    max-width: 50%;
    min-width: 250px;
  }
}

.container .image img {
  width: 100%;
  height: auto;
  margin: 0;
}

.top {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 2;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  line-height: 1;
  font-size: 14px;
  background: rgba(0, 0, 0, 70%);
  padding: $space-10 $space-10 $space-10 $space-20;
  color: $grey-40;
}

.top .label {
  display: inline-block;
  border-radius: 9px;
  padding: 0 12px;
  line-height: 15px;
  height: 17px;
  color: $black;
  margin-left: auto;
  background-image: $gradient-btn-orange;
}

.top .lessons {
  background-image: $gradient-btn-orange;
}

.top .news {
  background-image: $gradient-green-blue;
}

.description {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin: 0 $space-30 24px;

  @include tablet {
    margin: $space-30 16px $space-30 0;
  }
}

.title {
  color: $black;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: 150%;
}

.summary {
  color: $grey-100;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%;
}
