import React from 'react';
import { Carousel, ReviewCard } from '../';
import styles from './Reviews.module.scss';

const PageConstructorReviews = ({ data, type = 'info' }) => {
  if (data?.items === undefined) return;

  return (
    <div className={styles.container}>
      <Carousel
        className={`carousel-reviews-${type}`}
        enableArrowNavigation
        arrowType="round"
        enablePagination
      >
        {data?.items?.map((item, i) => (
          <ReviewCard
            key={i}
            className={styles.box}
            title={`${item.author.firstname} ${item.author.lastname}`}
            subTitle={item.author.type}
            date={item.published_at}
            description={item.content}
            imageSrc={item.author.title_photo_url}
            rating={item.rating}
            textLimit={120}
            fullHeight
          />
        ))}
      </Carousel>
    </div>
  );
};

export default PageConstructorReviews;
