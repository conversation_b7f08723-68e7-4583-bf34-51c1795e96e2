import React from 'react';
import styles from './Cta.module.scss';
import ClapperboardIcon from '../../public/assets/icons/icon-clapperboard.svg';

export const PageConstructorImageLink = ({ data }) => {
  return (
    <div className={styles.container}>
      <div className={styles['interactive-container']}>
        <div>
          <div className={styles.header}>{data.title}</div>
          <div className={styles.description}>
            <ClapperboardIcon className={styles.icon} />
            <span>{data.description}</span>
          </div>
        </div>
        <a className={styles['cta-button']} href={data.link}>
          {data.button}
        </a>
      </div>
    </div>
  );
};

export default PageConstructorImageLink;
