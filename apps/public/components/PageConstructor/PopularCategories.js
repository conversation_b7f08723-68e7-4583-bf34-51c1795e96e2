import React from 'react';
import { SeoLinking } from '../';
import styles from './PopularCategories.module.scss';
import cn from 'classnames';

export const PageConstructorPopular = ({ title, data, highlight }) => {
  return (
    <div
      className={cn(styles.container, {
        [styles.highlight]: highlight,
      })}
    >
      {title && <h3 className={styles.title}>{title}</h3>}
      <SeoLinking seoCrossLinking={data} title={false} />
    </div>
  );
};

export default PageConstructorPopular;
