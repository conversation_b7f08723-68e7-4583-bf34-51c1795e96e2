@import '../../styles/mixins';
@import '../../styles/variables';

.interactive-container {
  padding: $space-30;
  background-image: $gradient-cta-container;
  background-color: $violet-10;
  border-radius: 5px;
  color: $violet-100;

  .header {
    text-align: center;
    font-size: 16px;
    font-weight: 700;
    line-height: 130%;
    text-transform: capitalize;

    @include tablet {
      text-align: left;
      font-size: 22px;
      padding-left: 0;
    }
  }

  .cta-button {
    cursor: pointer;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: $space-10 32px;
    background:
      0 0 $gradient-btn-purple,
      $gradient-btn-cta-overlay;
    box-shadow: $shadow-btn-cta;
    background-size: 200% auto;
    border-radius: 156px;
    margin: auto;
    border: none;
    transition: all 0.3s ease-in-out;
    color: $white;
    text-align: center;
    text-decoration: none;
    font-size: 14px;
    font-weight: 700;
    line-height: 30px;
    height: 40px;
    width: 100%;

    &:hover {
      background-position: 100% 50%;
      box-shadow: $shadow-btn-cta-hover;
    }

    @include tablet {
      margin: auto 0 auto auto;
      flex-shrink: 0;
      max-width: 180px;
    }
  }

  @include tablet {
    padding: $space-30 $space-55 $space-30 $space-60;
    display: flex;
    flex-direction: row;
    gap: 10px;
  }
}

.icon {
  width: 30px;
  height: 30px;
  flex-shrink: 0;

  @include tablet {
    width: 25px;
    height: 25px;
  }
}

.description {
  display: flex;
  margin: $space-20 0 $space-25;
  align-items: center;
  column-gap: $space-20;
  font-size: 14px;
  justify-content: center;

  .important-text {
    font-weight: 900;
    display: inline;

    @include tablet {
      display: inline;
    }
  }

  @include tablet {
    margin: $space-15 0 0;
    column-gap: $space-10;
    font-size: 16px;
    font-weight: 600;
    line-height: 140%;
  }
}

.container {
  border-radius: 3px;
  margin-bottom: $space-20;

  .interactive-container {
    @include tablet {
      padding-left: $space-40;
      padding-right: $space-20;
    }
  }

  .header {
    text-align: left;

    @include tablet {
      font-size: 18px;
    }
  }

  .description {
    font-size: 14px;
    justify-content: left;
  }
}
