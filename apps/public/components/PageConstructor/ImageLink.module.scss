@import '../../styles/mixins';
@import '../../styles/variables';

.container {
  width: 100%;
}

.item-content {
  border-radius: 5px 5px 0 0;
  background-color: $white;
  display: flex;
  flex-direction: column;
  box-shadow: $shadow-form-control;
  transition: all 0.2s ease-in 0s;
  width: 100%;

  .item-image {
    margin: 0;
    aspect-ratio: 2/1;
    background-size: cover;
    width: 100%;

    @include tablet {
      aspect-ratio: 1/1;
    }
  }

  &:hover {
    box-shadow: $shadow-image-link-hover;
    transition: all 0.2s ease-in 0s;
  }
}

.description {
  padding: $space-15;
  text-decoration: none;
  font-size: 14px;
  font-weight: 600;
  color: $black;
}
