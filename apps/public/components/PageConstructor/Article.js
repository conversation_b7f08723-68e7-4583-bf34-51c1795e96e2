import React from 'react';
import styles from './Article.module.scss';
import Link from 'next/link';
import classnames from 'classnames';
import Image from 'next/image';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { ARTICLE_STATUS } from '../../constants/articles';

/**
 * @param {Object} props
 * @param {import('../../services/endpoints/articles').FetchArticleResponse} props.data
 */
const PageConstructorArticle = ({ data }) => {
  if (
    !data ||
    data.status !== 'ok' ||
    data.articleStatus === ARTICLE_STATUS.Draft
  )
    return <></>;

  dayjs.extend(relativeTime);
  const relativeDate = dayjs().to(dayjs(data.time));
  const category = data.categories[0];
  const link = `/blog/${category.slug}/${data.slug.toLowerCase()}`;
  const label = category.title;
  const image = data.thumbnailImage?.size5?.href;
  const title = data.title;
  const summary = data.summary;

  return (
    <Link href={link} className={styles.container}>
      <div className={styles.image}>
        <span className={styles.top}>
          <span>
            {relativeDate} {}
          </span>
          <span
            className={classnames(styles.label, styles[label.toLowerCase()])}
          >
            {label}
          </span>
        </span>
        {image && (
          <Image alt={title} unoptimized src={image} width={450} height={250} />
        )}
      </div>
      <div className={styles.description}>
        <div className={styles.title}>{title}</div>
        <div
          className={styles.summary}
          dangerouslySetInnerHTML={{ __html: summary || '' }}
        />
      </div>
    </Link>
  );
};

export default PageConstructorArticle;
