import React from 'react';
import { Carousel } from '../';
import styles from './ImageLink.module.scss';

export const PageConstructorImageLink = ({ data }) => {
  return (
    <div className={styles.container}>
      <Carousel
        className="carousel-imagelinks"
        enablePagination
        enableArrowNavigation
        arrowType="round"
      >
        {data.map((item, i) => (
          <a key={i} href={item.linking}>
            <div className={styles['item-content']}>
              <div
                className={styles['item-image']}
                style={{ backgroundImage: `url(${item.image})` }}
              />
              <div className={styles.description}>{item.anchor}</div>
            </div>
          </a>
        ))}
      </Carousel>
    </div>
  );
};

export default PageConstructorImageLink;
