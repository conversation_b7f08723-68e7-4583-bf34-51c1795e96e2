import Head from 'next/head';
import { useRouter } from 'next/router';
import { formatArticleSchema } from '../../utils/seoSchema/article';
import { formatFaqSchema } from '../../utils/seoSchema/faq';
import { formatHowToSchema } from '../../utils/seoSchema/howTo';

function Seo({
  overrideTitle,
  overrideDescription,
  overrideKeywords,
  overrideH1,
  defaultTitle,
  defaultDescription,
  defaultKeywords,
  defaultH1,
  suffix,
  seoPage,
  jobPostData,
  article,
  faq,
  howTo,
  allowIndexing = true,
}) {
  const router = useRouter();

  let path = router.asPath;

  if (router.asPath.includes('?')) {
    path = router.asPath.split('?')[0];
  }

  const paginationExceptions = ['realitytv', 'voiceover'];
  const paginationPath = paginationExceptions.some((value) =>
    path.includes(value),
  )
    ? path.split('-page')[0]
    : path;

  const seoValues = seoPage || {};

  let {
    title,
    description,
    keywords,
    robots,
    viewport,
    microdata,
    h1,
    text,
    ogImageUrl,
    ogImageWidth,
    ogImageHeight,
  } = seoValues;

  if (!title) title = defaultTitle;
  if (!description) description = defaultDescription;
  if (!keywords) keywords = defaultKeywords;
  if (!h1) h1 = defaultH1;
  if (overrideTitle) title = overrideTitle;
  if (overrideDescription) description = overrideDescription;
  if (overrideKeywords) keywords = overrideKeywords;
  if (overrideH1) h1 = overrideH1;
  if (!title)
    title = 'allcasting ● Casting Website - Casting Calls for Everyone';
  if (!description)
    description = 'allcasting ● Casting Website - Casting Calls for Everyone';

  if (suffix) {
    title ? (title += suffix) : '';
    h1 ? (h1 += suffix) : '';
    description ? (description += suffix) : '';
  }

  if (allowIndexing === false) {
    if (typeof robots === 'string') {
      let robotsArray = robots.split(',').map((str) => str.trim());

      // Remove conflicting directives if they exist
      robotsArray = robotsArray.filter(
        (directive) => !['index', 'follow'].includes(directive),
      );

      if (!robotsArray.includes('noindex')) {
        robotsArray.push('noindex');
      }
      if (!robotsArray.includes('nofollow')) {
        robotsArray.push('nofollow');
      }

      robots = robotsArray.join(', ');
    } else {
      robots = 'noindex, nofollow';
    }
  }

  return (
    <>
      <Head>
        {title && <title>{title}</title>}
        <meta charSet={'UTF-8'} />
        <meta
          name={'viewport'}
          content={'width=device-width, initial-scale=1.0'}
        />

        <link rel="alternate" hrefLang="en" href={process.env.baseUrl + path} />

        {description && <meta name={'description'} content={description} />}

        <meta property="og:type" content="website" />
        {title && <meta property="og:title" content={title} />}
        {description && (
          <meta property="og:description" content={description} />
        )}
        {ogImageUrl && (
          <>
            <meta property="og:image" content={ogImageUrl} />
            <meta
              property="og:image:width"
              content={(ogImageWidth || 1200).toString()}
            />
            <meta
              property="og:image:height"
              content={(ogImageHeight || 630).toString()}
            />
          </>
        )}

        <link
          rel="canonical"
          href={`${process.env.baseUrl}${paginationPath}`}
        />
        <meta property="og:url" content={`${process.env.baseUrl}${path}`} />

        {robots && <meta name={'robots'} content={robots} />}
        {viewport && <meta name={'viewport'} content={viewport} />}
        {keywords && <meta name={'keywords'} content={keywords} />}
        {microdata &&
          microdata.map((data, key) => (
            <script
              key={key}
              type="application/ld+json"
              dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
            />
          ))}
        {jobPostData && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(jobPostData) }}
          />
        )}
        {article && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(formatArticleSchema(article)),
            }}
          />
        )}
        {faq && faq.questionsAndAnswers.length > 0 && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(formatFaqSchema(faq)),
            }}
          />
        )}
        {!!howTo?.items && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(formatHowToSchema(howTo)),
            }}
          />
        )}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        {/* 32×32 */}
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        {/* 180×180 */}
        <link rel="manifest" href="/manifest.json" />
      </Head>
      {h1 && <h1 dangerouslySetInnerHTML={{ __html: h1 }}></h1>}
      {text && text}
    </>
  );
}

export default Seo;
