@import 'styles/variables';

.accordion-title {
  padding: 0 $space-20;
  background-color: $grey-20;
  font-size: 14px;
  line-height: 30px;
  color: $grey-100;
  text-transform: uppercase;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 300;
  cursor: pointer;

  &:hover {
    background: $grey-10;
  }
}

.accordion-content {
  max-height: 0;
  overflow: hidden;
  transition: all 1.5s cubic-bezier(0, 1, 0, 1);
  background-color: $white;
  padding-bottom: $space-10;
}

.accordion-content-open {
  max-height: 1000px;
  transition: max-height 1s ease-in;
}

.accordion-arrow-icon {
  filter: invert(65%) sepia(100%) saturate(3200%) hue-rotate(160deg)
    brightness(104%) contrast(99%);
  opacity: 0.5;
  transform: rotate(90deg);
}

.accordion-arrow-icon-up {
  transform: rotate(-90deg);
}
