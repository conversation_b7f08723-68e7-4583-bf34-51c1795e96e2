import { memo, useState } from 'react';
import styles from './AccordionItem.module.scss';
import Image from 'next/image';
import cn from 'classnames';

const AccordionItem = ({ title, children }) => {
  const [isActive, setIsActive] = useState(true);

  const toggleActive = () => setIsActive(!isActive);

  return (
    <div>
      <div className={styles['accordion-title']} onClick={toggleActive}>
        <div>{title}</div>
        <Image
          alt="icon"
          width={21}
          height={14}
          src="/assets/icons/icon-angle-2.svg"
          className={cn(styles['accordion-arrow-icon'], {
            [styles['accordion-arrow-icon-up']]: isActive,
          })}
        />
      </div>
      <div
        className={cn(styles['accordion-content'], {
          [styles['accordion-content-open']]: isActive,
        })}
      >
        {children}
      </div>
    </div>
  );
};

export default memo(AccordionItem);
