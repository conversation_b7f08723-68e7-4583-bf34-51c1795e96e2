@import '../../styles/variables';
@import '../../styles/mixins';

.main {
  --main-padding: 20px;

  min-height: 62vh;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 40px;
  padding: 0 var(--main-padding);
  width: 100%;
  max-width: $content-max-width;

  @include desktop {
    --main-padding: 60px;
  }
}

.main p,
.main li {
  margin-block: 1em;
}

.new-margins p {
  margin-block: var(--margin-p);
}
