@import '../../styles/mixins';
@import '../../styles/variables';

.profile-preview {
  display: flex;
  flex-direction: column;
  flex-grow: 1;

  @include desktop {
    position: relative;
    max-width: 826px;
    display: grid;
    grid-template-columns: minmax(0, 1fr) minmax(0, 1fr);
    grid-auto-flow: column;
  }
}

.profile-preview-no-images {
  display: unset;
}

.profile-gallery {
  display: initial;
  background-color: $grey-10;
  border-radius: 0;
  overflow: hidden;
  height: 100%;
  width: 100%;

  @include desktop {
    border-radius: 10px 0 0 10px;
  }
}

.profile-info {
  background-color: $white;
  width: 100vw;

  @include desktop {
    border-radius: 0 10px 10px 0;
    flex-flow: column nowrap;
    flex: 0 0 50%;
    padding: $space-40 0 16px;
    display: flex;
    width: 100%;
  }
}

.profile-info-header {
  display: none;

  @include desktop {
    display: initial;
    margin-bottom: $space-15;
    padding: 0 $space-20;
  }
}

.profile-info-details {
  display: flex;
  justify-content: space-between;
  padding: $space-15 $space-20 $space-20;
  background-color: $violet-80;

  @include desktop {
    padding: 0 $space-20 $space-15;
    background-color: $white;
  }
}

.slider-slide {
  width: 100%;
  height: 100%;
  display: block;
  overflow: hidden;
}

.slider-slide-item {
  position: relative;
  overflow: hidden;
  line-height: 0;

  img {
    width: 100%;
    height: auto;
    object-fit: contain;
    aspect-ratio: 600 / 800;
  }

  .placeholder {
    aspect-ratio: 600 / 800;
    background-repeat: no-repeat;
    background-position: bottom;
    background-size: 100% 90%;
    opacity: 0.6;

    &.male {
      background-image: url('#{$assetUrl}/assets/placeholders/male-height.svg');
    }

    &.female {
      background-image: url('#{$assetUrl}/assets/placeholders/female-height.svg');
    }
  }
}

.rating {
  display: flex;
  align-items: center;
  gap: 3px;
  color: $white;

  @include desktop {
    color: $black;
  }
}

.star {
  filter: invert(71%) sepia(97%) saturate(1417%) hue-rotate(359deg)
    brightness(101%) contrast(104%);
}

.count {
  display: inline-block;
  vertical-align: middle;
  font-weight: 300;
  font-size: 14px;
}

.name {
  display: inline-block;
  font-weight: 700;
  font-size: 30px;
  line-height: 1;
  margin: 0;
}

.id {
  font-size: 14px;
  font-weight: 300;
  text-transform: uppercase;
  opacity: 0.7;
  color: $white;

  @include desktop {
    color: $grey-100;
  }
}

.profile-overview {
  padding: $space-10 $space-20 0;
}

.profile-actions {
  display: flex;
  align-items: center;
  padding: $space-20;
  border-top: 1px solid $grey-40;
  justify-content: center;

  &.center-content {
    justify-content: center;
  }

  @include desktop {
    padding: $space-20 $space-20 0;
    justify-content: space-between;
  }
}

.profile-link {
  color: $blue-100;
  font-size: 16px;
  font-weight: 400;
  line-height: 1;
}

.close {
  background-color: transparent;
  border-style: none;
  cursor: pointer;
  outline: none;
  position: absolute;
  right: 14px;
  top: 14px;
  height: 28px;
  opacity: 0.5;
  transition: opacity 0.2s ease-in-out;
  width: 28px;
  z-index: 10;

  &:hover {
    opacity: 1;
    transition: opacity 0.2s ease-in-out;
  }

  &::before {
    background-color: $black;
    transform: rotate(45deg);
    content: '';
    height: 1px;
    left: 50%;
    margin-left: -$space-10;
    position: absolute;
    top: 50%;
    width: 20px;
  }

  &::after {
    background-color: $black;
    transform: rotate(-45deg);
    content: '';
    height: 1px;
    left: 50%;
    margin-left: -$space-10;
    position: absolute;
    top: 50%;
    width: 20px;
  }
}

.profile-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.profile-button {
  display: none;

  @include desktop {
    display: initial;
  }
}

.profile-button-mobile {
  color: $blue-100;
  font-size: 16px;
  font-weight: 400;
  line-height: 1;
  text-align: right;

  @include desktop {
    display: none;
  }
}

.contact-button {
  display: none;

  @include desktop {
    display: initial;
  }
}

.contact-btn-container-mobile {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 $space-20 $space-20;
  flex-direction: column;
  background-color: $violet-80;

  @include desktop {
    display: none;
  }
}

.separator {
  width: 100%;
  border-top: 1px solid $white;
  padding: 0 $space-20 $space-20;
}
