import { memo, useEffect, useState } from 'react';
import { useProfileContext } from '../../contexts/ProfileContext';
import { useAuth } from '../../contexts/AuthContext';
import { useRouter } from 'next/router';
import cn from 'classnames';
import styles from './ProfilePreview.module.scss';
import { AccordionItem, Button, HeaderMobile, ProfileOverview } from '../index';
import Image from 'next/image';
import Link from 'next/link';
import Carousel from '../Carousel/Carousel';
import { isImageDeclined, sortProfilePhotos } from '../../utils/imageHelpers';

const ProfilePreview = ({ onContactTalent = () => {}, onClose = () => {} }) => {
  const [filteredImages, setFilteredImages] = useState([]);
  const { id, name, images, rating, username, isAgent, gender } =
    useProfileContext();
  const { userType, profileId } = useAuth();
  const router = useRouter();

  useEffect(() => {
    let sortedImages = [...images];

    if (sortedImages?.length) {
      sortedImages = sortProfilePhotos(
        sortedImages.filter((image) => {
          return image !== undefined && !isImageDeclined(image);
        }),
      );
    }
    setFilteredImages(sortedImages);
  }, [images]);

  const viewFullProfile = async () => {
    if (id === profileId && userType === 'talent') {
      window.location.href = `${process.env.redirectTalentUrl}/profile/${
        username || id
      }/info`;
    } else {
      await router.push(`/profile/${username || id}`.toLowerCase());
    }
  };

  return (
    <div
      className={cn(
        styles['profile-preview'],
        !filteredImages.length ? styles['profile-preview-no-images'] : '',
      )}
    >
      <HeaderMobile
        showLink
        href={`/profile/${username || id}`}
        onClose={onClose}
        label="Profile"
      >
        {name}
      </HeaderMobile>
      {filteredImages.length > 0 && (
        <div className={styles['profile-gallery']}>
          <Carousel
            className="carousel-profile-preview"
            slidesToScroll={1}
            enableArrowNavigation
            draggable
          >
            {filteredImages.map((img, index) => (
              <div key={index} className={styles['slider-slide']}>
                <div className={styles['slider-slide-item']}>
                  {img.proxy_url ? (
                    <Image
                      key={index}
                      src={img.proxy_url}
                      alt="image"
                      priority={!index}
                      unoptimized
                      width={600}
                      height={800}
                    />
                  ) : (
                    <div
                      key={index}
                      className={cn(
                        styles.placeholder,
                        styles[gender || 'male'],
                      )}
                    />
                  )}
                </div>
              </div>
            ))}
          </Carousel>
        </div>
      )}
      <div className={styles['profile-info']}>
        <div className={styles['profile-info-header']}>
          <span className={styles.name}>{name}</span>
        </div>
        <div className={styles['profile-info-details']}>
          {!isAgent && (
            <div className={styles.rating}>
              <Image
                className={styles['star']}
                src={'/assets/icons/icon-star-2.svg'}
                width={17}
                height={17}
                alt="star icon"
              />
              <span className={styles.count}>{rating}</span>
            </div>
          )}
          <div className={styles.id}>
            <span>Profile ID: </span>
            <span>{id}</span>
          </div>
        </div>
        {!isAgent && userType !== 'talent' && (
          <div className={styles['contact-btn-container-mobile']}>
            <div className={styles['separator']}></div>
            <Button
              href="/"
              onClick={onContactTalent}
              label="CONTACT TALENT"
              kind="secondary"
              color="blue"
              minWidth={'220px'}
            />
          </div>
        )}
        <div className={styles['profile-content']}>
          <AccordionItem title={'OVERVIEW'}>
            <div className={styles['profile-overview']}>
              <ProfileOverview />
            </div>
          </AccordionItem>
          {isAgent ? (
            <div
              className={cn(
                styles['center-content'],
                styles['profile-actions'],
              )}
            >
              <Button
                type="link"
                href={`/director/${username || id}`}
                label="View full profile"
                kind="secondary"
                minWidth={'220px'}
              />
            </div>
          ) : (
            <div className={styles['profile-actions']}>
              {!isAgent && userType !== 'talent' ? (
                <>
                  <Button
                    label="Contact talent"
                    kind="primary"
                    onClick={onContactTalent}
                    minWidth={'220px'}
                    className={styles['contact-button']}
                  />
                  <Link
                    href={`/profile/${username || id}`.toLowerCase()}
                    className={styles['profile-link']}
                  >
                    View full profile
                  </Link>
                </>
              ) : (
                <>
                  <Button
                    className={styles['profile-button']}
                    label="View full profile"
                    kind="secondary"
                    onClick={viewFullProfile}
                    minWidth={'100%'}
                  />
                  <Link
                    href={`/profile/${username || id}`.toLowerCase()}
                    onClick={onClose}
                    className={styles['profile-button-mobile']}
                  >
                    View full profile
                  </Link>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default memo(ProfilePreview);
