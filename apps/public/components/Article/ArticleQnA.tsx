import React from 'react';
import cn from 'classnames';
import styles from './ArticleQnA.module.scss';
import CallToActionBlock from '../CallToActionBlock/CallToActionBlock';
import type { Article } from '../../services/endpoints/articles';

type ArticleQnAProps = Pick<Article, 'questionsAndAnswers'> & {
  className?: string;
};

const ArticleQnA = ({
  className = '',
  questionsAndAnswers,
}: ArticleQnAProps) => {
  return (
    <div className={cn(styles.container, className)}>
      <h2 className={styles.heading}>Q&A</h2>
      {questionsAndAnswers.map((qa) => (
        <details key={qa.id} className={styles.accordion}>
          <summary className={styles.summary}>{qa.question}</summary>
          <div className={styles.content}>{qa.answer}</div>
        </details>
      ))}
      <CallToActionBlock className={styles.cta} list />
    </div>
  );
};

export default ArticleQnA;
