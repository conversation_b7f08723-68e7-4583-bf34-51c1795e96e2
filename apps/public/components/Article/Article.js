import styles from './Article.module.scss';
import React, { memo } from 'react';
import Image from 'next/image';
import { Breadcrumbs, Seo } from '../index';
import dayjs from 'dayjs';
import Categories from './Categories';
import ArticleQnA from './ArticleQnA';
import HowTo from './HowTo';
import { ARTICLE_STATUS } from '../../constants/articles';
import Head from 'next/head';

/**
 * @typedef {import('../../services/endpoints/articles').Article} Article
 */

/**
 * @typedef {Object} ArticleProps
 * @property {JSX.Element} content
 * @property {Object} meta
 * @property {string} meta.title
 * @property {string} meta.time
 * @property {string} meta.author
 * @property {Article['meta']} meta.meta
 * @property {Object} [seoPage]
 * @property {boolean} [showCreationInfo=true]
 * @property {boolean} [showHeadingImage=false]
 * @property {Array<{ href: string, text: string }>} [crumbs=[]]
 * @property {Article} article
 */

/**
 * @param {ArticleProps} props
 */
function Article({
  content,
  meta,
  seoPage,
  showCreationInfo = true,
  showHeadingImage = false,
  crumbs = [],
  article,
}) {
  const date = dayjs(meta.time).format('MMM D, YYYY');
  const image = article.headlineImage?.reference;
  const showImage = showHeadingImage && !!image?.href;
  const isDraft = article.articleStatus === ARTICLE_STATUS.Draft;

  return (
    <div className={styles.container}>
      <div>
        <Breadcrumbs crumbs={[...crumbs, { text: meta.title }]} />
        {showCreationInfo && (
          <div className={styles.meta}>
            <span className={styles.date}>{date}</span>
            <Categories
              className={styles.categories}
              categories={article.categories}
            />
          </div>
        )}
        {isDraft && (
          // Should also prevent any test pages like /info/test_page_constructor from indexing
          <Head>
            <meta name={'robots'} content="noindex, nofollow" />
          </Head>
        )}
        {seoPage && !isDraft && (
          <Seo
            seoPage={seoPage}
            defaultTitle={`${meta.title} • Blog • allcasting`}
            defaultDescription={
              meta.meta?.description ||
              article.summary ||
              `《 allcasting 》 ${meta.title} ✪  Latest blog about casting, movies, TV shows, modeling and acting.`
            }
            defaultKeywords={meta.meta?.keywords}
            article={{
              headline: meta.title,
              description: meta.meta?.description,
              author: article.author,
              datePublished: meta.time,
              image: article.headlineImage?.reference.href,
            }}
            faq={{ questionsAndAnswers: article.questionsAndAnswers }}
            howTo={article.howTo}
          />
        )}
        {!seoPage?.h1 && <h1>{meta.title}</h1>}
      </div>
      <div className={styles.content}>
        {showImage && image.width && image.height && (
          <Image
            className={styles.image}
            src={image.href}
            alt="header image"
            width={image.width}
            height={image.height}
          />
        )}
        {article.tldrSummary && (
          <div className={styles.tldr}>
            <div
              className={styles['tldr-content']}
              dangerouslySetInnerHTML={{ __html: article.tldrSummary }}
            />
          </div>
        )}
        <div className="article-content">{content}</div>
        {showCreationInfo && article.author && (
          <p className={styles.author}>
            <i>Posted by {article.author.name}</i>
          </p>
        )}
        {!!article.howTo?.items && <HowTo {...article.howTo} />}
        {article.questionsAndAnswers.length > 0 && (
          <ArticleQnA questionsAndAnswers={article.questionsAndAnswers} />
        )}
      </div>
    </div>
  );
}

export default memo(Article);
