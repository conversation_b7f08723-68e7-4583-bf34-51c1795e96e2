import React from 'react';
import cn from 'classnames';
import { ARTICLE_CATEGORIES_CHIP_LABELS } from '../../constants/articles';
import styles from './Categories.module.scss';
import type { ListArticle } from '../../services/endpoints/articles';

type Props = {
  categories: ListArticle['categories'];
  className?: string;
};

const Categories = ({ categories, className }: Props) => {
  return (
    <div className={cn(styles.categories, className)}>
      {categories.map((category) => (
        <span key={category.id} className={styles.chip}>
          {ARTICLE_CATEGORIES_CHIP_LABELS[category.slug] || category.title}
        </span>
      ))}
    </div>
  );
};

export default Categories;
