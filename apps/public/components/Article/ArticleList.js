import React from 'react';
import { ArticleBanner, Breadcrumbs, Pa<PERSON>ator, Promo, Seo } from '../index';
import { useRouter } from 'next/router';
import CallToActionBlock from '../CallToActionBlock/CallToActionBlock';
import { useAuth } from '../../contexts/AuthContext';
import { Amp } from '../../services/amp';
import ArticlePreview from '../Article/ArticlePreview';
import cn from 'classnames';
import ChipSelect from '../FormFields/ChipSelect/ChipSelect';
import {
  ARTICLE_CATEGORIES_LABELS,
  ARTICLE_FILTER_CATEGORIES,
  ARTICLE_PAGE_LIMIT,
} from '../../constants/articles';

import styles from './ArticleList.module.scss';

/**
 * @param {Object} props
 * @param {import('../../services/endpoints/articles').ArticleList} props.list
 * @param {import('../../services/endpoints/promotions').PromotionResource[]} props.promos
 * @param {string} [props.category = '']
 * @param {string|number} props.page
 * @param {Object} [props.seoPage]
 * @param {boolean} [props.isSaleHidden]
 * @param {boolean} [props.isUserGuest]
 * @param {boolean} [props.isAgent]
 *
 */
const ArticleList = ({
  list,
  promos,
  category = '',
  page,
  seoPage,
  isSaleHidden,
  isUserGuest,
  isAgent,
}) => {
  const router = useRouter();
  const { accountLevel } = useAuth();

  const handleCategoryChange = (op) => {
    router.push(`/blog/${op.value}`, undefined, { scroll: false });
  };

  const handleCategoryClick = (op) => {
    if (op.value === category) {
      router.push(`/blog`, undefined, { scroll: false });
    }
  };

  const title = ARTICLE_CATEGORIES_LABELS[category] || 'Allcasting Blog';
  const seo =
    Number(page) > 1
      ? {
          overrideTitle: `${title} ● allcasting - page ${page}`,
          overrideDescription: `${title} ● allcasting - page ${page}`,
          overrideH1: `${title} - page ${page}`,
        }
      : { overrideH1: title };

  const crumbs = [{ text: 'Allcasting Blog', href: '/blog' }];

  if (category) {
    crumbs.push({ text: title, href: `/blog/${category}` });
  }

  const filters = ARTICLE_FILTER_CATEGORIES.map((category) => ({
    title: ARTICLE_CATEGORIES_LABELS[category],
    value: category,
  }));

  return (
    <>
      <ArticleBanner />
      {/* ToDo: re-add isAgent when agent promotions are ready */}
      {isUserGuest && <Promo promos={promos} />}
      <Breadcrumbs crumbs={crumbs} />
      {isSaleHidden && !accountLevel?.isPaidOrDelayed && !isAgent && (
        <CallToActionBlock position={Amp.element.position.top} />
      )}
      <div className={styles.heading}>
        <Seo seoPage={seoPage} {...seo} />
        <p className={cn(styles.count, styles.nomargin)}>
          <b>Articles found</b>: {list.total} available{' '}
        </p>
      </div>
      <ChipSelect
        name="category"
        className={styles.filter}
        value={category}
        options={filters}
        onChange={handleCategoryChange}
        onClick={handleCategoryClick}
      />
      <div className={styles.content}>
        {list.items.map((item) => (
          <ArticlePreview key={item.id} article={item} />
        ))}
      </div>
      <Paginator
        total={list.total}
        page={parseInt(String(page))}
        perPage={ARTICLE_PAGE_LIMIT}
        prefixPage={'?page='}
        prefixUrl={category ? `/blog/${category}` : '/blog'}
        scroll={false}
        prefetch={true}
      />
    </>
  );
};

export default ArticleList;
