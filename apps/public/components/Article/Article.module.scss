@import 'styles/variables';
@import 'styles/mixins';

.meta {
  display: flex;
  flex-direction: column;
  font-size: $text-14;
  gap: 0.625rem;
}

.date {
  font-weight: 600;
  color: $grey-80;
}

.categories {
  font-size: $text-12;
}

.content img {
  display: block;
  margin: 30px auto;
  max-width: 100%;
  height: auto !important; /* stylelint-disable-line declaration-no-important */

  @include desktop {
    margin: 45px auto;
  }
}

.content a {
  color: $blue-100;
  scroll-margin-top: 80px;
}

.content iframe {
  max-width: 100%;
}

.content p {
  word-break: break-word;
}

.image {
  width: 100%;
}

.tldr {
  border-radius: 8px;
  background-color: $violet-10;
  padding: 0.1px 3rem;
}

.tldr-content {
  margin-block: var(--margin-p);
}

.container {
  max-width: 800px;
  margin: 0 auto;
}

.author {
  color: $grey-80;
}
