import React from 'react';
import dayjs from 'dayjs';
import Image from 'next/image';
import cn from 'classnames';
import Link from 'next/link';
import Categories from './Categories';
import { deterministicRandom } from '../../utils/deterministicRandom';
import { DEFAULT_IMAGE } from '../../services/endpoints/articles';
import type { ListArticle } from '../../services/endpoints/articles';

import styles from './ArticlePreview.module.scss';

const ArticlePreview = ({ article }: { article: ListArticle }) => {
  const { title, slug, time, summary, categories, author, thumbnailImage } =
    article;
  const thumbnail = thumbnailImage.size4;
  const date = dayjs(time).format('MMM D, YYYY');
  const category = categories[0].slug;

  return (
    <Link href={`/blog/${category}/${slug}`} className={styles.card}>
      <Image
        src={thumbnail.href}
        width={250}
        height={500}
        alt={title}
        className={styles.image}
        // ToDo: maybe fix imageLoader to optimize outside images
        unoptimized={thumbnail.href !== DEFAULT_IMAGE.href}
      />
      <div className={styles.content}>
        <p className={cn(styles.date, styles.nomargin)}>
          {date}
          <span className={styles.divider}>
            {deterministicRandom(article.id, 5, 8)} min read
          </span>
        </p>
        <h3 className={cn(styles.title, styles.clamp)}>{title}</h3>
        <p className={cn(styles.summary, styles.clamp, styles.nomargin)}>
          {summary}
        </p>
        <Categories className={styles.categories} categories={categories} />
        {author && (
          <p className={cn(styles.author, styles.nomargin)}>
            <i>Posted by {author.name}</i>
          </p>
        )}
      </div>
    </Link>
  );
};

export default ArticlePreview;
