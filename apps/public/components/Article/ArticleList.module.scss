@import 'styles/variables';
@import 'styles/mixins';
@import 'styles/article-commons';

.heading {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 5px;

  @include tablet {
    flex-flow: row wrap;
    align-items: baseline;
    gap: 20px;
  }

  h1,
  p {
    margin: 0;
  }

  h1 {
    flex-shrink: 0;
  }
}

p.count {
  flex-shrink: 0;

  &.nomargin {
    // ToDo: issue with prod build stylesheet order

    margin: 0;
  }
}

.left {
  text-align: center;
  margin-left: -$space-15;

  @include desktop {
    text-align: left;
    margin-left: -$space-30;
  }
}

.title-type-a {
  background-image: $gradient-green-blue;
  display: inline-block;
  color: transparent;
  font-size: 30px;
  font-weight: 700;
  text-transform: uppercase;
  line-height: 1;
  margin-bottom: $space-30;
  background-clip: text;
}

.title-type-b {
  background-image: $gradient-btn-orange;
  display: inline-block;
  color: transparent;
  font-size: 30px;
  font-weight: 700;
  text-transform: uppercase;
  line-height: 1;
  margin-bottom: $space-30;
  background-clip: text;
  margin-top: $space-40;
}

.content {
  @extend %article-grid;
}

.load {
  position: relative;
  text-align: center;
  margin-top: $space-65;
  margin-bottom: $space-40;

  &::after {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 0;
    right: 0;
    height: 1px;
    background-color: $black;
    opacity: 0.15;
  }
}

.filter {
  font-size: $text-14;
  min-width: 0;
  margin: 20px 0;

  @media (max-width: $breakpoint-tablet) {
    flex-wrap: nowrap;
    padding: 0 var(--main-padding);
    overflow-x: auto;
    width: calc(100% + var(--main-padding) * 2);
    margin-left: calc(var(--main-padding) * -1);
    margin-right: calc(var(--main-padding) * -1);
  }

  @include tablet {
    margin: 30px 0;
  }
}
