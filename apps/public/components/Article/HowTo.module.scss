@import 'styles/variables';
@import 'styles/mixins';
@import 'styles/article-commons';

.content {
  @extend %container;

  padding: $padding;
  padding-top: calc($padding / var(--line-height-h2));
  width: 100%;

  & p,
  li,
  ol {
    margin: 0;
  }
}

.header {
  display: flex;
  flex-direction: column;
  gap: $gap-sm;
  margin-bottom: $gap-bg;
}

.title {
  @extend %title;

  width: 100%;
}

.how-to {
  @extend %margin-top;

  margin-bottom: $gap-bg;
}

.ol {
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: $gap-bg;
}

.li {
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: $gap-sm;
}

.step-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0;
  gap: 8px;
  width: 100%;
}

.step-number {
  width: 25px;
  height: 25px;
  background: $violet-40;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: $text-14;
  color: $white;
  font-weight: 700;
}

$icon-gap: 6px;
$icon-width: 14px;

.tip {
  margin-top: calc($gap-bg - $gap-sm);
  background-color: $violet-10;
  padding: 25px 35px;
  padding-left: calc(25px + $icon-width + $icon-gap);
  border-radius: 8px;
  font-size: $text-14;
  color: $violet-80;
}

.pro-tip {
  position: relative;
}

.bulb {
  width: $icon-width;
  position: absolute;
  fill: currentcolor;
  right: calc(100% + $icon-gap);
  top: 0;
  bottom: 0;
  margin-block: auto;
}

.footer {
  padding-top: $padding;
  display: flex;
  flex-direction: column;
  align-items: center;
}
