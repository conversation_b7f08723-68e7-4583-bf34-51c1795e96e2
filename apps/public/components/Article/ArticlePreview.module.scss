@import '../../styles/variables';
@import '../../styles/mixins';

.card {
  background-color: $white;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  user-select: none;
  cursor: pointer;
  box-shadow: $shadow-article-preview;

  &:hover {
    text-decoration: none;
    box-shadow: $shadow-article-preview-hover;
  }

  p,
  p.nomargin {
    margin: 0;
  }
}

.image {
  border-top-right-radius: inherit;
  border-top-left-radius: inherit;
  width: 100%;
  height: auto;
  aspect-ratio: 2;
  object-fit: cover;
}

.content {
  flex-grow: 1;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  gap: 14px;
}

.divider::before {
  content: '\2022';
  font-weight: 400;
  font-size: 0.5em;
  padding: 0 0.75em;
  position: relative;
  bottom: 0.5em;
}

p.date {
  font-size: var(--text-14);
  font-weight: 600;
  color: $grey-80;

  &.nomargin {
    // ToDo: issue with prod build stylesheet order

    margin: 0;
  }
}

.title {
  --clamp-lines: 2;
  --lh: 1.3;

  font-size: 1.125rem;
  line-height: var(--lh);
  margin: 0;
  font-weight: 800;
}

.card:hover .title {
  color: $blue-100;
}

p.summary {
  --clamp-lines: 3;

  word-break: break-word;
  flex-grow: 1;

  &.nomargin {
    // ToDo: issue with prod build stylesheet order

    margin: 0;
  }
}

.clamp {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: calc(var(--clamp-lines) * var(--lh, 1.6) * 1em);
  line-clamp: var(--clamp-lines);
  -webkit-line-clamp: var(--clamp-lines);
  -webkit-box-orient: vertical;
}

.categories {
  font-size: var(--text-12);
}

p.author {
  font-size: var(--text-14);
  color: $grey-80;

  &.nomargin {
    // ToDo: issue with prod build stylesheet order

    margin: 0;
  }
}
