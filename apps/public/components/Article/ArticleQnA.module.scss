@import '../../styles/variables';
@import '../../styles/mixins';
@import '../../styles/article-commons';

.container {
  display: flex;
  flex-direction: column;
  gap: $gap-bg;
}

.heading {
  @extend %title;
  @extend %margin-top;
}

.accordion {
  @extend %container;

  &[open] {
    summary::after {
      transform: rotate(180deg);
    }
  }
}

.summary {
  cursor: pointer;
  user-select: none;
  font: var(--font-h3);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $padding;

  &::after {
    content: url('#{$assetUrl}/assets/icons/icon-angle-1.svg');
    width: 1.125em;
    color: $grey-60;
    flex-shrink: 0;
    transition: transform 0.2s ease;
  }

  &:hover {
    background-color: $grey-10;
  }
}

.content {
  padding: 0 $padding $padding;
}

.cta {
  margin: 0;
}
