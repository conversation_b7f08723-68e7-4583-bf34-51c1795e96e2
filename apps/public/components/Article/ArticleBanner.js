import { memo } from 'react';
import cn from 'classnames';
import styles from './ArticleBanner.module.scss';
import { useAuth } from '../../contexts/AuthContext';
import { useCTA } from '../../contexts/CTAContext';
import { Amp } from '../../services/amp';
import { ElementViewed } from '../index';
import useTrackElementActions from '../../utils/useTrackElementActions';

const ArticleBanner = () => {
  const { bannerMobileHref, bannerDesktopHref, isDiscountBannerEnabled } =
    useCTA();
  const { isAuthenticated, accountLevel } = useAuth();
  const showBanner =
    isDiscountBannerEnabled &&
    isAuthenticated &&
    !accountLevel?.isPaidOrDelayed;

  const { onTrackClick } = useTrackElementActions({
    name: 'Discount banner',
    type: Amp.element.type.block,
    context: Amp.element.context.ctaBlock,
    autoTrackEnabled: false,
  });

  const onClick = () => {
    onTrackClick(Amp.element.type.block);
  };

  return (
    <>
      {showBanner && (
        <ElementViewed
          name="Discount banner"
          context={Amp.element.context.ctaBlock}
          type={Amp.element.type.block}
        >
          <a
            onClick={onClick}
            href={`${process.env.redirectTalentUrl}/upgrade`}
            className={cn(styles['article-banner-link'])}
          >
            <picture>
              <source srcSet={bannerDesktopHref} media="(min-width: 620px)" />
              <img
                className={styles['article-banner-image']}
                src={bannerMobileHref}
                alt="article-banner-image"
              />
            </picture>
          </a>
        </ElementViewed>
      )}
    </>
  );
};

export default memo(ArticleBanner);
