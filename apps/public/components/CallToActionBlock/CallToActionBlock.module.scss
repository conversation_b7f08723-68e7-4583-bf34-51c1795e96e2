@import '../../styles/mixins';
@import '../../styles/variables';

.interactive-container {
  padding: $space-30;
  background-image: $gradient-cta-container;
  background-color: $violet-10;
  border-radius: 5px;
  color: $violet-100;

  @include tablet {
    padding: $space-30 $space-55 $space-30 $space-60;
    display: flex;
    flex-direction: row;
  }
}

.header {
  text-align: center;
  font-size: 18px;
  font-weight: 700;
  line-height: 130%;
  text-transform: capitalize;

  @include tablet {
    text-align: left;
    font-size: 22px;
    padding-left: 0;
  }
}

.description {
  display: flex;
  margin: $space-10 0 $space-20;
  align-items: center;
  column-gap: $space-20;
  font-size: 14px;
  justify-content: center;

  @include tablet {
    margin: $space-15 0 0;
    column-gap: $space-10;
    font-size: 16px;
    font-weight: 600;
    line-height: 140%;
    justify-content: flex-start;
  }
}

.icon {
  width: 30px;
  height: 30px;
  flex-shrink: 0;

  @include tablet {
    width: 25px;
    height: 25px;
  }
}

.cta-button {
  cursor: pointer;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: $space-10 32px;
  width: 180px;
  background:
    0 0 $gradient-btn-purple,
    $gradient-btn-cta-overlay;
  box-shadow: $shadow-btn-cta;
  background-size: 200% auto;
  border-radius: 156px;
  margin: auto;
  border: none;
  transition: all 0.3s ease-in-out;
  color: $white;
  text-align: center;
  font-size: 14px;
  font-weight: 700;
  line-height: 30px;
  height: 40px;

  &:hover {
    background-position: 100% 50%;
    box-shadow: $shadow-btn-cta-hover;
  }

  @include tablet {
    margin: auto 0 auto $space-20;
    flex-shrink: 0;
  }
}

.subscription-blast-image-container {
  display: flex;
}

.article-banner-image {
  width: 100%;
  height: auto;
  vertical-align: middle;
}

.injected-in-header {
  margin-bottom: $space-30;

  .interactive-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    @include tablet {
      flex-direction: row;
      justify-content: space-between;
    }
  }
}

.injected-in-list {
  margin-bottom: $space-20;

  .interactive-container {
    @include tablet {
      padding-left: $space-40;
      padding-right: $space-20;
    }
  }

  .header {
    @include tablet {
      font-size: 18px;
    }
  }

  .description {
    font-size: 14px;
  }
}

.btn-container {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  @include tablet {
    justify-content: flex-end;
  }
}

.cta-blast-button {
  margin: (-$space-20) 0 0;

  @include mobilexl {
    margin: 0 0 $space-20;
  }
}

.picture {
  max-width: 290px;
  width: 100%;

  @include mobilexl {
    max-width: 400px;
    width: 100%;
  }
}

.subscription-blast {
  border-radius: 5px;
  cursor: pointer;
  background-image: url('#{$assetUrl}/assets/call-to-action-block/subscription-blast-overlay-mobile.webp');
  background-color: #e9e8ff;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  padding: $space-30;

  @include mobilexl {
    background-image: url('#{$assetUrl}/assets/call-to-action-block/subscription-blast-overlay-desktop.webp');
    flex-direction: row;
    padding: $space-20 $space-20 0;
  }
}

.grid {
  .interactive-container {
    .cta-button {
      margin: 0;
    }

    .btn-container {
      flex-grow: 0;
    }

    @include tablet {
      flex-direction: column;
      gap: $space-20;
      margin-bottom: $space-20;
      height: 100%;
      justify-content: center;
      align-items: center;
      padding-left: $space-30;
      padding-right: $space-30;
    }

    @include desktop {
      padding-left: $space-40;
      padding-right: $space-40;
    }
  }

  .interactive-content {
    display: flex;
    flex-direction: column;

    @include tablet {
      align-items: center;
    }
  }

  .subscription-blast {
    flex-direction: column;
    justify-content: center;
    padding: $space-30;
    background-image: url('#{$assetUrl}/assets/call-to-action-block/subscription-blast-overlay-mobile.webp');

    @include mobilexl {
      flex-direction: row;
      background-image: url('#{$assetUrl}/assets/call-to-action-block/subscription-blast-overlay-desktop.webp');
      padding: $space-20 $space-20 0;
      justify-content: space-between;
    }

    @include tablet {
      background-image: url('#{$assetUrl}/assets/call-to-action-block/subscription-blast-overlay-mobile.webp');
      flex-direction: column;
      height: 100%;
      justify-content: center;
      align-items: center;
    }
  }

  .picture {
    max-width: 290px;
    width: 100%;
  }

  .cta-blast-button {
    margin: (-$space-20) 0 0;

    @include tablet {
      margin: (-$space-20) 0 $space-20;
    }
  }
}
