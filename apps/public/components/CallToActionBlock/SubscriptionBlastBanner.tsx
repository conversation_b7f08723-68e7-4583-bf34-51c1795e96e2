import { getImageProps } from 'next/image';
import subscriptionBlastBannerDesktop from '../../public/assets/call-to-action-block/subscription-blast-banner-desktop.webp';
import subscriptionBlastBannerMobile from '../../public/assets/call-to-action-block/subscription-blast-banner-mobile.webp';
import styles from './CallToActionBlock.module.scss';

type SubscriptionBlastBannerProps = {
  listGrid?: boolean;
};

export const SubscriptionBlastBanner = ({
  listGrid,
}: SubscriptionBlastBannerProps) => {
  const {
    props: { srcSet: mobileBannerImage, ...mobileBannerRest },
  } = getImageProps({
    alt: 'article-banner-image',
    src: subscriptionBlastBannerMobile,
  });

  const {
    props: { srcSet: desktopBannerImage, ...desktopBannerRest },
  } = getImageProps({
    alt: 'article-banner-image',
    src: subscriptionBlastBannerDesktop,
  });

  return (
    <picture className={styles.picture}>
      <source srcSet={mobileBannerImage} media="(max-width: 620px)" />
      {listGrid && (
        <source srcSet={desktopBannerImage} media="(max-width: 768px)" />
      )}
      <img
        className={styles['article-banner-image']}
        {...(listGrid ? mobileBannerRest : desktopBannerRest)}
      />
    </picture>
  );
};
