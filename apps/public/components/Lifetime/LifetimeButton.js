import { useAuth } from '../../contexts/AuthContext';
import { useModalContext } from '../../contexts/ModalContext';
import { Amp } from '../../services/amp';
import Button from '../Button/Button';

const LifetimeButton = ({
  label = '',
  minWidth = '',
  color = '',
  shadow = false,
  className,
}) => {
  const { onLoginModalOpen } = useModalContext();
  const { isAuthenticated } = useAuth();

  const onClick = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'get lifetime',
      scope: Amp.element.scope.lifetimeMembershipPage,
      type: Amp.element.type.button,
    });

    if (isAuthenticated) {
      window.location.href = `${process.env.redirectTalentUrl}/checkout?action=343`;
    } else {
      onLoginModalOpen();
    }
  };

  return (
    <Button
      className={className}
      onClick={onClick}
      kind="primary"
      color={color}
      shadow={shadow}
      label={label}
      minWidth={minWidth}
    />
  );
};

export default LifetimeButton;
