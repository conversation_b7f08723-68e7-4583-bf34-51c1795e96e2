@import '../../styles/variables';
@import '../../styles/mixins';

.btn {
  font-weight: 700;
  font-size: var(--text-14);
  border: 0;
  border-radius: 3em;
  cursor: pointer;
  display: inline-block;
  line-height: 1;
  padding: 12px $space-20;
  text-transform: uppercase;

  h1 {
    font-size: var(--text-14);
    margin: 0;
  }
}

a.btn {
  text-decoration: none;
  text-align: center;

  &:hover {
    text-decoration: none;
  }
}

.primary {
  border: 2px solid transparent;
  background-size: 200% auto;
  background-position: 0 0;
  color: $white;
  transition: all 0.2s ease-in-out;

  &.shadow {
    box-shadow: $shadow-button;
  }

  &.blue {
    background-color: $blue-100;
    background-image: $gradient-btn-blue;

    &:hover:not(:disabled) {
      background-position: 100% 50%;
      transition: all 0.2s ease-in-out;
    }
  }

  &.orange {
    background-color: $yellow-100;
    background-image: $gradient-btn-orange;

    &.shadow {
      box-shadow: $shadow-btn-orange;
    }

    &:hover:not(:disabled) {
      background-position: 100% 50%;
      transition: all 0.2s ease-in-out;
    }
  }

  &.purple {
    color: $white;
    background-color: #7045e6;
    background-image: $gradient-btn-purple;

    &.shadow {
      box-shadow: $shadow-btn-purple;
    }

    &:hover:not(:disabled) {
      background-position: 100% 50%;
      transition: all 0.2s ease-in-out;
    }
  }

  &.greenblue {
    background-color: $green-80;
    background-image: $gradient-btn-green-blue;

    &:hover:not(:disabled) {
      background-position: 100% 50%;
      transition: all 0.2s ease-in-out;
    }
  }

  &.green-gradient {
    background-color: $green-100;
    background-image: $gradient-btn-green-gradient;

    &.shadow {
      box-shadow: $shadow-btn-green-gradient;
    }

    &:hover:not(:disabled) {
      background-position: 100% 50%;
      transition: all 0.2s ease-in-out;
    }
  }

  &.grey {
    opacity: 0.3;
    background-color: transparent;
    border: 1px solid $white;
    color: $white;

    &:hover {
      opacity: 0.7;
    }
  }

  &.pink {
    background-color: #cb1ce7;

    &:hover:not(:disabled) {
      background-color: #b008cb;
      transition: all 0.2s ease-in-out;
    }
  }

  &.white {
    background-color: $white;
    color: $black;

    &:hover {
      background-color: $grey-10;
    }
  }

  &.black {
    background-color: $black;
    color: $white;

    &:hover:not(:disabled) {
      background-color: #4d4d4d;
      transition: all 0.2s ease-in-out;
    }
  }

  &.red {
    background-image: linear-gradient(90deg, $red-100 0%, $red-80 99.13%);

    &.shadow {
      box-shadow: none;
    }

    &:hover:not(:disabled) {
      background-position: 100% 50%;
      transition: all 0.2s ease-in-out;
    }
  }
}

.lowercase {
  text-transform: initial;
}

.secondary {
  background: transparent;

  &.blue {
    color: $blue-100;
    border: 2px solid $blue-100;

    &.disabled {
      opacity: 0.3;
      color: $black;
      border: 2px solid $black;
    }

    &:hover:not(:disabled) {
      box-shadow: $shadow-btn-blue-hover;
    }
  }

  &.orange {
    color: $white;
    border: 2px solid $yellow-100;

    &:hover:not(:disabled) {
      box-shadow: $shadow-btn-orange-hover;
    }
  }

  &.white {
    color: $white;
    border: 2px solid $white;

    &:hover:not(:disabled) {
      box-shadow: $shadow-btn-white-hover;
    }
  }

  &.purple {
    color: #523d8f;
    border: 2px solid #7045e5;

    &:hover:not(:disabled) {
      box-shadow: $shadow-btn-purple-hover;
    }
  }

  &.greenblue {
    color: $green-80;
    background:
      $gradient-btn-white padding-box,
      $gradient-btn-green-blue border-box;
    border: 2px solid transparent;

    &:hover:not(:disabled) {
      border: 4px solid transparent;
      padding: $space-10 18px;
    }
  }

  &.midnight {
    font-weight: 400;
    font-size: 14px;
    text-transform: capitalize;
    color: $violet-60;
    padding: 12px 16px;
    border: 1px solid rgba($violet-60, 0.4);

    &:hover {
      box-shadow: $shadow-btn-midnight-hover;
    }
  }

  &.red {
    color: $white;
    border: 2px solid $red-100;

    &:hover:not(:disabled) {
      box-shadow: inset 0 0 0 2px $red-100;
    }
  }

  &.black {
    color: $black;
    border: 2px solid $black;

    &:hover:not(:disabled) {
      box-shadow: inset 0 0 0 2px $black;
    }
  }
}

.disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
