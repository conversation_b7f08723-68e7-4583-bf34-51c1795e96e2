import { memo } from 'react';
import styles from './ProfileCreditCard.module.scss';
import Image from 'next/image';
import dayjs from 'dayjs';

const ProfileCreditCard = ({ title, company, description, year, month }) => {
  return (
    <div className={styles['profile-credit-card']}>
      <div>
        <span className={styles['card-title']}>{title}</span>
        <span className={styles['card-subtitle']}>by {company}</span>
      </div>
      <hr className={styles['separator']} />
      <span className={styles['card-description']}>{description}</span>
      <div className={styles['card-date']}>
        <Image
          src={'/assets/icons/icon-calendar-1.svg'}
          width={15}
          height={15}
          alt="calendar icon"
        />
        <span>{dayjs(`${year}-${month}`).format('MMMM, YYYY')}</span>
      </div>
    </div>
  );
};

export default memo(ProfileCreditCard);
