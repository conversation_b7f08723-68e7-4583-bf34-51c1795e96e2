@import '../../styles/variables';

.profile-credit-card {
  border: 1px dashed $grey-60;
  position: relative;
  padding: $space-20;
  scroll-margin-top: 80px;
  display: flex;
  flex-direction: column;
  gap: $space-20;
}

.card-title {
  color: $black;
  font-size: 18px;
  font-weight: 700;
  line-height: 1.3;
  display: block;
  word-break: break-word;
}

.card-subtitle {
  font-size: 14px;
  color: $grey-100;
  font-weight: 300;
}

.card-date {
  font-weight: 300;
  display: flex;
  gap: $space-5;
  align-items: center;
  font-size: 14px;
}

.card-description {
  font-weight: 300;
  word-break: break-word;
}

.separator {
  display: block;
  height: 1px;
  background-color: $grey-80;
  opacity: 0.15;
  margin: 0;
  border: none;
}
