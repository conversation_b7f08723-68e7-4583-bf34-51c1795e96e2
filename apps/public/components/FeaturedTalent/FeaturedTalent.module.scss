@import '../../styles/mixins';
@import '../../styles/variables';

.featured-talent-container {
  position: relative;
}

.image-container {
  display: block;
  border-radius: 50%;
  border: 1px solid $grey-60;
  position: relative;
  width: 86px;
  height: 86px;
  padding: 3px;
  flex: 0 0 auto;
  margin-bottom: $space-25;
  cursor: pointer;

  @include desktop {
    width: 100px;
    height: 100px;
  }
}

.image {
  position: relative;
  height: 100%;
  background-color: $grey-60;
  border-radius: 50%;
  overflow: hidden;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.rating-badge {
  font-size: 14px;
  font-weight: 300;
  position: absolute;
  bottom: 5px;
  right: 0;
  display: flex;
  flex-direction: column;
  align-items: center;

  @include desktop {
    bottom: 10px;
  }
}
