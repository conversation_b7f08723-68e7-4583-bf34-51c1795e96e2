import { memo } from 'react';
import Image from 'next/image';
import styles from './FeaturedTalent.module.scss';

const FeaturedTalent = ({ imageSrc, rating, openProfilePopup }) => {
  return (
    <div
      className={styles['featured-talent-container']}
      data-cy="featured-talent"
    >
      <div onClick={openProfilePopup} className={styles['image-container']}>
        <div
          className={styles['image']}
          style={{
            backgroundImage: `url('${imageSrc}')`,
          }}
        ></div>
      </div>
      <div className={styles['rating-badge']}>
        <Image
          src={'/assets/icons/icon-star-3.svg'}
          width={21}
          height={21}
          alt="rating badge"
        />
        <span>{rating}</span>
      </div>
    </div>
  );
};

export default memo(FeaturedTalent);
