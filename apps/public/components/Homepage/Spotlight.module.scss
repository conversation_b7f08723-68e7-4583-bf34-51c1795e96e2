@import '../../styles/variables';
@import '../../styles/mixins';

.wrapper {
  display: flex;
  width: 100%;
  background-color: $white;

  @include desktop {
    margin-top: 0;
  }
}

.container {
  margin-left: auto;
  margin-right: auto;
  padding-left: $space-20;
  padding-right: $space-20;
  max-width: $content-max-width;
  background: url('#{$assetUrl}/assets/homepage/spotlight-layer-bg.webp')
    50% -89px no-repeat;
  width: 100%;

  @include desktop {
    padding-left: $space-60;
    padding-right: $space-60;
  }
}

.inner {
  display: flex;
  flex-direction: column;

  @include desktop {
    flex-direction: row;
  }
}

.header {
  color: $grey-80;
  width: 100%;
  border-bottom: 1px solid $grey-40;
  padding: $space-20 0;
  font-weight: 300;

  @include desktop {
    padding: $space-40 0 $space-20;
  }
}

.spacer {
  display: none;
  width: 40px;
  margin-top: $space-60;
  border-top: 2px solid $black;

  @include desktop {
    display: inline;
  }
}

.hide-mobile {
  display: none;

  @include desktop {
    display: inline-block;
  }
}

.description {
  padding: 0;
  z-index: 1;

  & .title {
    font-size: 24px;
    line-height: 1;
    margin-bottom: $space-25;
    font-weight: 700;
    color: $black;
  }

  & .summary {
    display: none;
    color: $black;
    margin-left: $space-5;
    margin-right: $space-10;
    margin-bottom: $space-20;
    line-height: 1.9;

    @include desktop {
      display: block;
    }
  }

  .mobile-read-more {
    margin-left: auto;
    color: $blue-100;
    display: block;
    font-weight: 400;

    @include desktop {
      display: none;
    }
  }

  @include desktop {
    padding-left: $space-30;
    padding-right: $space-30;
    margin-top: 0;
    width: 50%;
    min-width: 500px;
    align-items: flex-start;

    & .title {
      font-size: 48px;
      font-weight: 900;
    }
  }
}

.talent {
  display: flex;
  text-align: center;
  justify-content: center;
  padding-top: $space-30;
  width: 100%;

  img {
    color: transparent;
    width: 100%;
    height: auto;
  }

  & .image {
    width: 100%;
    max-width: 500px;
    margin-top: -$space-50;
    transition: all 0.2s ease-in-out;
  }

  @include desktop {
    align-items: flex-end;
    padding-top: 0;

    & .image {
      max-width: 548px;
      margin-top: -130px;
    }
  }
}
