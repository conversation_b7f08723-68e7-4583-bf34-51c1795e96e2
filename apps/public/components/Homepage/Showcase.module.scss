@import '../../styles/variables';
@import '../../styles/mixins';

.wrapper {
  display: flex;
  width: 100%;
  background-color: $white;

  @include desktop {
    margin-top: 0;
  }
}

.container {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: $space-20;
  max-width: $content-max-width;
  width: 100%;

  @include desktop {
    padding-left: $space-60;
    padding-right: $space-60;
  }
}

.thumbs {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-top: $space-20;
  gap: $space-10;
  z-index: 1;

  @include tablet {
    margin-top: -$space-50;
    gap: 0;
  }
}

.thumb {
  position: relative;
  display: flex;

  &:nth-child(n + 3) {
    display: none;
  }

  @include mobile {
    &:nth-child(n) {
      display: flex;
      justify-content: center;
    }

    &:nth-child(n + 4) {
      display: none;
    }
  }

  @include tablet {
    &:nth-child(n) {
      display: flex;
    }
  }
}

.thumbnail {
  display: flex;
  position: relative;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  cursor: pointer;
  height: 100%;
  outline: 2px solid transparent;
  box-sizing: border-box;
  transition: all 0.2s ease-in-out;

  &:hover {
    outline: 2px solid grey;
    transition: all 0.2s ease-in-out;
    z-index: 3;
  }
}

.thumbnail-mobile {
  display: flex;
  position: relative;
  overflow: hidden;
  border-radius: 10px;
}

.thumbnail-desktop-image {
  object-fit: contain;
  height: auto;
  width: 100%;
  transform: scale(1);
  transition: all 0.2s ease-in-out;
  background-color: $grey-10;

  &:hover {
    transform: scale(1.1);
    transition: all 0.2s ease-in-out;
  }
}

.thumbnail-mobile-image {
  transform: scale(1);
  transition: all 0.2s ease-in-out;
  max-width: 100%;
  width: 184px;
  height: auto;
  min-height: 200px;
  background-color: $white;
  object-fit: cover;

  @include desktop {
    width: initial;
    min-height: unset;
  }

  &:hover {
    transform: scale(1.1);
    transition: all 0.2s ease-in-out;
  }
}

.active-thumb {
  outline: 2px solid grey;
  z-index: 4;
}

.arrow {
  position: absolute;
  left: 50%;
  bottom: -13px;
  transform: translate(-50%);
  border-width: 12px 13px 0;
  border-color: grey transparent transparent;
  border-style: solid;
}

.casting-slide {
  font-weight: 300;
  color: $black;
  margin-bottom: $space-15;
  line-height: 30px;
  margin-top: $space-45;
}

.casting-info {
  display: flex;
  align-items: center;
  padding-bottom: $space-15;
  font-size: 12px;
  line-height: 1.5;
}

.casting-description {
  a[href] {
    color: $blue-100;
    font-weight: 400;
  }
}

.hot {
  text-transform: uppercase;
  color: $red-60;
  font-weight: 700;
}

.label {
  &::after {
    content: '\2022';
    opacity: 0.5;
    font-weight: 200;
    margin-left: 7px;
    margin-right: 7px;
    color: $grey-60;
  }

  &.location {
    display: flex;
    align-items: center;

    &::before {
      display: inline-block;
      background: url('#{$assetUrl}/assets/icons/icon-pin-2.svg') center center
        no-repeat;
      background-size: cover;
      content: '';
      width: 8px;
      height: 11px;
      line-height: 0;
      margin-right: 5px;
    }
  }
}

.slide-content {
  display: flex;
  flex-direction: row;
}

.slide-description {
  padding-right: $space-40;
  overflow-y: hidden;
  max-height: 330px;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 80px;
    background: $gradient-showcase-slide;
  }
}

.slide-image {
  min-width: 450px;
  display: none;

  @include desktop {
    display: block;

    & img {
      max-width: 100%;
    }
  }
}

.explore {
  margin-top: $space-40;
}

.icon {
  background-image: $gradient-icon-orange;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  min-width: 40px;
  margin-right: 18px;
  display: none;
  align-items: center;
  justify-content: center;

  @include desktop {
    left: -20px;
    top: 0;
  }

  &.icon-acting {
    background-image: $gradient-icon-acting;
    display: flex;
  }

  &.icon-musicvideos {
    background-image: $gradient-icon-music-videos;
    display: flex;
  }

  &.icon-dancing {
    background-image: $gradient-icon-dancing;
    display: flex;
  }

  &.icon-modeling {
    background-image: $gradient-icon-modeling;
    display: flex;
  }

  &.icon-promotionalmodels {
    background-image: $gradient-icon-promotional-models;
    display: flex;
  }

  &.icon-realitytv {
    background-image: $gradient-icon-reality-tv;
    display: flex;
  }

  &.icon-singing {
    background-image: $gradient-icon-singing;
    display: flex;
  }

  &.icon-theater {
    background-image: $gradient-icon-theater;
    display: flex;
  }

  &.icon-voiceover {
    background-image: $gradient-icon-voiceover;
    display: flex;
  }

  &.icon-pageant {
    background-image: $gradient-icon-pageant;
    display: flex;
  }

  &.icon-other {
    background-image: $gradient-icon-other;
    display: flex;
  }

  &.icon-extras {
    background-image: $gradient-icon-extras;
    display: flex;
  }

  &.icon-commercials {
    background-image: $gradient-icon-commercials;
    display: flex;
  }

  &.icon-contentcreators {
    background-image: $gradient-icon-content-creators;
    display: flex;
  }

  &.icon-influencers {
    background-image: $gradient-icon-influencers;
    display: flex;
  }
}

.icon-mobile {
  position: absolute;
  border: 1px solid $white;
  left: 5px;
  top: 5px;
}

.title-mobile {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  width: 100%;
  height: 100%;
  gap: 10px;
  position: absolute;
  color: $white;
  z-index: 2;
  left: 0;
  bottom: 0;
  padding: $space-35 $space-15 $space-10;
  background-image: $gradient-showcase-title;
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
}

.btn-apply {
  margin-right: $space-20;
  font-size: 14px;
}

.category-list {
  margin-bottom: $space-15;
  width: 60%;
}

.payment {
  margin-right: auto;

  @include desktop {
    margin-left: auto;
    margin-right: 0;
    padding-left: $space-10;
  }
}

.display-desktop {
  display: none;

  @include desktop {
    display: block;
  }
}

.display-mobile {
  display: block;

  .thumbs {
    margin: $space-20 $space-20 0;
  }

  @include desktop {
    display: none;
  }
}
