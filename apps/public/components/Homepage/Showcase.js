import React, { memo, useState } from 'react';
import styles from './Showcase.module.scss';
import Image from 'next/image';
import cn from 'classnames';
import dayjs from 'dayjs';
import { useModalContext } from '../../contexts/ModalContext';
import { Button, Carousel, CategoryList, PaymentLabel } from '../index';
import Link from 'next/link';
import { Amp } from '../../services/amp';

const Showcase = ({ promos }) => {
  const { onAgentSignupModalOpen } = useModalContext();
  const [active, setActive] = useState(
    promos.findIndex((promo) => promo.url?.length === 0),
  );

  const select = (index, url = '') => {
    Amp.track(Amp.events.elementClicked, {
      name: 'image with link',
      scope: Amp.element.scope.homepage,
      section: Amp.element.section.showcase,
      type: Amp.element.type.tab,
    });

    if (url.length > 0) {
      // Promotion is not a casting call
      window.location.href = url;
    } else {
      setActive(index);
    }
  };

  const Thumbnail = (promo, index) => {
    const { url, desktopImageHref, castingCall } = promo;
    const { title } = castingCall || {};

    return (
      <div
        key={`desktop-${index}`}
        className={styles['thumb']}
        onClick={() => select(index, url)}
      >
        <div
          className={cn(styles.thumbnail, {
            [styles['active-thumb']]: active === index,
          })}
        >
          <Image
            alt={title || 'Thumbnail'}
            src={desktopImageHref}
            width={599}
            height={326}
            className={styles['thumbnail-desktop-image']}
            unoptimized
          />
        </div>
        {active === index && <div className={styles.arrow} />}
      </div>
    );
  };

  const MobileThumbnail = (promo, index) => {
    const { url, storyImageHref, castingCall } = promo;
    const {
      category,
      id,
      title,
      payment_amount,
      payment_period,
      payment_currency,
    } = castingCall || {};
    const iconSlug = category?.name?.toLowerCase().replace(/[\W_]+/g, '');
    const icon = `/assets/icons/categories/icon-${iconSlug}.svg`;

    return (
      <div className={styles.thumb} key={`mobile-${index}`}>
        <Link
          onClick={onViewCastingCallClick}
          href={url.length > 0 ? url : `/castingcall/${id}`}
          className={styles['thumbnail-mobile']}
        >
          <Image
            alt={title || 'Thumbnail'}
            src={storyImageHref}
            width={190}
            height={330}
            className={styles['thumbnail-mobile-image']}
            unoptimized
          />
          <span className={styles['title-mobile']}>
            {title}
            {(payment_amount || payment_period === 'TFP') && (
              <span className={styles.payment}>
                <PaymentLabel
                  paymentAmount={payment_amount}
                  paymentPeriod={payment_period}
                  paymentCurrency={payment_currency}
                />
              </span>
            )}
          </span>
          {iconSlug && (
            <span
              className={cn(
                styles['icon'],
                styles['icon-mobile'],
                styles[`icon-${iconSlug}`],
              )}
            >
              <Image src={icon} width={20} height={20} alt={iconSlug} />
            </span>
          )}
        </Link>
      </div>
    );
  };

  const onViewCastingCallClick = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'view casting call',
      scope: Amp.element.scope.homepage,
      section: Amp.element.section.showcase,
      type: Amp.element.type.button,
    });
  };

  const onPostCastingCallClick = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'post a casting call',
      scope: Amp.element.scope.homepage,
      section: Amp.element.section.showcase,
      type: Amp.element.type.button,
    });

    onAgentSignupModalOpen();
  };

  const CastingSlide = () => {
    const { desktopImageHref, castingCall = {} } = promos[active];
    const {
      hot,
      category,
      location,
      expiration_date,
      expiration,
      id,
      payment_amount,
      payment_period,
      payment_currency,
      title,
      additional_categories,
      description,
    } = castingCall || {};
    const iconSlug = category?.name?.toLowerCase().replace(/[\W_]+/g, '');
    const icon = `/assets/icons/categories/icon-${iconSlug}.svg`;

    return (
      <div className={styles['casting-slide']}>
        <div className={styles['slide-content']}>
          <div className={styles['slide-description']}>
            <div className={styles['casting-info']}>
              <span className={cn(styles.icon, styles[`icon-${iconSlug}`])}>
                <Image src={icon} width={20} height={20} alt={iconSlug} />
              </span>
              {hot && <span className={cn(styles.label, styles.hot)}>Hot</span>}
              <span className={cn([styles.label, styles.location])}>
                {location}
              </span>
              <span>
                {(expiration_date || expiration) &&
                  `Expires on ${dayjs(expiration_date || expiration).format(
                    'M/D/YYYY',
                  )}`}
              </span>
              {(payment_amount || payment_period === 'TFP') && (
                <span className={styles.payment}>
                  <PaymentLabel
                    paymentAmount={payment_amount}
                    paymentPeriod={payment_period}
                    paymentCurrency={payment_currency}
                  />
                </span>
              )}
            </div>
            <div className={styles['category-list']}>
              <CategoryList
                mainCategory={category}
                additionalCategories={additional_categories}
              />
            </div>
            <div
              className={styles['casting-description']}
              dangerouslySetInnerHTML={{
                __html: description,
              }}
            />
          </div>
          <div className={styles['slide-image']}>
            <Image
              alt={title || 'Thumbnail'}
              src={desktopImageHref}
              width={450}
              height={245}
              unoptimized
            />
          </div>
        </div>
        <div className={styles.explore}>
          <Button
            type="link"
            href={`/castingcall/${id}`}
            onClick={onViewCastingCallClick}
            kind="primary"
            color="blue"
            label="View Casting Call"
            minWidth="230px"
            className={styles['btn-apply']}
          />
          <Button
            onClick={onPostCastingCallClick}
            kind="secondary"
            color="blue"
            label="Post Your Casting Call"
            minWidth="230px"
            className={styles['btn-apply']}
          />
        </div>
      </div>
    );
  };

  return (
    <div className={styles.wrapper}>
      <div className={styles.container} data-cy="showcase">
        <div className={styles['display-desktop']}>
          <div className={styles.thumbs}>
            {promos.slice(0, 5).map((promo, index) => Thumbnail(promo, index))}
          </div>
          {promos[active]?.castingCall?.id && <CastingSlide />}
        </div>
        <div className={styles['display-mobile']}>
          <div className={styles.thumbs}>
            <Carousel
              slidesToScroll={1}
              className="carousel-showcase"
              startIndex={1}
              dragFree
            >
              {promos.map((promo, index) => MobileThumbnail(promo, index))}
            </Carousel>
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(Showcase);
