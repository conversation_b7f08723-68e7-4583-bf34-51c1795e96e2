@import '../../styles/variables';
@import '../../styles/mixins';

.wrapper {
  width: 100%;

  @include desktop {
    margin-top: $space-20;
  }
}

.container {
  display: grid;
  grid-template-columns: 1fr;
  margin-left: auto;
  margin-right: auto;
  max-width: $content-max-width;
  width: 100%;

  @include desktop {
    grid-template-columns: 1fr 1fr 1fr;
    padding-left: $space-40;
    padding-right: $space-40;
  }
}

.review {
  position: relative;
  border-radius: 10px;
  background: $grey-20;
  font-size: 16px;
  font-weight: 300;
  line-height: 1.875;
  padding: $space-15 $space-20 $space-20;
  margin: $space-15;

  & .reviewer {
    position: absolute;
    bottom: -55px;
    left: 90px;
    font-size: 14px;
    font-weight: 300;
    line-height: 1.875;

    & .name {
      font-size: 16px;
      font-weight: 700;
      display: block;
    }
  }

  & .reviewer-photo {
    position: absolute;
    bottom: -55px;
    left: 8px;
    width: 74px;
    height: 74px;
    border: 7px solid $white;
    border-radius: 50%;
    object-fit: cover;
  }
}

.read-more {
  width: 100%;
  text-align: center;
  margin: $space-65 0 $space-50;
}

.mobile-hide {
  display: none;

  @include desktop {
    display: block;
  }
}
