import React, { memo } from 'react';
import { Button } from '..';
import cn from 'classnames';
import styles from './Reviews.module.scss';
import { useRouter } from 'next/router';
import { Amp } from '../../services/amp';
import Image from 'next/image';

const Reviews = () => {
  const router = useRouter();
  const onReadMoreClick = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'read more',
      scope: Amp.element.scope.homepage,
      section: Amp.element.section.reviews,
      type: Amp.element.type.button,
    });

    router.push(`/reviews`);
  };

  return (
    <div className={styles.wrapper}>
      <div className={styles.container} data-cy="reviews">
        <div className={styles.review}>
          <p>
            This site is amazing! I got cast to model for NYFW as well as an
            International Commercial. This is only the beginning of the rest of
            my career.
          </p>
          <Image
            alt="allcasting"
            src={'/assets/homepage/reviews/Nycki-Saint.jpeg'}
            width={74}
            height={74}
            className={styles['reviewer-photo']}
          />
          <div className={styles.reviewer}>
            <span className={styles.name}>Nycki Saint</span>
            <span>Talent</span>
          </div>
        </div>
        <div className={cn(styles.review, styles['mobile-hide'])}>
          <p>
            Absolutely love the frequent posting! Plenty of opportunities for
            everyone. Got my first extra lead a week later. A OWN (Delihah)
            credit added to my portfolio.
          </p>
          <Image
            alt="allcasting"
            src={'/assets/homepage/reviews/Keila-Williams.jpeg'}
            width={74}
            height={74}
            className={styles['reviewer-photo']}
          />
          <div className={styles.reviewer}>
            <span className={styles.name}>Keila Williams</span>
            <span>Talent</span>
          </div>
        </div>
        <div className={cn(styles.review, styles['mobile-hide'])}>
          <p>
            Got an extra gig through this app. Check me out in
            &quot;Irresistible&quot; written and directed by Jon Stewart,
            starring Steve Carrell.
          </p>
          <Image
            alt="allcasting"
            src={'/assets/homepage/reviews/Christopher-Walsh.jpeg'}
            width={74}
            height={74}
            className={styles['reviewer-photo']}
          />
          <div className={styles.reviewer}>
            <span className={styles.name}>Christopher Walsh</span>
            <span>Talent</span>
          </div>
        </div>
      </div>
      <div className={styles['read-more']}>
        <Button
          type="link"
          onClick={onReadMoreClick}
          kind="secondary"
          color="blue"
          label="READ MORE REVIEWS"
          minWidth={'230px'}
        />
      </div>
    </div>
  );
};

export default memo(Reviews);
