import React, { memo } from 'react';
import { Button } from '../index';
import styles from './Spotlight.module.scss';
import Image from 'next/image';
import Link from 'next/link';
import { Amp } from '../../services/amp';

const Spotlight = () => {
  const onReadMoreClick = (type) => {
    Amp.track(Amp.events.elementClicked, {
      name: 'read more',
      scope: Amp.element.scope.homepage,
      section: Amp.element.section.spotlight,
      type: type,
    });
  };

  return (
    <div className={styles.wrapper}>
      <div className={styles.container} data-cy="spotlight">
        <div className={styles.header}>
          TALENT <strong>SPOTLIGHT</strong>
        </div>
        <div className={styles.inner}>
          <div className={styles.spacer} />
          <div className={styles.description}>
            <h2 className={styles.title}>
              Rising star <br className={styles['hide-mobile']} />
              <PERSON> shares his Success Story
            </h2>
            <p className={styles.summary}>
              <PERSON> is a 20-year old actor living in New York. In this short,
              but heartfelt interview he tells us about his recent acting gigs,
              his struggles and how he got his first real acting jobs through
              <strong> allcasting</strong>.
            </p>
            <Link
              onClick={() => onReadMoreClick('link')}
              href="blog/success-stories/tyler-rivera-success-story"
              className={styles['mobile-read-more']}
              aria-label="Talent success story"
            >
              — Read more
            </Link>
            <Button
              type="link"
              onClick={() => onReadMoreClick('button')}
              href="blog/success-stories/tyler-rivera-success-story"
              kind="primary"
              color="blue"
              label="Read more"
              minWidth={'230px'}
              className={styles['hide-mobile']}
              ariaLabel="Talent success story"
            />
          </div>
          <div className={styles.talent}>
            <div className={styles.image}>
              <Image
                alt="allcasting"
                src="/assets/homepage/talent-spotlight.webp"
                width={548}
                height={589}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(Spotlight);
