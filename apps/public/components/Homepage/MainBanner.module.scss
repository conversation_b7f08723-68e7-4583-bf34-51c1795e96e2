@import '../../styles/variables';
@import '../../styles/mixins';

.wrapper {
  margin-top: -$space-45;
  align-items: stretch;
  display: flex;
  width: 100%;
  overflow: hidden;
  background-color: #7234b0;
  background-image: linear-gradient(
    120deg,
    #7234b0 0%,
    #9f50a4 62.3%,
    #eec791 99.51%
  );

  @include desktop {
    margin-top: 0;
  }
}

.container {
  display: flex;
  flex-direction: column;
  margin-left: auto;
  margin-right: auto;
  max-width: $content-max-width;
  width: 100%;
  min-height: 513px;
  justify-content: center;
  text-align: center;
  padding-left: $space-20;
  padding-right: $space-20;

  @include desktop {
    background-position: right bottom;
    flex-direction: row;
    padding-left: $space-60;
    padding-right: $space-60;
    text-align: left;
    justify-content: flex-start;
  }
}

.welcome {
  padding: 0;
  margin-top: $space-60;
  z-index: 1;

  & .title {
    margin: 0;
    font-weight: 600;
    line-height: normal;
    color: $white;
    font-size: 28px;
    display: inline-block;
    max-width: 90%;

    @include mobile {
      max-width: 400px;
    }

    @include desktop {
      font-size: 40px;
      font-weight: 700;
      max-width: 100%;
    }

    @include xlarge {
      font-size: 48px;
      font-weight: 700;
    }
  }

  & .description {
    font-size: 14px;
    font-weight: 400;
    color: $white;
    margin-top: $space-20;
    margin-bottom: $space-45;
    line-height: 1.7;

    @include desktop {
      font-size: 16px;
      max-width: 430px;
    }

    @include xlarge {
      font-size: 18px;
      font-weight: 300;
    }

    & h1 {
      margin: 0;
      font-size: inherit;
      font-weight: inherit;
      display: inline;
    }
  }

  & .intro {
    display: none;
    margin-top: $space-25;
    margin-bottom: 0;

    @include desktop {
      display: block;
    }
  }

  @include desktop {
    margin-top: $space-20;
    font-weight: 700;
    padding: $space-30 0;
    width: 55%;
    min-width: 500px;
  }
}

.hero {
  text-align: center;
  width: 100%;

  & .image {
    margin-left: auto;
    margin-right: auto;

    & img {
      width: 140%;
      height: auto;
      margin-left: -31%;
      vertical-align: bottom;
      object-fit: cover;

      @include tablet {
        width: 100%;
        margin-left: -15%;
        margin-top: -100px;
      }

      @include desktop {
        width: 230%;
        margin-left: -75%;
        margin-top: 0;
      }
    }
  }

  @include desktop {
    padding-top: 0;
    width: 40%;
  }
}

.cc-btn {
  margin: $space-20 0 $space-20;

  @include tablet {
    margin: 0 0 0 $space-30;
  }
}

.join {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  & button {
    align-self: center;
  }

  @include tablet {
    margin-bottom: 80px;
    flex-direction: row;
  }

  @include desktop {
    justify-content: flex-start;
  }
}
