import React, { memo } from 'react';
import styles from './MainBanner.module.scss';
import { useModalContext } from '../../contexts/ModalContext';
import { But<PERSON>, Seo } from '../';
import { xTracking } from '../../services/tracking';
import cn from 'classnames';
import { useRouter } from 'next/router';
import { Amp } from '../../services/amp';
import Image from 'next/image';

const MainBanner = ({ seoPage }) => {
  const { onSignUpModalOpen } = useModalContext();
  const router = useRouter();

  const onPostCastingCallClick = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'post a casting call',
      scope: Amp.element.scope.homepage,
      section: Amp.element.section.mainBanner,
      type: Amp.element.type.button,
    });

    const trackingParams = {
      utm_source: 'mainpage',
      utm_medium: 'unknown',
      utm_campaign: 'postacall',
    };

    xTracking(trackingParams);

    router.push('/register/professional');
  };

  const onJoinNowClick = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'join now',
      scope: Amp.element.scope.homepage,
      section: Amp.element.section.mainBanner,
      type: Amp.element.type.button,
    });
    onSignUpModalOpen();
  };

  return (
    <div className={cn(styles.wrapper)}>
      <div className={styles.container}>
        <div className={styles.welcome}>
          <p className={cn(styles.description, styles.intro)}>
            Step Into Showbiz
          </p>
          <span className={styles.title} data-cy="main-title">
            Connecting Talent With Casting Professionals
          </span>
          <div className={styles.description}>
            Discover 1&apos;000s of <Seo seoPage={seoPage} /> and talent looking
            to be in your next project. Get started today.
          </div>
          <div className={styles.join}>
            <Button
              onClick={onJoinNowClick}
              kind="primary"
              color="orange"
              shadow
              label="Join now"
              minWidth={'230px'}
              dataCy="main-banner-join-btn"
            />
            <Button
              onClick={onPostCastingCallClick}
              className={styles['cc-btn']}
              kind="secondary"
              color="white"
              label="Cast a Project"
              minWidth={'230px'}
              dataCy="main-banner-cast-btn"
              shadow={false}
            />
          </div>
        </div>

        <div className={styles.hero}>
          <div className={styles.image}>
            <Image
              alt="allcasting"
              src="/assets/homepage/talent.webp"
              width={1030}
              height={585}
              sizes="(max-width: 1023px) 140vw, (min-width: 1024px) 90vw"
              priority
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(MainBanner);
