import { memo, useEffect, useState } from 'react';
import Image from 'next/image';
import styles from './MobileMenu.module.scss';
import {
  guestLinks,
  authenticatedLinks,
} from '../../constants/menuLinks/mobile-links';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';
import { useAlert } from '../../contexts/AlertContext';
import cn from 'classnames';
import Link from 'next/link';
import { Amp } from '../../services/amp';
import { NumberBadge } from '../index';

const MobileMenu = ({ onSideBarOpen }) => {
  const [isHomePage, setIsHomePage] = useState(false);
  const [menuLinks, setMenuLinks] = useState(guestLinks);
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const { newConversationCount } = useAlert();

  const onMobileMenuOpen = () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'burger open',
      scope: Amp.element.scope.burger,
      section: Amp.element.section.navigation,
      type: Amp.element.type.tab,
    });
    onSideBarOpen();
  };

  useEffect(() => {
    setIsHomePage(router.asPath === '/');
  }, [router]);

  useEffect(() => {
    if (isAuthenticated) {
      setMenuLinks(authenticatedLinks);
    } else {
      setMenuLinks(guestLinks);
    }
  }, [isAuthenticated]);

  const onMenuItemClick = (label) => {
    Amp.track(Amp.events.elementClicked, {
      name: label,
      section: Amp.element.section.navigation,
      type: Amp.element.type.tab,
    });
  };

  return (
    <div className={styles['mobile-menu-container']}>
      <ul className={styles['mobile-menu']}>
        {menuLinks.map(({ id, icon, title, routerLink, aliasLink }, index) => (
          <li key={id} className={styles['mobile-menu-item']}>
            <Link
              href={routerLink}
              onClick={() => onMenuItemClick(title)}
              className={cn(styles['mobile-menu-link'], {
                [styles.active]:
                  router.asPath.includes(routerLink) ||
                  (aliasLink && router.asPath.includes(aliasLink)),
              })}
            >
              <Image
                className={styles['mobile-menu-icon']}
                src={`/assets/icons/icon-${icon}.svg`}
                alt="icon"
                width={40}
                height={20}
              />
              {isHomePage && index === 0 && (
                <h1 className={styles['mobile-menu-txt']}>{title}</h1>
              )}
              {(!isHomePage || (isHomePage && index !== 0)) && (
                <span className={styles['mobile-menu-txt']}>{title}</span>
              )}
              {id === 'inbox' && newConversationCount > 0 && (
                <NumberBadge
                  className={styles.badge}
                  number={newConversationCount}
                />
              )}
            </Link>
          </li>
        ))}
        <li className={styles['mobile-menu-item']}>
          <div
            className={styles['mobile-menu-link']}
            onClick={onMobileMenuOpen}
          >
            <Image
              className={styles['mobile-menu-icon']}
              src={'/assets/icons/icon-menu-burger.svg'}
              alt="icon"
              width={40}
              height={20}
            />
            <span className={styles['mobile-menu-txt']}>menu</span>
          </div>
        </li>
      </ul>
    </div>
  );
};

export default memo(MobileMenu);
