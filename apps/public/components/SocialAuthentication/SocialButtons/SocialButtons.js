import React, { useState } from 'react';
import { SocialLogin } from '../../../constants/socialOptions';
import SocialButton from '../SocialButton/SocialButton';
import styles from './SocialButtons.module.scss';
import Loading from '../../Loading/Loading';

const SocialButtons = (
  props = {
    isLogin: false,
    isModal: false,
    isTalent: false,
    styleTheme: 'light',
  },
) => {
  const [loading, setLoading] = useState(false);
  const buttonProps = { ...props, setLoading };

  return loading ? (
    <Loading minHeight="48px" />
  ) : (
    <div className={styles.wrapper}>
      <SocialButton type={SocialLogin.Google} {...buttonProps} />
      <SocialButton type={SocialLogin.Facebook} {...buttonProps} />
      <SocialButton type={SocialLogin.Apple} {...buttonProps} />
    </div>
  );
};

export default SocialButtons;
