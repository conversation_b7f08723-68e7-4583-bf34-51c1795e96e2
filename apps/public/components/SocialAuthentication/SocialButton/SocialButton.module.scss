@import 'styles/variables';

.button {
  --size: 48px;
  --border-width: 1px;
  --icon-size: 20px;

  height: var(--size);
  width: var(--size);
  border: var(--border-width) solid $grey-40;
  border-radius: 50%;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  cursor: pointer;
  background-color: transparent;

  // using padding instead of centering with flex because apple icon looks better that way
  padding: calc((var(--size) - var(--icon-size)) / 2 - var(--border-width));
}

.icon {
  width: 100%;
}
