import { memo, useState } from 'react';
import styles from './SocialButton.module.scss';
import { SocialModal } from '../../index';
import { CookieService } from '../../../services/cookieService';
import { useRouter } from 'next/router';
import { Amp } from '../../../services/amp';
import { SocialLogin } from '../../../constants/socialOptions';
import { icons } from '../Icons';

const SocialButton = ({
  isLogin = false,
  isModal = false,
  isTalent = false,
  type = SocialLogin.Google,
  styleTheme,
  setLoading,
}) => {
  const [showModal, setShowModal] = useState(false);
  const router = useRouter();

  const openModal = () => {
    Amp.track(Amp.events.modalViewed, {
      name: 'social signup confirmation',
      scope: Amp.element.scope.global,
      section: Amp.element.section.signup,
      method: type,
    });
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
  };

  const login = (event) => {
    setLoading(true);
    event.preventDefault();

    try {
      if (router.asPath.includes('/premium')) {
        CookieService.setRedirectCookie(`/checkout?action=329`);
      }

      if (router.asPath.includes('/lifetime')) {
        CookieService.setRedirectCookie(`/checkout?action=343`);
      }

      let href = `${process.env.publicGateway}/auth/social/${type}/connect`;

      const tracking = CookieService.getTrackingCookie();

      if (tracking) {
        href = `${href}?x-tracking=${encodeURIComponent(
          JSON.stringify(tracking),
        )}`;
      }

      Amp.track(Amp.events.elementClicked, {
        name: 'social login',
        type: Amp.element.type.button,
        method: type,
      });

      window.open(href, '_self');
    } catch (error) {
      setLoading(false);
    }
  };

  const fillOptions = {
    [SocialLogin.Apple]: {
      dark: 'white',
      light: 'black',
    },
  };
  const fill = fillOptions[type]?.[styleTheme];
  const Icon = icons[type];

  return (
    <>
      <button
        className={styles['button']}
        onClick={isLogin ? login : openModal}
        type="button"
      >
        <Icon className={styles.icon} fill={fill} />
      </button>
      {showModal && (
        <SocialModal
          onClose={closeModal}
          isModal={isModal}
          isTalent={isTalent}
          type={type}
        />
      )}
    </>
  );
};

export default memo(SocialButton);
