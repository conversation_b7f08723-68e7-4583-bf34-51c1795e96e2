@import '../../../styles/variables';
@import '../../../styles/mixins';

.container {
  width: 100vw;
  position: fixed;
  bottom: 0;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  padding: $space-35 0 0;

  @include tablet {
    position: relative;
    height: unset;
    max-width: 500px;
    border-radius: 10px;
    padding: $space-40 0 0;
  }
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $space-25;
  padding: 0 $space-20 $space-35;

  .title {
    font-size: 24px;
    font-weight: 700;
    color: $black;
  }

  .description {
    color: $black;
    opacity: 1;
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    margin: 0;
  }

  .link {
    font-size: 16px;
    color: $blue-100;
    cursor: pointer;
    font-weight: 700;
    border-bottom: 1.5px solid transparent;

    &:hover {
      border-bottom: 1.5px solid $blue-100;
    }
  }

  @include tablet {
    padding: 0 $space-40 $space-35;
  }
}

.button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: $space-20;
  width: 100%;

  .button {
    max-width: 160px;
    width: 100%;
  }

  @include tablet {
    gap: $space-30;

    .button {
      max-width: 180px;
    }
  }
}
