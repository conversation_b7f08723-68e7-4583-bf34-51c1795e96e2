import React, { memo, useState } from 'react';
import styles from './SocialModal.module.scss';
import { Button, Loading, Modal, PrivacyPolicy, TermsOfUse } from '../../index';
import { CookieService } from '../../../services/cookieService';
import { useRouter } from 'next/router';
import { Amp } from '../../../services/amp';
import { SocialLogin } from '../../../constants/socialOptions';
import { icons } from '../Icons';

const SocialModal = ({
  onClose,
  isTalent,
  isModal,
  type = SocialLogin.Google,
}) => {
  const [loading, setLoading] = useState(false);
  const [showTerms, setShowTerms] = useState(false);
  const [showPrivacyPolicy, setShowPrivacyPolicy] = useState(false);
  const router = useRouter();

  const toggleShowTerms = (e) => {
    e?.preventDefault();
    setShowTerms(!showTerms);
  };

  const toggleShowPrivacyPolicy = (e) => {
    e?.preventDefault();
    setShowPrivacyPolicy(!showPrivacyPolicy);
  };

  const onConfirm = (event) => {
    setLoading(true);
    event.preventDefault();

    try {
      if (router.asPath.includes('/premium') && isTalent) {
        CookieService.setRedirectCookie(`/checkout?action=329`);
      }

      if (router.asPath.includes('/lifetime') && isTalent) {
        CookieService.setRedirectCookie(`/checkout?action=343`);
      }

      let href = `${
        process.env.publicGateway
      }/auth/social/${type}/connect?type=${isTalent ? 'talent' : 'agent'}`;

      const tracking = CookieService.getTrackingCookie();

      if (tracking) {
        href = `${href}&x-tracking=${encodeURIComponent(
          JSON.stringify(tracking),
        )}`;
      }

      Amp.track(Amp.events.elementClicked, {
        name: 'confirmed social signup',
        type: Amp.element.type.button,
        method: type,
      });

      window.open(href, '_self');
    } catch (error) {
      setLoading(false);
    }
  };

  const labels = {
    [SocialLogin.Apple]: 'Apple ID',
    [SocialLogin.Google]: 'Google',
    [SocialLogin.Facebook]: 'Facebook',
  };
  const Icon = icons[type];
  const titleLabel = labels[type];

  return (
    <>
      <Modal
        backdropClose
        onClose={onClose}
        classNameContainer={styles.container}
        showDefaultLayout={false}
        showMobileBorderRadius={false}
        floatCloseButton
      >
        <div className={styles.content}>
          <Icon width={48} />
          <span className={styles.title}>Sign Up with {titleLabel}</span>
          <p className={styles.description}>
            By choosing to join, I certify I am at least 18 years old and have
            read and agree to the allcasting.com{' '}
            <span className={styles.link} onClick={toggleShowPrivacyPolicy}>
              privacy policy
            </span>{' '}
            and{' '}
            <span className={styles.link} onClick={toggleShowTerms}>
              terms of use
            </span>
            . I agree to receive welcome email, newsletter, SMS & occasional
            account updates from allcasting.com
          </p>
          <div className={styles['button-container']}>
            {loading ? (
              <Loading />
            ) : (
              <>
                <Button
                  className={styles.button}
                  label="Cancel"
                  kind="secondary"
                  onClick={onClose}
                />
                <Button
                  className={styles.button}
                  label="Confirm"
                  shadow={false}
                  onClick={onConfirm}
                />
              </>
            )}
          </div>
        </div>
      </Modal>
      {showTerms && (
        <Modal
          backdropClose
          onClose={toggleShowTerms}
          disableBackgroundScroll={!isModal}
        >
          <TermsOfUse />
        </Modal>
      )}
      {showPrivacyPolicy && (
        <Modal
          backdropClose
          onClose={toggleShowPrivacyPolicy}
          disableBackgroundScroll={!isModal}
        >
          <PrivacyPolicy onNavigation={toggleShowPrivacyPolicy} />
        </Modal>
      )}
    </>
  );
};

export default memo(SocialModal);
