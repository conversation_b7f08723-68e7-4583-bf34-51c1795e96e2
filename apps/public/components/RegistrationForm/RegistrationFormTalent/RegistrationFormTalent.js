import React, { memo, useEffect, useRef, useState } from 'react';
import styles from './RegistrationFormTalent.module.scss';
import cn from 'classnames';
import { Amp } from '../../../services/amp';
import {
  GTM_ACTIONS,
  GTM_CATEGORIES,
  GTM_EVENTS,
} from '../../../constants/analytics';
import { useAnalytics } from 'use-analytics';
import Image from 'next/image';
import {
  Button,
  CheckboxFormik,
  SocialButtons,
  InputFormik,
  Loading,
  Modal,
  PasswordInputFormik,
  PrivacyPolicy,
  TermsOfUse,
} from '../../index';
import { Form, Formik } from 'formik';
import { CookieService } from '../../../services/cookieService';
import * as Yup from 'yup';
import {
  EMAIL_REGEX,
  ErrorMessage,
  PASSWORD_REGEX,
} from '../../../constants/form';
import { useRouter } from 'next/router';
import { useSale } from '../../../contexts/SaleContext';
import { useAuth } from '../../../contexts/AuthContext';
import ApiNoCache from '../../../services/apiNoCache';
import Link from 'next/link';
import * as Sentry from '@sentry/nextjs';

const RegistrationFormTalent = ({
  isModal,
  onRedirecting,
  onClose = () => {},
}) => {
  const [showTerms, setShowTerms] = useState(false);
  const [showPrivacyPolicy, setShowPrivacyPolicy] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const router = useRouter();
  const errorsRef = useRef({});
  const entriesRef = useRef({});
  const isFormStartedRef = useRef(false);

  const { track } = useAnalytics();
  const { refreshSale } = useSale();
  const { refreshUserProfiles, accountLevelVerify } = useAuth();

  const validationSchema = Yup.object({
    email: Yup.string()
      .required(ErrorMessage.EmailRequired)
      .matches(EMAIL_REGEX, ErrorMessage.EmailPattern),
    password: Yup.string()
      .required(ErrorMessage.PasswordRequired)
      .matches(PASSWORD_REGEX, ErrorMessage.PasswordPattern)
      .min(8, ErrorMessage.PasswordPattern),
    agree: Yup.bool().isTrue(),
  });

  useEffect(() => {
    const beforeUnload = () => {
      sendFormAbandonedErrorsToAmplitude();
    };

    // Triggers on tab switch / close and URL change
    window.addEventListener('beforeunload', beforeUnload);

    return () => {
      window.removeEventListener('beforeunload', beforeUnload);

      // Triggers on component unmount
      sendFormAbandonedErrorsToAmplitude();
    };
  }, []);

  const onSubmit = async (values, { resetForm }) => {
    setError('');
    setLoading(true);

    errorsRef.current = {};
    entriesRef.current = { ...values };

    const type = 'talent';
    const registrationResponse = await register({ ...values, type });

    setLoading(false);

    if (registrationResponse.status !== 'error') {
      onRedirecting(true);
      resetForm();
      setError('');

      const loginResponse = await login(
        registrationResponse.identifier,
        registrationResponse.key,
      );

      if (loginResponse.status !== 'error') {
        track(GTM_EVENTS.interaction, {
          target: GTM_CATEGORIES.successReg,
          action: GTM_ACTIONS.registration,
          label: type,
          client: {
            email: values.email,
          },
        });

        const authenticationToken = loginResponse.authenticationToken;
        const volatileToken = loginResponse.volatileToken;
        const tokenExpires = loginResponse.tokenExpires;
        const accountId = loginResponse.id;
        const profileId = loginResponse.links?.profiles?.items[0]?.id;

        CookieService.setAuthenticationCookie(authenticationToken);
        CookieService.setVolatileCookie(volatileToken);
        CookieService.setExpiresCookie(tokenExpires);
        CookieService.setAccountCookie(accountId);
        CookieService.setUserTypeCookie(type);
        CookieService.setProfileCookie(profileId);
        Sentry.setUser({ id: accountId, type });

        await refreshSale();
        await accountLevelVerify();
        await refreshUserProfiles(accountId);

        Amp.track(Amp.events.signUp, {
          type: 'talent',
          provider: 'native',
        });

        if (router.asPath.includes('/premium')) {
          CookieService.setRedirectCookie(`/checkout?action=329`);
        }

        if (router.asPath.includes('/lifetime')) {
          CookieService.setRedirectCookie(`/checkout?action=343`);
        }

        window.location.href = `${process.env.redirectTalentUrl}/wizard`;
      } else {
        Amp.track(Amp.events.invalidEventOccurred, {
          name: `login failed`,
        });

        await router.push('/login');
      }
    } else {
      setError(registrationResponse.message);
      errorsRef.current = {
        registrationResponse: registrationResponse.message,
      };

      sendFormSubmittedErrorsToAmplitude(registrationResponse.message);
    }
  };

  const sendFormStartedEventToAmplitude = () => {
    Amp.track(Amp.events.formStarted, {
      name: 'registration',
    });
  };

  const sendFormSubmittedErrorsToAmplitude = (message) => {
    Amp.track(Amp.events.formSubmitted, {
      name: 'registration errors',
      result: Amp.element.result.fail,
      message: message,
    });
  };

  const sendFormAbandonedErrorsToAmplitude = () => {
    if (Object.keys(errorsRef.current).length) {
      Amp.track(Amp.events.formAbandoned, {
        name: 'registration errors',
        errors: errorsRef.current,
        values: entriesRef.current,
      });
    }
  };

  const toggleShowTerms = (e) => {
    e?.preventDefault();
    setShowTerms(!showTerms);
  };

  const toggleShowPrivacyPolicy = (e) => {
    e?.preventDefault();
    setShowPrivacyPolicy(!showPrivacyPolicy);
  };

  const register = async (values) => {
    return (
      await ApiNoCache.clientAPIRoute(`/register`, {
        method: 'POST',
        body: JSON.stringify(values),
      })
    ).data;
  };

  const login = async (identifier, key) => {
    return (
      await ApiNoCache.clientAPIRoute(`/login/nonce`, {
        method: 'POST',
        body: JSON.stringify({
          identifier,
          key,
        }),
      })
    ).data;
  };

  const onChange = () => {
    if (error) {
      setError('');
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles['form-container']}>
        <Formik
          initialValues={{ email: '', password: '', agree: false }}
          validationSchema={validationSchema}
          onSubmit={onSubmit}
        >
          {({ isValid, dirty, isSubmitting, errors, values, touched }) => {
            if (!isFormStartedRef.current && dirty) {
              isFormStartedRef.current = true;
              sendFormStartedEventToAmplitude();
            }

            errorsRef.current = {};
            entriesRef.current = {};

            if (!isValid && dirty) {
              errorsRef.current = { ...errors };
              entriesRef.current = { ...values };
            }

            return (
              <Form
                onChange={onChange}
                className={styles.form}
                data-cy="talent-registration-form"
              >
                <span className={styles['mobile-title']}>
                  Sign up to become allcasting member
                </span>
                <div className={styles['socials-container']}>
                  <span className={styles['socials-signup']}>Sign up with</span>
                  <SocialButtons isModal={isModal} isTalent />
                  <span className={styles['socials-or']}>or</span>
                </div>
                <InputFormik
                  name="email"
                  placeholder="Email"
                  autoComplete="on"
                />
                <PasswordInputFormik
                  name="password"
                  placeholder="Create Password"
                  hint="Min 8 characters"
                  autoComplete="on"
                />
                <div
                  className={cn(styles['agreement-container'], {
                    [styles['agreement-error']]: touched.agree && errors.agree,
                  })}
                >
                  <CheckboxFormik name="agree">
                    <div className={styles['agreement-text']}>
                      <span>
                        By choosing to join, I certify I am at least 18 years
                        old and have read and agree to the AllCasting.com
                      </span>
                      <span> </span>
                      <span
                        className={styles.link}
                        onClick={toggleShowPrivacyPolicy}
                      >
                        privacy policy
                      </span>
                      <span> and </span>
                      <span className={styles.link} onClick={toggleShowTerms}>
                        terms of use
                      </span>
                      <span> . </span>
                      <span>
                        I agree to receive welcome email, newsletter, SMS &amp;
                        occasional account updates from AllCasting.com
                      </span>
                    </div>
                  </CheckboxFormik>
                </div>
                {error && (
                  <small className={styles['error-message']}>{error}</small>
                )}
                <div
                  className={cn(styles['action-container'], {
                    [styles['loading']]: loading,
                  })}
                >
                  {loading ? (
                    <Loading minHeight="40px" padding="0" />
                  ) : (
                    <div className={styles['btn-box']}>
                      <Button
                        className={styles.button}
                        color="orange"
                        disabled={!(isValid && dirty) || isSubmitting}
                        type="submit"
                        label="Sign Up"
                        minWidth="220px"
                      />
                    </div>
                  )}
                </div>
              </Form>
            );
          }}
        </Formik>
        <div className={styles['link-container-mobile']}>
          <span>Already a member?</span>
          <Link
            href={'/login'}
            className={styles['login-link']}
            onClick={onClose}
          >
            Log in now
          </Link>
        </div>
      </div>
      <div className={styles.footer}>
        <span className={styles['footer-title']}>TRUSTED BY TOP BRANDS</span>
        <div className={styles.brands}>
          <Image
            className={styles.mobile}
            src="/assets/register/brands-mobile.svg"
            width={230}
            height={104}
            alt="brands"
          />
          <Image
            className={styles.desktop}
            src="/assets/register/brands-desktop.svg"
            width={337}
            height={30}
            alt="brands"
          />
        </div>
      </div>
      {showTerms && (
        <Modal
          backdropClose
          onClose={toggleShowTerms}
          disableBackgroundScroll={!isModal}
        >
          <TermsOfUse />
        </Modal>
      )}
      {showPrivacyPolicy && (
        <Modal
          backdropClose
          onClose={toggleShowPrivacyPolicy}
          disableBackgroundScroll={!isModal}
        >
          <PrivacyPolicy onNavigation={toggleShowPrivacyPolicy} />
        </Modal>
      )}
    </div>
  );
};

export default memo(RegistrationFormTalent);
