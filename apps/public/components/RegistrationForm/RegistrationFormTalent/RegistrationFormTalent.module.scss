@import '../../../styles/variables';
@import '../../../styles/mixins';

.form-container {
  display: flex;
  flex-direction: column;
  gap: $space-25;
  padding-top: $space-30;
  box-shadow: $shadow-form-control;

  @include desktop {
    box-shadow: none;
    margin: 0;
    padding: 0 $space-50;
    gap: $space-5;
    justify-content: space-between;
    height: 100%;
  }
}

.form {
  padding: $space-10 $space-30 0;
  display: flex;
  flex-direction: column;
  gap: $space-25;
  border-radius: 3px;

  @include desktop {
    margin: 0;
    padding: 0;
  }
}

.agreement-container {
  display: flex;
  align-items: flex-start;
  padding: $space-20 0;

  @include desktop {
    margin-right: -$space-50;
    margin-left: -$space-50;
    padding: $space-20 $space-40;
  }
}

.agreement-text {
  margin-top: -2px;
  line-height: 20px;

  span {
    font-size: 12px;
    font-weight: 300;
  }

  .link {
    color: $blue-100;
    cursor: pointer;
    font-weight: 700;
  }
}

.action-container {
  display: flex;
  flex-direction: column;
  gap: $space-20;
  align-items: center;
  margin-bottom: -$space-20;
  width: 220px;
  align-self: center;

  .button {
    box-shadow: $shadow-btn-orange;
  }

  .button:disabled {
    opacity: 0.5;
  }

  @include desktop {
    margin-bottom: 0;
    padding-bottom: $space-25;
  }
}

.btn-box {
  background-color: $white;
}

.form-row {
  display: flex;
  flex-direction: column;
  gap: $space-20;
  align-items: center;

  @include desktop {
    flex-direction: row;
    justify-content: space-between;
  }
}

.error-message {
  color: $red-60;
  text-align: center;
  font-size: 14px;
  font-weight: 300;
}

.agreement-error {
  color: $red-60;
}

.loading {
  width: unset;
}

.socials-container {
  display: flex;
  flex-direction: column;
  gap: $space-20;
  align-items: center;
  margin-bottom: -$space-20;

  .socials-signup {
    display: none;

    @include desktop {
      display: initial;
    }
  }

  .socials-or {
    color: $grey-80;
    font-weight: 300;
    font-size: 14px;
  }

  @include desktop {
    padding-top: $space-20;
  }
}

.footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $space-30;
  background-color: $grey-10;
  padding: $space-30 0;

  @include desktop {
    gap: $space-10;
  }
}

.footer-title {
  font-size: 12px;
  font-weight: 400;
  color: $grey-80;
}

.brands {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  width: 100%;
}

.desktop {
  display: none;

  @include desktop {
    display: flex;
  }
}

.mobile {
  display: flex;

  @include desktop {
    display: none;
  }
}

.link-container-mobile {
  display: flex;
  gap: $space-5;
  font-weight: 300;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: $space-20;
  margin-bottom: $space-30;

  @include desktop {
    display: none;
  }
}

.login-link {
  color: $blue-100;
  font-weight: 400;
  cursor: pointer;
}

.mobile-title {
  text-align: center;
  font-size: 24px;
  font-weight: 700;

  @include desktop {
    display: none;
  }
}

.container {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  justify-content: space-between;
}
