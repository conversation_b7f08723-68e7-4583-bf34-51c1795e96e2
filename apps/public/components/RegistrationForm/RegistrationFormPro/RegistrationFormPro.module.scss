@import '../../../styles/variables';
@import '../../../styles/mixins';

.registration-form {
  padding: $space-10 $space-30 0;
  display: flex;
  flex-direction: column;
  gap: $space-25;
  box-shadow: $shadow-form-control;
  border-radius: 3px;
  margin: 0 $space-20;

  @include desktop {
    box-shadow: none;
    margin: 0;
    padding: $space-10 $space-50 0;
  }
}

.agreement-container {
  display: flex;
  align-items: flex-start;
  margin-right: -$space-40;
  margin-left: -$space-40;
  padding: $space-20 $space-40;
  margin-top: $space-20;

  @include desktop {
    background-color: $grey-10;
    margin-right: -$space-50;
    margin-left: -$space-50;
  }
}

.agreement-text {
  margin-top: -2px;
  line-height: 20px;

  span {
    font-size: 12px;
    font-weight: 300;
  }

  .link {
    color: $blue-100;
    cursor: pointer;
    font-weight: 700;
  }
}

.action-container {
  display: flex;
  flex-direction: column;
  gap: $space-20;
  align-items: center;
  margin-bottom: -$space-20;
  width: 220px;
  align-self: center;

  .button {
    box-shadow: $shadow-btn-orange;
  }

  .button:disabled {
    opacity: 0.5;
  }

  @include desktop {
    margin-bottom: 0;
    padding-bottom: $space-20;
  }
}

.btn-box {
  background-color: $white;
}

.form-row {
  display: flex;
  flex-direction: column;
  gap: $space-20;
  align-items: center;

  @include desktop {
    flex-direction: row;
    justify-content: space-between;
  }
}

.zip-form-row {
  display: flex;
  flex-direction: row;
  gap: $space-20;
  align-items: center;
}

.error-message {
  color: $red-60;
  text-align: center;
  font-size: 14px;
  font-weight: 300;
}

.location {
  font-weight: 300;
  min-width: 50%;
}

.agreement-error {
  color: $red-60;
}

.loading {
  width: unset;
}

.socials-container {
  display: flex;
  flex-direction: column;
  gap: $space-20;
  padding-top: $space-10;
  align-items: center;
  margin-bottom: -$space-20;

  .socials-or {
    color: $grey-80;
    font-weight: 300;
    font-size: 14px;
  }
}

.link-container-mobile {
  display: flex;
  gap: $space-5;
  font-weight: 300;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: $space-60;
  margin-bottom: $space-20;

  @include desktop {
    display: none;
  }
}

.login-link {
  color: $blue-100;
  font-weight: 400;
  cursor: pointer;
}
