@import '../../styles/variables';

.star-container {
  display: flex;
  gap: $space-5;
}

.star {
  filter: invert(100%) sepia(0%) saturate(7400%) hue-rotate(276deg)
    brightness(91%) contrast(72%);

  &.selected {
    filter: invert(71%) sepia(97%) saturate(1417%) hue-rotate(359deg)
      brightness(101%) contrast(104%);
  }

  &.dynamic:hover {
    cursor: pointer;
    filter: invert(71%) sepia(97%) saturate(1417%) hue-rotate(359deg)
      brightness(101%) contrast(104%);
  }
}
