import { memo, useState } from 'react';
import styles from './StarRating.module.scss';
import Image from 'next/image';
import cn from 'classnames';

const StarRating = ({
  initialRating = 0,
  fixed = true,
  width = 14,
  height = 14,
  onChange,
}) => {
  const [rating, setRating] = useState(initialRating);
  const [hoveredRating, setHoveredRating] = useState(0);

  const updateRating = (value) => {
    if (!fixed) {
      setRating(value);

      if (onChange) {
        onChange(value);
      }
    }
  };

  const updateHoveredRating = (value) => {
    if (!fixed) {
      setHoveredRating(value);
    }
  };

  return (
    <div className={styles['star-container']}>
      {[...Array(5)].map((id, index) => {
        index += 1;

        return (
          <Image
            title={
              fixed
                ? `${rating} star${rating > 1 ? 's' : ''}`
                : `${index} star${index > 1 ? 's' : ''}`
            }
            onClick={() => updateRating(index)}
            key={index}
            className={cn(styles.star, {
              [styles['selected']]: index <= (hoveredRating || rating),
              [styles['dynamic']]: !fixed,
            })}
            src={'/assets/icons/icon-star-2.svg'}
            width={width}
            height={height}
            alt="star"
            onMouseEnter={() => updateHoveredRating(index)}
            onMouseLeave={() => updateHoveredRating(rating)}
          />
        );
      })}
    </div>
  );
};

export default memo(StarRating);
