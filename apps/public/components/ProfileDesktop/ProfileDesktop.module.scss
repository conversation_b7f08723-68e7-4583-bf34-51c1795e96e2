@import '../../styles/mixins';
@import '../../styles/variables';

$sidebar-menu-width: 320px;

.profile-section {
  display: flex;
  justify-content: space-between;
  margin: 0 auto;
  max-width: 1240px;
  padding-top: $space-60;
  column-gap: $space-40;
}

.profile-container {
  display: flex;
  flex-direction: column;
  gap: $space-20;
  flex-grow: 1;
  align-items: stretch;
  max-width: calc(100% - $sidebar-menu-width - $space-40);
}

.profile-main-images {
  flex: 0 0 320px;
  width: 320px;
  margin-right: $space-40;
}

.profile {
  scroll-margin-top: 80px;
  position: relative;
  display: grid;
  grid-template-columns: 1fr 1.25fr;
}

.profile-overview {
  display: flex;
  flex-direction: column;
  gap: $space-20;
  margin-left: auto;
  padding-bottom: $space-40;
  width: 100%;
}

.profile-overview-header {
  color: $black;
  scroll-margin-top: 80px;
}

.profile-overview-details {
  display: flex;
  justify-content: space-between;
}

.profile-rating {
  display: flex;
  align-items: center;
  gap: 3px;
}

.star {
  filter: invert(71%) sepia(97%) saturate(1417%) hue-rotate(359deg)
    brightness(101%) contrast(104%);
}

.profile-rating-count {
  display: inline-block;
  vertical-align: middle;
  font-weight: 300;
  font-size: 14px;
}

.profile-name {
  display: flex;
  align-items: baseline;
  font-size: 48px;
  font-weight: 700;
  line-height: 1.05;
  position: relative;
  word-break: break-word;
  margin: 0;
}

.profile-id {
  font-size: 14px;
  font-weight: 300;
  text-transform: uppercase;
  opacity: 0.7;
}

.separator {
  display: block;
  height: 1px;
  background-color: $grey-80;
  opacity: 0.15;
  margin: 0;
  border: none;
}

.profile-sidemenu {
  display: block;
}

.profile-main-image-container {
  position: relative;
  padding-top: 133.124%;
  display: flex;
  flex-flow: row wrap;
  align-items: flex-end;
  background-color: $grey-10;
  color: $white;
  text-align: center;
}

.profile-main-image {
  position: absolute;
  inset: 0;
  overflow: hidden;
  background-size: contain;
  background-position: 50% 50%;
  background-repeat: no-repeat;
}

.profile-title {
  font-size: 36px;
  margin: 0;
  scroll-margin-top: 80px;
}

.profile-photo-container {
  display: grid;
  grid-template-columns: repeat(4, minmax(120px, 1fr));
  grid-gap: $space-20;
}

.profile-additional-images {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  grid-gap: $space-20;
}

.profile-video-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.profile-video-title {
  text-align: center;
  font-weight: 300;
}

.profile-btn-container {
  display: flex;
  justify-content: center;
}

.profile-content-section {
  display: flex;
  flex-direction: column;
  gap: $space-20;
}

.video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.video-container {
  position: relative;
  padding-top: 56%;
  margin-bottom: $space-15;
}

.video-card {
  flex: 0 0 50%;
  padding: 0 $space-15;
}

.placeholder-image-container {
  position: relative;
  padding-top: 133.124%;
  display: flex;
  flex-flow: row wrap;
  align-items: flex-end;
  background-color: $grey-10;
  color: $white;
  text-align: center;
  margin-bottom: $space-15;
}

.placeholder-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  padding-top: inherit;
  background-size: auto 37%;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  opacity: 0.3;
}

.placeholder-image-female-side {
  background-image: url('#{$assetUrl}/assets/placeholders/female-side.svg');
}

.placeholder-image-female-full {
  background-image: url('#{$assetUrl}/assets/placeholders/female-height.svg');
}

.placeholder-image-female-head {
  background-image: url('#{$assetUrl}/assets/placeholders/female-head.svg');
}

.placeholder-image-male-side {
  background-image: url('#{$assetUrl}/assets/placeholders/male-side.svg');
}

.placeholder-image-male-full {
  background-image: url('#{$assetUrl}/assets/placeholders/male-height.svg');
}

.placeholder-image-male-head {
  background-image: url('#{$assetUrl}/assets/placeholders/male-head.svg');
}

.placeholder-image-casting-director {
  background-image: url('#{$assetUrl}/assets/placeholders/casting-director.svg');
}

.gallery-modal {
  width: 100vw;
  background-color: transparent;
  display: flex;
  justify-content: center;
}

.gallery-modal-content {
  width: 500px;
  height: unset;
  background-color: transparent;
  border-radius: unset;
}

.breadcrumbs {
  margin-top: $space-40;
  margin-bottom: -($space-60);
}

.castingcall-list {
  padding-left: $space-20;
}

.profile-subtitle {
  font-weight: 700;
  font-size: 16px;
}

.profile-audio-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-gap: $space-15 32px;

  @media (width >= 1172px) {
    grid-template-columns: repeat(2, minmax(300px, 1fr));
  }
}
