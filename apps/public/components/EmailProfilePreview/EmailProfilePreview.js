import { memo } from 'react';
import styles from './EmailProfilePreview.module.scss';
import { maskPhoneNumber } from '../../utils/maskPhoneNumber';
import Image from 'next/image';
import Link from 'next/link';
import { isImageApproved } from '../../utils/imageHelpers';

const EmailProfilePreview = ({ previewId, profile }) => {
  const {
    id,
    name,
    location,
    age,
    ethnicity,
    gender,
    height,
    weight,
    eyeColor,
    hairColor,
    hipSize,
    dressSize,
    bust,
    cupSize,
    phone,
    email,
    socialNetworks,
    closeUpImage,
    sideViewImage,
    fullHeightImage,
    username,
  } = profile;

  return (
    <table id={previewId} className={styles['email-profile-preview-table']}>
      <thead>
        <tr>
          <th className={styles['email-profile-preview-table-header']}>
            <span className={styles['profile-preview-name']}>{name}</span>
            <br />
            <span className={styles['profile-preview-location']}>
              {location}
            </span>
          </th>
        </tr>
      </thead>
      <tbody>
        {age && (
          <>
            <tr>
              <td className={styles['profile-preview-title']}>Age range:</td>
            </tr>
            <tr>
              <td>
                {age < 2 ? 0 : age - 2} - {age + 2}
              </td>
            </tr>
          </>
        )}
        {ethnicity && (
          <>
            <tr>
              <td className={styles['profile-preview-title']}>Ethnicity:</td>
            </tr>
            <tr>
              <td>{ethnicity}</td>
            </tr>
          </>
        )}
        <tr>
          <td className={styles['profile-preview-title']}>Appearance:</td>
        </tr>
        <tr>
          <td>
            <div className={styles['profile-overview-list']}>
              {gender && (
                <span className={styles['profile-overview-list-item']}>
                  {gender}
                </span>
              )}

              {height && (
                <span className={styles['profile-overview-list-item']}>
                  <span> · </span>
                  {height} <strong>height</strong>
                </span>
              )}

              {weight && (
                <span className={styles['profile-overview-list-item']}>
                  <span> · </span>
                  {weight} <strong>weight</strong>
                </span>
              )}

              {eyeColor && (
                <span className={styles['profile-overview-list-item']}>
                  <span> · </span>
                  {eyeColor} <strong>eyes</strong>
                </span>
              )}

              {hairColor && (
                <span className={styles['profile-overview-list-item']}>
                  <span> · </span>
                  {hairColor} <strong>hair</strong>
                </span>
              )}

              {hipSize && (
                <span className={styles['profile-overview-list-item']}>
                  <span> · </span>
                  {hipSize} <strong>hip size</strong>
                </span>
              )}

              {dressSize && (
                <span className={styles['profile-overview-list-item']}>
                  <span> · </span>
                  {dressSize} <strong>dress size</strong>
                </span>
              )}

              {bust && (
                <span className={styles['profile-overview-list-item']}>
                  <span> · </span>
                  {bust} <strong>chest/bust</strong>
                </span>
              )}

              {cupSize && gender === 'female' && (
                <span className={styles['profile-overview-list-item']}>
                  <span> · </span>
                  {cupSize} <strong>cup size</strong>
                </span>
              )}
            </div>
          </td>
        </tr>
        <tr>
          <td className={styles['profile-preview-title']}>Contact info:</td>
        </tr>
        <tr>
          <td>
            {phone && (
              <Link
                prefetch={false}
                href={`tel:${phone}`}
                className={styles.link}
              >
                {maskPhoneNumber(phone)}
              </Link>
            )}

            <Link
              prefetch={false}
              href={`mailto:${email}`}
              className={styles.link}
            >
              · {email}
            </Link>
          </td>
        </tr>
        {socialNetworks.length > 0 && (
          <>
            <tr>
              <td className={styles['profile-preview-title']}>
                Social profiles:
              </td>
            </tr>
            <tr>
              <td>
                {socialNetworks.map(({ id, network, full_url }) => (
                  <a
                    className={styles['social-network-link']}
                    key={id}
                    href={full_url}
                    rel="noopener noreferrer"
                  >
                    <Image
                      src={`/assets/icons/social-network/icon-${network}-1.svg`}
                      width={25}
                      height={25}
                      alt=""
                    />
                  </a>
                ))}
              </td>
            </tr>
          </>
        )}
        {(closeUpImage || sideViewImage || fullHeightImage) && (
          <tr>
            <td>
              {closeUpImage && isImageApproved(closeUpImage) && (
                <img
                  className={styles['profile-preview-image']}
                  src={closeUpImage.proxy_url}
                  alt="headshot"
                />
              )}
              {sideViewImage && isImageApproved(sideViewImage) && (
                <img
                  className={styles['profile-preview-image']}
                  src={sideViewImage.proxy_url}
                  alt="sideshot"
                />
              )}
              {fullHeightImage && isImageApproved(fullHeightImage) && (
                <img
                  className={styles['profile-preview-image']}
                  src={fullHeightImage.proxy_url}
                  alt="heightshot"
                />
              )}
            </td>
          </tr>
        )}
        <tr>
          <td>
            You can see my full profile{' '}
            <Link
              prefetch={false}
              href={`${process.env.baseUrl}/profile/${username || id}`}
              className={styles.link}
            >
              here
            </Link>
          </td>
        </tr>
      </tbody>
    </table>
  );
};

export default memo(EmailProfilePreview);
