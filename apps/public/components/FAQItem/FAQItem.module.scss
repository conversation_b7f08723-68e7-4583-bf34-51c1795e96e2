@import 'styles/variables';
@import 'styles/mixins';

.faq-item {
  padding-bottom: 34px;

  &:not(:last-child) {
    border-bottom: 1px solid $grey-40;
  }

  @include desktop {
    padding-bottom: $space-40;
  }
}

.accordion-heading {
  position: relative;
  display: flex;
  gap: $space-20;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: space-between;
  padding: 34px 0 0;
  cursor: pointer;

  @include desktop {
    padding: $space-35 0 0;
  }
}

.accordion-title {
  font-size: 18px;
  color: $black;
  font-weight: 700;
  line-height: 1.3;
}

.accordion-body {
  max-height: 0;
  overflow: hidden;
  transition: all 0.6s ease-out 0s;
}

.accordion-text {
  padding: $space-20 $space-30 $space-5 0;
  font-size: 16px;
  color: $black;
  line-height: 1.5;

  a {
    color: $blue-100;
  }

  @include desktop {
    padding: 28px $space-30 0 0;
  }
}

.accordion-arrow-icon {
  width: 16px;
  height: 10px;
  flex-shrink: 0;
  background-size: contain;
  transform: rotate(-180deg);
  transition: all 0.3s ease-in 0s;
}

.is-open {
  & .accordion-body {
    max-height: 1000px;
    transition: all 0.6s ease-in 0s;
  }

  & .accordion-text {
    max-height: 1000px;
  }

  & .accordion-arrow-icon {
    transform: rotate(0deg);
  }
}
