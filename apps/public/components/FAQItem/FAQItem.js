import { memo, useState } from 'react';
import styles from './FAQItem.module.scss';
import cn from 'classnames';
import IconArrow from '/public/assets/icons/icon-angle-top.svg';

const FAQItem = ({ title, children }) => {
  const [isActive, setIsActive] = useState(false);

  const toggleActive = () => setIsActive(!isActive);

  return (
    <div
      className={cn(styles['faq-item'], {
        [styles['is-open']]: isActive,
      })}
      onClick={toggleActive}
    >
      <div className={styles['accordion-heading']}>
        <div className={styles['accordion-title']}>{title}</div>
        <IconArrow className={styles['accordion-arrow-icon']} />
      </div>
      <div
        className={cn(styles['accordion-body'], {
          [styles['is-open']]: isActive,
        })}
      >
        <div
          className={styles['accordion-text']}
          dangerouslySetInnerHTML={{ __html: children }}
        ></div>
      </div>
    </div>
  );
};

export default memo(FAQItem);
