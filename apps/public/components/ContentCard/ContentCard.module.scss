@import '../../styles/variables';
@import '../../styles/mixins';

.card {
  width: 100%;
  color: $black;
  background: $white;
  border-radius: 10px;
  box-shadow: $shadow-box-dark-wide;
  padding: $space-30 $space-30 16px;
  margin-bottom: $space-20;
}

.card-title {
  display: block;
  font-size: 18px;
  font-weight: 700;
  line-height: 30px;
  margin-bottom: $space-15;
  word-break: break-word;
}

.card hr {
  background-color: $black;
  opacity: 0.15;
  display: block;
  height: 1px;
}

.card-date {
  font-size: 14px;
  opacity: 0.5;
  font-weight: 300;
  flex-grow: 1;
  align-items: center;
}

.card-more {
  float: right;
  font-size: 16px;
  color: $blue-100;
  font-weight: 400;
}
