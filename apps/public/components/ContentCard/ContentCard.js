import styles from './ContentCard.module.scss';
import React, { memo } from 'react';
import Link from 'next/link';

const ContentCard = ({ title, link, date }) => (
  <div className={styles.card}>
    <span className={styles['card-title']}>
      <Link href={link}>{title}</Link>
    </span>
    <hr />
    <span className={styles['card-date']}>{date}</span>
    <span className={styles['card-more']}>
      <Link href={link}>read article</Link>
    </span>
  </div>
);

export default memo(ContentCard);
