import styles from './ItemDisplayMode.module.scss';
import React, { memo, useCallback } from 'react';
import cn from 'classnames';
import { VIEW_MODE } from '../../constants/castingCalls';
import { Amp } from '../../services/amp';

function ItemDisplayMode({ mode, onClick }) {
  const switchTo = useCallback(
    (newMode) => {
      Amp.track(Amp.events.elementClicked, {
        name: `change list view - ${newMode}`,
        scope: Amp.element.scope.castingCallListPage,
        type: Amp.element.type.button,
      });
      onClick(newMode);
    },
    [onClick],
  );

  return (
    <div className={styles.format}>
      <span
        className={cn(styles.item, styles.list, {
          [styles.active]: mode === VIEW_MODE.List,
        })}
        onClick={() => switchTo(VIEW_MODE.List)}
      >
        list
      </span>
      <span
        className={cn(styles.item, styles.grid, {
          [styles.active]: mode === VIEW_MODE.Grid,
        })}
        onClick={() => switchTo(VIEW_MODE.Grid)}
      >
        grid
      </span>
    </div>
  );
}

export default memo(ItemDisplayMode);
