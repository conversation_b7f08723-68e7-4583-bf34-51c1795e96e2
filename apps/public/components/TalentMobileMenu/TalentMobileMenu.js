import { memo } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import styles from './TalentMobileMenu.module.scss';
import { talentLinks } from '../../constants/menuLinks/mobile-links';
import { useRouter } from 'next/router';
import { useAlert } from '../../contexts/AlertContext';
import cn from 'classnames';
import NumberBadge from '../NumberBadge/NumberBadge';

const TalentMobileMenu = ({ onSideBarOpen, profile }) => {
  const profileLink = `${process.env.redirectTalentUrl}${profile?.profileUrl}/info`;

  const router = useRouter();
  const { newConversationCount } = useAlert();

  return (
    <div className={styles['mobile-menu-container']}>
      <ul className={styles['mobile-menu']}>
        {talentLinks.map(({ id, icon, title, routerLink, isMainAction }) => (
          <li
            key={id}
            className={cn(styles['mobile-menu-item'], {
              [styles['main-mobile-menu-item']]: isMainAction,
            })}
          >
            <Link
              href={id === 'profile' ? profileLink : routerLink}
              className={cn(styles['mobile-menu-link'], {
                [styles.active]: router.asPath.includes(
                  routerLink || profileLink,
                ),
              })}
            >
              {isMainAction ? (
                <div className={styles['mobile-menu-icon-container']}>
                  <Image
                    className={styles['mobile-menu-icon']}
                    src={`/assets/icons/icon-${icon}.svg`}
                    alt="icon"
                    width={20}
                    height={20}
                  />
                </div>
              ) : (
                <div className={styles['mobile-menu-icon-container']}>
                  <Image
                    className={styles['mobile-menu-icon']}
                    src={`/assets/icons/icon-${icon}.svg`}
                    alt="icon"
                    width={40}
                    height={20}
                  />
                  {id === 'inbox' && newConversationCount > 0 && (
                    <NumberBadge
                      className={styles.badge}
                      number={newConversationCount}
                    />
                  )}
                </div>
              )}
              <span className={styles['mobile-menu-txt']}>{title}</span>
            </Link>
          </li>
        ))}
        <li className={styles['mobile-menu-item']}>
          <div className={styles['mobile-menu-link']} onClick={onSideBarOpen}>
            <Image
              className={styles['mobile-menu-icon']}
              src={'/assets/icons/icon-menu-burger.svg'}
              alt="icon"
              width={40}
              height={20}
            />
            <span className={styles['mobile-menu-txt']}>menu</span>
          </div>
        </li>
      </ul>
    </div>
  );
};

export default memo(TalentMobileMenu);
