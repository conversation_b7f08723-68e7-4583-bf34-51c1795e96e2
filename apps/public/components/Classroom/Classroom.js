import React, { memo } from 'react';
import styles from './Classroom.module.scss';
import Carousel from '../Carousel/Carousel';
import Link from 'next/link';
import { Amp } from '../../services/amp';
import cn from 'classnames';
import Image from 'next/image';

const Classroom = ({ articles, style = '' }) => {
  const ClassroomItem = ({ article, index }) => {
    const onClassroomItemClick = () => {
      Amp.track(Amp.events.elementClicked, {
        name: 'classroom item',
        scope: Amp.element.scope.homepage,
        section: Amp.element.section.classroom,
        type: Amp.element.type.banner,
      });
    };

    const { slug, title } = article || {};
    const href = article?.['story-pic']?.sizeStory?.href;

    return (
      <Link
        key={`classroom-${index}`}
        onClick={onClassroomItemClick}
        href={`/blog/${article.categories[0].slug}/${slug}`}
        className={styles.lesson}
      >
        <Image
          className={styles['lesson-banner']}
          alt={title || 'Thumbnail'}
          src={href || '/assets/homepage/classroom/story-pic-placeholder.jpg'}
          width={248}
          height={440}
          unoptimized={!!href}
        />
      </Link>
    );
  };

  return (
    <div className={cn(styles.wrapper, styles[style])}>
      <div className={styles.container} data-cy="classroom">
        <div className={styles.header}>
          LEARN FROM OUR <strong>CLASSROOM</strong>
        </div>
        <div className={styles.inner}>
          <div className={styles.desktop}>
            {articles.map((article, index) => (
              <ClassroomItem key={index} article={article} />
            ))}
          </div>
          <div className={styles.mobile}>
            <Carousel
              slidesToScroll={1}
              className="carousel-classroom"
              startIndex={1}
              dragFree
            >
              {articles.map((article, index) => (
                <ClassroomItem key={index} article={article} />
              ))}
            </Carousel>
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(Classroom);
