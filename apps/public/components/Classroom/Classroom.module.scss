@import '../../styles/variables';
@import '../../styles/mixins';

.wrapper {
  display: flex;
  width: 100%;
  background-color: $grey-10;

  &.light {
    background: none;
  }

  @include desktop {
    margin-top: 0;
  }
}

.container {
  margin-left: auto;
  margin-right: auto;
  padding-left: $space-20;
  padding-right: $space-20;
  margin-bottom: $space-55;
  max-width: $content-max-width;
  width: 100%;

  @include desktop {
    padding-left: $space-60;
    padding-right: $space-60;
  }
}

.header {
  color: $grey-80;
  width: 100%;
  padding: $space-20 0;
  font-weight: 300;

  @include desktop {
    padding: $space-40 0 $space-20;
  }
}

.light {
  .container {
    padding: 0;
    margin: 0;
  }

  .header {
    display: none;
  }
}

.inner {
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.lesson {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  cursor: pointer;
  flex-basis: 244px;

  @include desktop {
    &:nth-child(n) {
      display: flex;
    }
  }
}

.lesson-banner {
  transform: scale(1);
  transition: all 0.2s ease-in-out;
  width: 100%;
  height: auto;
  object-fit: contain;

  &:hover {
    transform: scale(1.1);
    transition: all 0.2s ease-in-out;
  }
}

.desktop {
  display: none;

  @include tablet {
    display: flex;
  }
}

.mobile {
  display: block;

  @include tablet {
    display: none;
  }
}
