import styles from './TermsOfUseToggle.module.scss';
import Modal from '../Modal/Modal';
import TermsOfUse from './TermsOfUse';
import { useState } from 'react';
import { createPortal } from 'react-dom';

const TermsOfUseToggle = ({ children }) => {
  const [show, setShow] = useState(false);
  const toggle = () => setShow((prev) => !prev);

  return (
    <>
      <span className={styles.toggle} onClick={toggle}>
        {children}
      </span>
      {show &&
        createPortal(
          <Modal backdropClose onClose={toggle}>
            <TermsOfUse />
          </Modal>,
          document.body,
        )}
    </>
  );
};

export default TermsOfUseToggle;
