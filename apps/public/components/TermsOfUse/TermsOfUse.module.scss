@import '../../styles/variables';

.terms-section {
  display: flex;
  flex-direction: column;
  padding: 0 $space-20;
  gap: $space-10;
  word-break: break-word;
  color: $black;

  p,
  ol,
  ul {
    text-align: justify;
  }

  p {
    color: $black;
    opacity: 1;
    font-size: 16px;
    font-weight: 300;
    margin: 0;
  }

  li {
    font-weight: 300;
    margin: 5px 0;
  }

  ol,
  ul {
    margin: 0;
  }

  ol {
    & > li > ol {
      list-style-type: lower-alpha;

      & > li > ol {
        list-style-type: lower-roman;
      }
    }
  }
}

p.important {
  text-align: center;
  text-decoration: underline;
}

.support-link {
  display: inline;
  color: $blue-100;
  font-weight: 400;
  word-break: break-word;
}

.terms-title {
  font-size: 24px;
  font-weight: 700;
  word-break: break-word;
}
