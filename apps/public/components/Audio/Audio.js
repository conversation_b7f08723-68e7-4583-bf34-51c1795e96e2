import styles from './Audio.module.scss';
import React, { memo } from 'react';
import { AudioPlayer } from '../index';

const Audio = ({ title, url, isMobileFromUserAgent }) => {
  return (
    <>
      <div className={styles['audio-preview']}>
        <div className={styles['audio-preview-header']}>
          <span className={styles.title}>{title}</span>
        </div>
        <AudioPlayer url={url} isMobileFromUserAgent={isMobileFromUserAgent} />
      </div>
    </>
  );
};

export default memo(Audio);
