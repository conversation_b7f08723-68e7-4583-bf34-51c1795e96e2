@import '../../styles/variables';
@import '../../styles/mixins';

.audio-preview {
  border-radius: 5px;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: $space-15;
  justify-content: space-between;
  gap: $space-5;
  border: 1px solid #d1d1d1d1;
}

.title {
  font-size: 14px;
  color: $black;
  font-weight: 600;
  text-align: center;

  @include tablet {
    font-size: 16px;
  }
}

.audio-preview-actions {
  display: none;
  align-items: center;
  justify-content: center;
  gap: 32px;

  @include tablet {
    display: flex;
  }
}

.icon {
  cursor: pointer;
}

.download {
  display: flex;
}

.audio-preview-header {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  @include tablet {
    .icon {
      display: none;
    }
  }
}

.audio-preview-actions-mobile {
  display: flex;
  flex-direction: column;
  gap: $space-20;
  padding: $space-30 38px;
}

.audio-control-modal {
  border-radius: 10px;
}

.audio-preview-action {
  display: flex;
  align-items: center;
  gap: $space-15;
  font-size: 14px;
  font-weight: 600;
}
