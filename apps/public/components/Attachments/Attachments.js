import styles from './Attachments.module.scss';
import IconVideo from '../../public/assets/icons/icon-video.svg';
import IconAudio from '../../public/assets/icons/icon-audio.svg';

export const Attachment = ({ attachment, openImageAttachment }) => {
  const type =
    attachment.content_type.split('/')[1] === 'pdf'
      ? attachment.content_type.split('/')[1]
      : attachment.content_type.split('/')[0];

  switch (type) {
    case 'image':
      return (
        <button
          className={styles['image-opener']}
          onClick={() => openImageAttachment(attachment.id)}
        >
          <img
            className={styles.image}
            src={attachment.path}
            alt={attachment.name}
          />
        </button>
      );
    case 'pdf':
      return (
        <a
          href={attachment.path}
          target="_blank"
          rel="noreferrer"
          className={styles['attachment-document']}
        >
          PDF
        </a>
      );
    case 'video':
      return (
        <a
          href={attachment.path}
          target="_blank"
          rel="noreferrer"
          className={styles['attachment-document']}
        >
          <IconVideo />
        </a>
      );
    case 'audio':
      return (
        <a
          href={attachment.path}
          target="_blank"
          rel="noreferrer"
          className={styles['attachment-document']}
        >
          <IconAudio />
        </a>
      );
    default:
      return (
        <a
          href={attachment.path}
          target="_blank"
          rel="noreferrer"
          className={styles['attachment-document']}
        >
          DOC
        </a>
      );
  }
};

export default Attachment;
