@import '../../styles/variables';

.zoom-image-inner-container::after {
  content: '';
  position: absolute;
  inset: 0;
  background-color: $black-100-opacity-60;
  background-image: url('#{$assetUrl}/assets/icons/icon-zoom.svg');
  background-repeat: no-repeat;
  background-position: 50% 50%;
  background-size: 70px 70px;
  opacity: 0;
  transition: opacity ease-in-out 0.3s;
}

.zoom-image-inner-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: $grey-10;
  overflow: hidden;
  z-index: 1;

  img {
    width: 100%;
  }

  &:hover {
    cursor: pointer;

    &::after {
      z-index: 100;
      opacity: 1;
    }
  }
}

.zoom-image-container {
  position: relative;
  padding-top: 133.124%;
  margin-bottom: $space-15;
}
