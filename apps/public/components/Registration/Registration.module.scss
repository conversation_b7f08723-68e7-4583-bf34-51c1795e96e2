@import '../../styles/mixins';
@import '../../styles/variables';

.registration-form-container {
  display: flex;
  width: 100%;
  box-shadow: none;
  background-color: $white;

  @include desktop {
    max-width: 820px;
    border-radius: 10px;
    box-shadow: $shadow-card-container;
  }
}

.registration-popup {
  max-width: unset;
  height: 100%;

  @include desktop {
    height: initial;
  }
}

.poster-container {
  display: none;

  @include desktop {
    display: flex;
    flex: 0 0 385px;
    flex-flow: column nowrap;
    color: $white;
    padding: $space-35 $space-30 $space-30;
    justify-content: center;
    background-size: cover;
    background-image: url('#{$assetUrl}/assets/register/signup-woman.webp');
    background-color: $violet-100;
    background-repeat: no-repeat;
    background-position: center;
    gap: $space-20;
    border-bottom-left-radius: 10px;
    border-top-left-radius: 10px;
  }
}

.form-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  background-color: $white;
  height: fit-content;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;

  @include desktop {
    min-height: 725px;
  }

  &.has-hidden-tabs {
    padding: $space-20 0;

    @include desktop {
      padding: $space-50 0;
    }
  }

  &.redirect-in-progress {
    min-height: 0;
  }
}

.tabs {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  justify-content: space-around;
  gap: $space-10;
  border-bottom: unset;

  @include xsmall {
    padding: 0 $space-10;
  }

  @include desktop {
    padding: 0 $space-40;
    gap: $space-40;
    border-bottom: 1px solid $grey-60;
    margin-top: 0;
  }
}

.tab-container {
  padding: $space-30 0 $space-10;
  border-bottom: 3px solid transparent;

  &.active {
    color: $violet-60;
    border-bottom: 3px solid $violet-60;
  }
}

.tab {
  font-size: 16px;
  font-weight: 700;
  line-height: 25px;
  text-transform: uppercase;
  color: $black;
  opacity: 0.5;
  cursor: pointer;

  &:active,
  &:hover {
    text-decoration: none;
  }

  @include xsmall {
    font-size: 18px;
  }
}

.active .tab {
  color: $violet-60;
  opacity: 1;
}

.link-container {
  display: flex;
  gap: $space-10;
  font-weight: 300;
}

.link {
  color: $blue-100;
  font-weight: 400;
  cursor: pointer;
}

.link-container-mobile {
  display: flex;
  gap: $space-5;
  font-weight: 300;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: $space-60;
  margin-bottom: $space-20;

  @include desktop {
    display: none;
  }
}

.redirecting {
  text-align: center;
  padding: $space-50 0;

  h1 {
    margin: 0;
    font-size: 30px;
    font-weight: 700;
  }

  p {
    margin-bottom: $space-40;
  }
}
