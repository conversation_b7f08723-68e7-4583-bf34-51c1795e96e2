import { memo, useState } from 'react';
import styles from './Registration.module.scss';
import {
  HeaderMobile,
  Loading,
  RegistrationForm,
  RegistrationFormPro,
  RegistrationFormTalent,
} from '../index';
import { useRouter } from 'next/router';
import cn from 'classnames';
import Link from 'next/link';
import { Amp } from '../../services/amp';

const Registration = ({
  children,
  isModal,
  closeModal,
  showAgent,
  hideTabs,
  isNewRegistrationFlow,
}) => {
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [isTalent, setIsTalent] = useState(!showAgent);
  const router = useRouter();

  const onRedirecting = async (value) => {
    setIsRedirecting(value);
  };

  const handleNavigateBack = async () => {
    await router.push('/');
  };

  const switchToTalentView = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'talent tab',
      scope: Amp.element.scope.modal,
      type: Amp.element.type.tab,
    });

    if (!isModal) {
      await router.push('/register');
    } else {
      setIsTalent(true);
    }
  };

  const switchToAgentView = async () => {
    Amp.track(Amp.events.elementClicked, {
      name: 'casting director tab',
      scope: Amp.element.scope.modal,
      type: Amp.element.type.tab,
    });

    if (!isModal) {
      await router.push('/register/professional');
    } else {
      setIsTalent(false);
    }
  };

  const closeModalOnRedirect = () => {
    isModal && closeModal();
  };

  return (
    <>
      {!isRedirecting && (
        <HeaderMobile
          marginOverride={!isModal && !isNewRegistrationFlow}
          onClose={isModal ? closeModal : handleNavigateBack}
        >
          {children}
        </HeaderMobile>
      )}
      <div
        className={cn(styles['registration-form-container'], {
          [styles['registration-popup']]: isModal,
        })}
      >
        {!isRedirecting && (
          <div className={styles['poster-container']}>
            {children}
            <div
              className={styles['link-container']}
              onClick={closeModalOnRedirect}
            >
              <span>Already a member?</span>
              <Link href={'/login'} className={styles.link}>
                Log in
              </Link>
            </div>
          </div>
        )}
        <div
          className={cn(styles['form-container'], {
            [styles['has-hidden-tabs']]: hideTabs,
            [styles['redirect-in-progress']]: isRedirecting,
          })}
        >
          {isRedirecting ? (
            <div className={styles.redirecting}>
              <h1>Authentication</h1>
              <p>It will take a moment, please stand by.</p>
              <Loading minHeight="50px" padding="0" />
            </div>
          ) : (
            <>
              {!hideTabs && (
                <div className={styles['tabs']}>
                  <div
                    className={cn(styles['tab-container'], {
                      [styles.active]: isTalent,
                    })}
                  >
                    <span onClick={switchToTalentView} className={styles.tab}>
                      TALENT
                    </span>
                  </div>
                  <div
                    className={cn(styles['tab-container'], {
                      [styles.active]: !isTalent,
                    })}
                  >
                    <span onClick={switchToAgentView} className={styles.tab}>
                      CASTING DIRECTOR
                    </span>
                  </div>
                </div>
              )}
              {isNewRegistrationFlow ? (
                <>
                  {isTalent ? (
                    <RegistrationFormTalent
                      isModal={isModal}
                      onRedirecting={onRedirecting}
                      onClose={closeModal}
                    />
                  ) : (
                    <RegistrationFormPro
                      isModal={isModal}
                      onRedirecting={onRedirecting}
                      onClose={closeModal}
                    />
                  )}
                </>
              ) : (
                <>
                  <RegistrationForm
                    isTalent={isTalent}
                    isModal={isModal}
                    onRedirecting={onRedirecting}
                  />
                </>
              )}
              {!isNewRegistrationFlow && (
                <div className={styles['link-container-mobile']}>
                  <span>Already a member?</span>
                  <Link
                    href={'/login'}
                    className={styles.link}
                    onClick={closeModal}
                  >
                    Log in now
                  </Link>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default memo(Registration);
