export const getServerSideProps = async ({ query }) => {
  const { type } = query;
  const value = {};

  if (type === '404') {
    return {
      notFound: true,
    };
  } else if (type === 'type') {
    const testError = value.items.find((item) => item.id);
  } else if (type === 'prop') {
    return { props: { value: undefined } };
  } else if (type === 'reference') {
    testFunction();
  }

  return {
    props: {},
  };
};
const TestErrorServer = () => {
  return (
    <div>
      <h1>Test server-side errors</h1>
    </div>
  );
};

export default TestErrorServer;
