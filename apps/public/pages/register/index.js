import { useEffect } from 'react';
import styles from '../../styles/register.module.scss';
import { MainLayout, PageLayout } from '../../components/Layouts';
import { useNotifications } from '../../contexts/NotificationContext';
import { Registration, Seo } from '../../components';
import seoPageProps from '../../utils/seoPageProps';
import { sharedServerSideProps } from '../../utils/sharedServerSideProps';
import { Amp } from '../../services/amp';

export const getServerSideProps = async ({ resolvedUrl, req, res }) => {
  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      seoPage: await seoPageProps(resolvedUrl),
      isNewRegistrationFlow: false,
    },
  };
};

export default function Register({ seoPage, isNewRegistrationFlow }) {
  const { setNotification } = useNotifications();

  useEffect(() => {
    if (!navigator.cookieEnabled) {
      setNotification({
        type: 'error',
        message: 'Please enable Cookies to use full website functionality',
      });
    }

    Amp.track(Amp.events.viewRegister, {
      type: 'talent',
    });
  }, []);

  return (
    <MainLayout isDefaultHeaderVisible isMobileMenuVisible>
      {isNewRegistrationFlow ? (
        <main className={styles['new-registration-flow']}>
          <section className={styles['registration-section']}>
            <Registration isNewRegistrationFlow>
              <Seo seoPage={seoPage} />
            </Registration>
          </section>
        </main>
      ) : (
        <PageLayout>
          <section className={styles['registration-section']}>
            <Registration>
              <Seo seoPage={seoPage} />
            </Registration>
          </section>
        </PageLayout>
      )}
    </MainLayout>
  );
}
