import { useEffect } from 'react';
import styles from '../../styles/register.module.scss';
import { MainLayout, PageLayout } from '../../components/Layouts';
import { Registration, Seo } from '../../components';
import { useNotifications } from '../../contexts/NotificationContext';
import seoPageProps from '../../utils/seoPageProps';
import cn from 'classnames';
import { sharedServerSideProps } from '../../utils/sharedServerSideProps';
import { Amp } from '../../services/amp';

export const getServerSideProps = async ({ resolvedUrl, req, res }) => {
  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      seoPage: await seoPageProps(resolvedUrl),
    },
  };
};

export default function Register({ seoPage }) {
  const { setNotification } = useNotifications();

  useEffect(() => {
    if (!navigator.cookieEnabled) {
      setNotification({
        type: 'error',
        message: 'Please enable Cookies to use full website functionality',
      });
    }

    Amp.track(Amp.events.viewRegister, {
      type: 'agent',
    });
  }, []);

  return (
    <MainLayout isDefaultHeaderVisible isMobileMenuVisible>
      <PageLayout>
        <section
          className={cn(
            styles['registration-section'],
            styles['director-form'],
          )}
        >
          <Registration showAgent hideTabs>
            <Seo seoPage={seoPage} />
            <ul className={styles['registration-director-perks']}>
              <li>Free to cast a project</li>
              <li>Easy to find the right talent</li>
              <li>Simple to navigate the virtual casting office</li>
              <li>Quick to reach 2+ Million diverse talent</li>
            </ul>
          </Registration>
        </section>
      </PageLayout>
    </MainLayout>
  );
}
