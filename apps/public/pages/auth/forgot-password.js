import { PageLayout } from '../../components/Layouts';
import styles from '../../styles/forgot.module.scss';
import { ForgotPasswordForm, HeaderMobile, Seo } from '../../components';
import seoPageProps from '../../utils/seoPageProps';
import { sharedServerSideProps } from '../../utils/sharedServerSideProps';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { Amp } from '../../services/amp';

export const getServerSideProps = async ({ resolvedUrl, req, res }) => {
  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      seoPage: await seoPageProps(resolvedUrl),
    },
  };
};

export default function ForgotPassword({ seoPage }) {
  const router = useRouter();

  const navigate = async () => {
    await router.push('/login');
  };

  useEffect(() => {
    Amp.track(Amp.events.viewForgotPassword);
  }, []);

  return (
    <PageLayout>
      <HeaderMobile onClose={navigate} marginOverride>
        <Seo seoPage={seoPage} />
      </HeaderMobile>
      <section className={styles['forgot-section']}>
        <ForgotPasswordForm>
          <Seo seoPage={seoPage} />
        </ForgotPasswordForm>
      </section>
    </PageLayout>
  );
}
