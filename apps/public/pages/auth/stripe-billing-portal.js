import { memo } from 'react';
import {
  authenticate,
  callbackPerpetualLogin,
  checkResponseError,
  extractAccountIdFromToken,
} from '../../utils/authHelper';
import { CookieService } from '../../services/cookieService';
import { xTracking } from '../../services/tracking';
import { Loading, Seo } from '../../components';
import ApiNoCache from '../../services/apiNoCache';
import * as Sentry from '@sentry/nextjs';

export const provideLoginProps = async (
  req,
  res,
  query,
  resolvedUrl,
  callback,
) => {
  const { identifier, key } = query;
  const queryString = JSON.stringify(query);

  if (!identifier || !key) {
    Sentry.captureException(new Error('Stripe authentication missing params'), {
      tags: {
        feature: 'Stripe authentication',
      },
      extra: {
        identifier: identifier,
        key: key,
        query: queryString,
      },
    });

    CookieService.cleanAuthenticationCookies(req, res);

    return {
      redirect: {
        destination: `${process.env.baseUrl}/login`,
        permanent: false,
      },
    };
  }

  try {
    const authenticationResponse = await callback(identifier, key, req);

    const error = checkResponseError(authenticationResponse);

    if (error) {
      Sentry.captureException(
        new Error('Stripe authentication request error'),
        {
          tags: {
            feature: 'Stripe authentication',
          },
          extra: {
            identifier,
            key,
            query: queryString,
            errorMessage: authenticationResponse?.message || '',
          },
        },
      );

      CookieService.cleanAuthenticationCookies(req, res);

      return {
        redirect: {
          destination: `${process.env.baseUrl}/login`,
          permanent: false,
        },
      };
    }

    const { isError } =
      (await authenticate(
        authenticationResponse,
        req,
        res,
        resolvedUrl,
        'Stripe',
      )) || {};

    if (isError) {
      CookieService.cleanAuthenticationCookies(req, res);

      return {
        redirect: {
          destination: `${process.env.baseUrl}/login`,
          permanent: false,
        },
      };
    }

    xTracking(query, req, res);

    const authenticationToken = authenticationResponse.authenticationToken;
    const accountId = extractAccountIdFromToken(authenticationToken);

    return {
      props: {
        accountId: accountId,
      },
    };
  } catch (error) {
    Sentry.captureException(
      new Error('Stripe authentication exception error'),
      {
        tags: {
          feature: 'Stripe authentication',
        },
        extra: {
          identifier,
          key,
          query: queryString,
          error: JSON.stringify(error),
        },
      },
    );

    return {
      redirect: {
        destination: `${process.env.baseUrl}/login`,
        permanent: false,
      },
    };
  }
};

export const getServerSideProps = async ({ req, res, query, resolvedUrl }) => {
  return provideLoginProps(
    req,
    res,
    query,
    resolvedUrl,
    callbackPerpetualLogin,
  );
};

const StripeBillingPortalPage = ({ accountId }) => {
  const getBillingPortalSession = async () => {
    const body = new FormData();

    body.append('account_id', accountId);
    const stripePortalResponse = await ApiNoCache.clientGateway(
      `/stripe/billing-portal-session`,
      {
        body: body,
        method: 'POST',
      },
    );

    if (stripePortalResponse.data.session_url) {
      window.location = stripePortalResponse.data.session_url;
    }
  };

  getBillingPortalSession();

  return (
    <>
      <Seo allowIndexing={false} />
      <Loading padding="20px" />
    </>
  );
};

export default memo(StripeBillingPortalPage);
