import { memo } from 'react';
import { callbackPerpetualLogin } from '../../utils/authHelper';
import NonceLoginPage, { provideLoginProps } from './nonce';
import { isUserAgentBot } from '../../utils/isUserAgentBot';

export const getServerSideProps = async ({ req, res, query, resolvedUrl }) => {
  if (isUserAgentBot(req.headers['user-agent'])) {
    return {
      props: {
        isBot: true,
      },
    };
  }

  return provideLoginProps(
    req,
    res,
    query,
    resolvedUrl,
    callbackPerpetualLogin,
    'Perpetual',
  );
};

const PerpetualLoginPage = ({ isBot }) => <NonceLoginPage isBot={isBot} />;

export default memo(PerpetualLoginPage);
