import { memo } from 'react';
import {
  authenticate,
  callbackNonceLogin,
  checkResponseError,
  getRedirectUrl,
} from '../../utils/authHelper';
import { CookieService } from '../../services/cookieService';
import { xTracking } from '../../services/tracking';
import { Loading, Seo } from '../../components';
import * as Sentry from '@sentry/nextjs';
import { isUserAgentBot } from '../../utils/isUserAgentBot';

export const provideLoginProps = async (
  req,
  res,
  query,
  resolvedUrl,
  callback,
  title,
) => {
  const { identifier, key, goto } = query;
  const queryString = JSON.stringify(query);

  if (!identifier || !key) {
    Sentry.captureException(
      new Error(`${title} authentication missing params`),
      {
        tags: {
          feature: `${title} authentication`,
        },
        extra: {
          identifier,
          key,
          query: queryString,
        },
      },
    );

    CookieService.cleanAuthenticationCookies(req, res);

    return {
      redirect: {
        destination: `${process.env.baseUrl}/login`,
        permanent: false,
      },
    };
  }

  try {
    const authenticationResponse = await callback(identifier, key, req);
    const error = checkResponseError(authenticationResponse);

    if (error) {
      // Sentry.captureException(
      //   new Error(`${title} authentication request error`),
      //   {
      //     tags: {
      //       feature: `${title} authentication`,
      //     },
      //     extra: {
      //       identifier,
      //       key,
      //       query: queryString,
      //       errorMessage: authenticationResponse?.message || '',
      //     },
      //   },
      // );

      CookieService.cleanAuthenticationCookies(req, res);

      return {
        redirect: {
          destination: `${process.env.baseUrl}/login`,
          permanent: false,
        },
      };
    }

    const { isFullProfile, type, isError } =
      (await authenticate(
        authenticationResponse,
        req,
        res,
        resolvedUrl,
        title,
      )) || {};

    if (isError) {
      CookieService.cleanAuthenticationCookies(req, res);

      return {
        redirect: {
          destination: `${process.env.baseUrl}/login`,
          permanent: false,
        },
      };
    }

    const utm = [];

    for (const property in query) {
      if (property.startsWith('utm_')) {
        utm.push(`${property}=${query[property]}`);
      }
    }

    xTracking(query, req, res);

    let destination = getRedirectUrl(type, goto, null, isFullProfile);

    if (utm.length > 0) {
      const connector = destination.includes('?') ? '&' : '?';

      destination = `${destination}${connector}${utm.join('&')}`;
    }

    return {
      redirect: {
        destination: destination,
        permanent: false,
      },
    };
  } catch (error) {
    Sentry.captureException(
      new Error(`${title} authentication exception error`),
      {
        tags: {
          feature: `${title} authentication`,
        },
        extra: {
          identifier,
          key,
          query: queryString,
          error: JSON.stringify(error),
        },
      },
    );

    return {
      redirect: {
        destination: `${process.env.baseUrl}/login`,
        permanent: false,
      },
    };
  }
};

export const getServerSideProps = async ({ req, res, query, resolvedUrl }) => {
  if (isUserAgentBot(req.headers['user-agent'])) {
    return {
      props: {
        isBot: true,
      },
    };
  }

  return provideLoginProps(
    req,
    res,
    query,
    resolvedUrl,
    callbackNonceLogin,
    'Nonce',
  );
};

const NonceLoginPage = ({ isBot }) => (
  <>
    <Seo allowIndexing={false} />
    {isBot ? (
      <div>Bot detected. No action taken.</div>
    ) : (
      <Loading padding="20px" />
    )}
  </>
);

export default memo(NonceLoginPage);
