import { memo, useEffect } from 'react';
import { PageLayout } from '../../../components/Layouts';
import { useRouter } from 'next/router';
import styles from '../../../styles/reset.module.scss';
import { HeaderMobile, ResetPasswordForm } from '../../../components';
import { Amp } from '../../../services/amp';

const PasswordRecover = () => {
  const router = useRouter();
  const token = router.query.token;

  const navigate = async () => {
    await router.push('/login');
  };

  useEffect(() => {
    Amp.track(Amp.events.viewPasswordRecover);
  }, []);

  return (
    <PageLayout>
      <HeaderMobile marginOverride onClose={navigate}>
        <h1>Create your new password</h1>
      </HeaderMobile>
      <section className={styles['reset-section']}>
        <ResetPasswordForm token={token} />
      </section>
    </PageLayout>
  );
};

export default memo(PasswordRecover);
