import { memo, useEffect } from 'react';
import {
  authenticate,
  callbackTokenLogin,
  checkResponseError,
  getRedirectUrl,
} from '../../utils/authHelper';
import { CookieService } from '../../services/cookieService';
import { Loading, Seo } from '../../components';
import * as Sentry from '@sentry/nextjs';
import { Amp } from '../../services/amp';
import { useRouter } from 'next/router';

export const provideLoginProps = async (req, res, query, callback) => {
  const { account, series, token, initial, provider } = query;
  const queryString = JSON.stringify(query);

  if (!account || !series || !token) {
    Sentry.captureException(new Error('Social authentication missing params'), {
      tags: {
        feature: 'Social authentication',
      },
      extra: {
        account,
        series,
        token,
        query: queryString,
      },
    });

    CookieService.cleanAuthenticationCookies(req, res);

    return {
      redirect: {
        destination: `${process.env.baseUrl}/login`,
        permanent: false,
      },
    };
  }

  try {
    const authenticationResponse = await callback(account, series, token, req);
    const error = checkResponseError(authenticationResponse);

    if (error) {
      Sentry.captureException(
        new Error('Social authentication request error'),
        {
          tags: {
            feature: 'Social authentication',
          },
          extra: {
            account,
            series,
            token,
            query: queryString,
            errorMessage: authenticationResponse?.message || '',
          },
        },
      );

      CookieService.cleanAuthenticationCookies(req, res);

      return {
        redirect: {
          destination: `${process.env.baseUrl}/login`,
          permanent: false,
        },
      };
    }

    const { isFullProfile, type, isError } =
      (await authenticate(
        authenticationResponse,
        req,
        res,
        '/auth/social',
        'Social',
      )) || {};

    if (isError) {
      CookieService.cleanAuthenticationCookies(req, res);

      return {
        redirect: {
          destination: `${process.env.baseUrl}/login`,
          permanent: false,
        },
      };
    }

    let redirectPath = CookieService.getRedirectCookie(req, res);
    const isSignUp = Boolean(Number(initial));

    if (!redirectPath && type !== 'talent' && isSignUp) {
      redirectPath = '/welcome';
    }

    const destination = getRedirectUrl(type, null, redirectPath, isFullProfile);

    if (isSignUp) {
      return {
        props: {
          destination,
          isSignUp,
          type,
          provider,
        },
      };
    } else {
      return {
        redirect: {
          destination,
          permanent: false,
        },
      };
    }
  } catch (error) {
    Sentry.captureException(
      new Error('Social authentication exception error'),
      {
        tags: {
          feature: 'Social authentication',
        },
        extra: {
          account,
          series,
          token,
          query: queryString,
          error: JSON.stringify(error),
        },
      },
    );

    return {
      redirect: {
        destination: `${process.env.baseUrl}/login`,
        permanent: false,
      },
    };
  }
};

export const getServerSideProps = async ({ req, res, query }) => {
  return provideLoginProps(req, res, query, callbackTokenLogin);
};

const SocialLoginPage = ({ destination, isSignUp, type, provider }) => {
  const router = useRouter();

  useEffect(() => {
    const trackAmplitude = async () => {
      if (isSignUp) {
        try {
          await Amp.trackAsync(Amp.events.signUp, { type, provider });
        } catch (error) {
          console.error('Tracking error:', error);
        }
      }
    };

    trackAmplitude().then(() => {
      if (router.isReady) {
        router.replace(destination || '/');
      }
    });
  }, [router, destination, isSignUp, type, provider]);

  return (
    <>
      <Seo allowIndexing={false} />
      <Loading padding="20px" />
    </>
  );
};

export default memo(SocialLoginPage);
