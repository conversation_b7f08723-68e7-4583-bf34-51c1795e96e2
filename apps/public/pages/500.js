import React, { useEffect } from 'react';
import styles from '../styles/500.module.scss';
import Logo from '../public/assets/logo/logo-2.svg';
import Link from 'next/link';
import { MainLayout } from '../components/Layouts';
import { Amp } from '../services/amp';

export default function Custom500() {
  useEffect(() => {
    Amp.track(Amp.events.view500);
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
      isSaleHeaderVisible
      isMobileSubscribeHeaderVisible
    >
      <div className={styles['content']}>
        <div className={styles['cloud-bg']}>
          <div className={styles['cloud-bg-center']}></div>
        </div>
        <div className={styles['body']}>
          <div className={styles['main-block']}>
            <Link href="/" className={styles['logo-wrap']} passHref>
              <Logo className={styles['logo']} />
            </Link>
            <div className={styles['image-500']}></div>
            <h1 className={styles['title']}>
              <p>
                We apologize for the inconvenience, but an unexpected error has
                occurred on our server. Our technical team has been notified and
                is working to resolve the issue as quickly as possible.
              </p>
              <p>
                Please try again later or contact our support team for further
                assistance.
              </p>
            </h1>
          </div>
          <div className={styles['info-section']}>
            <div className={styles['info-box']}>
              <div className={styles['info-title']}>Talent Support</div>
              <div className={styles['info-text']}>
                <p>
                  <Link href="tel:18006058590">****************</Link>
                </p>
                <p>Monday-Friday 8am-5pm PST</p>
                <p>
                  <Link href="mailto:<EMAIL>">
                    <EMAIL>
                  </Link>
                </p>
              </div>
            </div>
            <div className={styles['info-box']}>
              <div className={styles['info-title']}>
                Casting Director Support
              </div>
              <div className={styles['info-text']}>
                <p>
                  <Link href="tel:18009135480">****************</Link>
                </p>
                <p>Monday-Friday 8am-5pm PST</p>
                <p>
                  <Link href="mailto:<EMAIL>">
                    <EMAIL>
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
