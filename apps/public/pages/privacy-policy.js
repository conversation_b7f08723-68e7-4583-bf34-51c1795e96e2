import { Seo, PrivacyPolicy as Policy } from '../components';
import { MainLayout, PageLayout } from '../components/Layouts';
import seoPageProps from '../utils/seoPageProps';
import { sharedServerSideProps } from '../utils/sharedServerSideProps';
import { useEffect } from 'react';
import { Amp } from '../services/amp';

export const getServerSideProps = async ({ resolvedUrl, req, res }) => {
  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      seoPage: await seoPageProps(resolvedUrl),
    },
  };
};

export default function PrivacyPolicy({ seoPage }) {
  useEffect(() => {
    Amp.track(Amp.events.viewPrivacyPolicy);
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
      isSaleHeaderVisible
      isMobileSubscribeHeaderVisible
    >
      <PageLayout>
        <Seo seoPage={seoPage} />
        <Policy scrollButton={false} />
      </PageLayout>
    </MainLayout>
  );
}
