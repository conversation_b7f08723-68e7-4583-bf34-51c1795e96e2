import Image from 'next/image';
import { Button, Input, Select, Seo } from '../components';
import styles from '../styles/invoice.module.scss';
import { sharedServerSideProps } from '../utils/sharedServerSideProps';
import ApiNoCache from '../services/apiNoCache';
import { getGatewayHeaders } from '../utils/headerHelper';
import { MainLayout } from '../components/Layouts';
import React from 'react';

export const getServerSideProps = async ({ resolvedUrl, req, res, query }) => {
  const { hash } = query;

  if (!hash) {
    return {
      redirect: {
        destination: `/`,
        permanent: false,
      },
    };
  }

  const invoice = (
    await ApiNoCache.serverGateway(
      `/invoices/${hash}?expand[]=level`,
      getGatewayHeaders(resolvedUrl),
      req,
      res,
    )
  ).data;

  if (!invoice?.links?.level?.recurring_interval_description) {
    return {
      redirect: {
        destination: `/`,
        permanent: false,
      },
    };
  }

  const sharedProps = await sharedServerSideProps(req, res);
  const periodData =
    invoice.links.level.recurring_interval_description.split(' ');

  return {
    props: {
      ...sharedProps,
      price: invoice.amount || 0,
      period: periodData.length > 0 ? Number(periodData[0]) : 0,
    },
  };
};

export default function InvoicePage({ price, period }) {
  return (
    <MainLayout isFooterVisible={false}>
      <Seo allowIndexing={false} />
      <div className={styles.container}>
        <form className={styles['payment-form']}>
          <div className={styles['payment-form-header']}>
            <div className={styles['payment-plan']}>
              <p className={styles['payment-plan-title']}>
                {`${period} month`}&nbsp;
                <br className={styles['payment-plan-title-break']} />
                <span>subscription</span>
              </p>
              <span className={styles['payment-plan-sub-title']}>
                Cancel any time
              </span>
            </div>
            <div className={styles['payment-price-container']}>
              <span className={styles['payment-price-with-discount']}>
                ${price}
              </span>
            </div>
          </div>
          <div className={styles['payment-form-content']}>
            <div className={styles['payment-information-container']}>
              <div className={styles['payment-information-title-container']}>
                <Image
                  src="/assets/icons/icon-lock.svg"
                  width={21}
                  height={24}
                  alt="icon lock"
                />
                <span className={styles['payment-information-title']}>
                  Payment information:
                </span>
              </div>
              <div className={styles['form-row']}>
                <Input name="nameOnCard" placeholder="Name on card" />
              </div>
              <div className={styles['form-row']}>
                <div className={styles['card-input-field']}>
                  <div className={styles['card-input-container']}>
                    <Input
                      className={styles.input}
                      name="creditCard"
                      placeholder="Card number"
                    />
                  </div>
                  <div className={styles['card-icons-outer-container']}>
                    <div className={styles['card-icons-container']}>
                      <div className={styles['card-icon-container']}>
                        <Image
                          className={styles['card-field-icon']}
                          src={'/assets/icons/icon-visa.svg'}
                          width={28}
                          height={18}
                          alt=""
                        />
                      </div>
                      <div className={styles['card-icon-container']}>
                        <Image
                          className={styles['card-field-icon']}
                          src={'/assets/icons/icon-mastercard.svg'}
                          width={28}
                          height={18}
                          alt=""
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className={styles['form-field']}>
                  <Input name="cvvCode" placeholder="CVV/CVC" />
                  <div className={styles['cvv-icon-container']}>
                    <Image
                      className={styles['cvv-icon']}
                      src={'/assets/icons/icon-cvv.svg'}
                      alt="icon cvv"
                      width={68}
                      height={20}
                    />
                  </div>
                </div>
              </div>
              <div className={styles['form-row']}>
                <div className={styles['form-inner-row']}>
                  <Select name="expMonth" placeholder="Month" options={[]} />
                  <Select name="expYear" placeholder="Year" options={[]} />
                </div>
                <div className={styles['form-field']}>
                  <Input name="zipCode" placeholder="Zip Code" maxLength={10} />
                </div>
              </div>
              <p className={styles['payment-description']}>
                Your subscription will <strong>automatically renew</strong> for
                the same price (<strong>${price}</strong>) and package length
                until you cancel via your Account Settings page. By subscribing,
                you <strong>authorize us</strong> to charge your{' '}
                <strong>card now</strong> and upon each <strong>renewal</strong>
                . <span className={styles.link}>Learn more</span>
              </p>
              <div className={styles['payment-form-footer']}>
                <p className={styles['payment-description']}>
                  By clicking{' '}
                  <span className={styles['payment-button-label']}>
                    Complete Subscription
                  </span>{' '}
                  I agree to the AllCasting.com{' '}
                  <span className={styles.link}>privacy policy</span> and{' '}
                  <span className={styles.link}>terms of use</span>
                </p>
                <div className={styles['payment-button-container']}>
                  <Button
                    label="Complete subscription"
                    color="orange"
                    minWidth="220px"
                    type="submit"
                  />
                </div>
                <div className={styles['payment-certificates-container']}>
                  <div className={styles['payment-certificate']}>
                    <Image
                      src="/assets/icons/icon-payment-secure.png"
                      width={44}
                      height={44}
                      alt="icon secure"
                    />
                    <span className={styles['payment-certificate-title']}>
                      Site is Secure
                    </span>
                  </div>
                  <div className={styles['payment-certificate']}>
                    <Image
                      src="/assets/icons/icon-payment-verified.png"
                      width={57}
                      height={44}
                      alt="icon verified"
                    />
                    <span className={styles['payment-certificate-title']}>
                      Merchant Services
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </MainLayout>
  );
}
