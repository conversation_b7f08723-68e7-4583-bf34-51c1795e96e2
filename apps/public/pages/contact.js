import { Seo } from '../components';
import { MainLayout, PageLayout } from '../components/Layouts';
import styles from '../styles/contact.module.scss';
import seoPageProps from '../utils/seoPageProps';
import { sharedServerSideProps } from '../utils/sharedServerSideProps';
import { useEffect } from 'react';
import { Amp } from '../services/amp';

export const getServerSideProps = async ({ resolvedUrl, req, res }) => {
  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      seoPage: await seoPageProps(resolvedUrl),
    },
  };
};

export default function Contact({ seoPage }) {
  useEffect(() => {
    Amp.track(Amp.events.viewContactUs);
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
      isSaleHeaderVisible
      isMobileSubscribeHeaderVisible
    >
      <PageLayout>
        <section className={styles['contact-section']}>
          <Seo seoPage={seoPage} />
          <h2>Mailing Address</h2>
          <div className={styles.content}>
            <p>AllCasting 300 Delaware Ave, #210</p>
            <p>Wilmington, DE 19801</p>
          </div>

          <h2>Customer Support</h2>
          <div className={styles.content}>
            <a href="tel:+18006058590">****************</a>
            <p>Monday-Friday 8am-5pm PST</p>
            <a href="mailto:<EMAIL>"><EMAIL></a>
          </div>
        </section>
      </PageLayout>
    </MainLayout>
  );
}
