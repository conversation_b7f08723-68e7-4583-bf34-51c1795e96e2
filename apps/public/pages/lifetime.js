import styles from '../styles/lifetime.module.scss';
import cn from 'classnames';
import Image from 'next/image';
import { MainLayout } from '../components/Layouts';
import { LifetimeButton, Seo, TermsOfUseToggle } from '../components';
import seoPageProps from '../utils/seoPageProps';
import { sharedServerSideProps } from '../utils/sharedServerSideProps';
import { useEffect } from 'react';
import { Amp } from '../services/amp';

export const getServerSideProps = async ({ resolvedUrl, req, res }) => {
  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      seoPage: await seoPageProps(resolvedUrl),
    },
  };
};

const LifetimePage = ({ seoPage }) => {
  useEffect(() => {
    Amp.track(Amp.events.viewLifetime);
  }, []);

  return (
    <MainLayout isPremiumHeaderVisible>
      <Seo
        seoPage={seoPage}
        overrideTitle="Lifetime Access to Casting Calls & Job Opportunities | allcasting"
        overrideDescription="Secure your future in the entertainment industry with allcasting's lifetime subscription. Access unlimited casting calls anytime, anywhere - no hidden costs, no extra fees."
      />
      <main>
        <article className={cn(styles.banner, styles.container)}>
          <section className={styles['content']}>
            <h1>
              Unlock a <span className={styles.lifetime}>Lifetime</span> of
              Opportunities{' '}
              <span style={{ whiteSpace: 'nowrap' }}>with allcasting</span>
            </h1>
            <p>Only</p>
            <p className={styles.price}>
              <sup>$</sup>198
            </p>
            <LifetimeButton
              className={styles['banner-button']}
              color="purple"
              label="Get Lifetime Access Now!"
              minWidth="220px"
            />
            <p className={styles.disclaimer}>
              This is a one-time payment.
              <br />
              By proceeding, you have read and agreed to the{' '}
              <TermsOfUseToggle>Terms of Use.</TermsOfUseToggle>
            </p>
          </section>
        </article>
        <div className={styles.container}>
          <div className={cn(styles['features-content'], styles.content)}>
            <p className={styles.text}>
              Invest in your career with a Lifetime subscription, and watch as
              doors open to a world of possibilities in the entertainment
              industry. Just one payment for lifetime access — no hidden costs,
              no extra fees.
            </p>

            <div className={styles['benefit-left']}>
              <div className={styles['benefit-image']}>
                <Image
                  src={'/assets/lifetime/boost-pic.webp'}
                  height={400}
                  width={400}
                  alt="A Lifetime of job opportunities"
                  priority
                />
              </div>
              <div className={styles['benefit-description']}>
                <h3>A Lifetime of job opportunities</h3>
                <p>
                  Take control of your career and access casting calls anytime,
                  anywhere.
                </p>
              </div>
            </div>

            <div className={styles['benefit-right']}>
              <div className={styles['benefit-image']}>
                <Image
                  src="/assets/lifetime/story-pic.webp"
                  alt="A Lifetime of savings"
                  height={400}
                  width={400}
                  priority
                />
              </div>
              <div className={styles['benefit-description']}>
                <h3>A Lifetime of savings</h3>
                <p>
                  Forget renewals and fees! Our Lifetime subscription is a
                  one-time investment for lasting peace of mind.
                </p>
              </div>
            </div>

            <div className={styles['benefit-left']}>
              <div className={styles['benefit-image']}>
                <Image
                  src="/assets/lifetime/mailng-pic.webp"
                  height={400}
                  width={400}
                  alt="A Lifetime of growth"
                  priority
                />
              </div>
              <div className={styles['benefit-description']}>
                <h3>A Lifetime of growth</h3>
                <p>
                  Enjoy uninterrupted access to casting calls and resources that
                  empower you to develop your craft and grow your portfolio in
                  the entertainment industry.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className={cn(styles['cta-block'], styles.container)}>
          <div className={styles.content}>
            <h2>Try Lifetime now</h2>
            <LifetimeButton
              className={styles['cta-button']}
              color="white"
              label="Get Started"
              minWidth="220px"
            />
            <p className={styles.disclaimer}>
              This is a one-time payment.
              <br />
              By proceeding, you have read and agreed to the{' '}
              <TermsOfUseToggle>Terms of Use.</TermsOfUseToggle>
            </p>
          </div>
        </div>
      </main>
    </MainLayout>
  );
};

export default LifetimePage;
