import Head from 'next/head';
import * as Sentry from '@sentry/nextjs';
import Api from '../../../services/api';
import { Article } from '../../../components';
import seoPageProps from '../../../utils/seoPageProps';
import { formatFilters } from '../../../utils/formatFilters';
import {
  detectComponents,
  parseContent,
} from '../../../services/pageConstructor';
import {
  ARTICLE_DEFAULT_REDIRECT_URL,
  ARTICLE_STATUS,
} from '../../../constants/articles';
import { getGatewayHeaders, getHeaders } from '../../../utils/headerHelper';
import { param } from '../../../utils/sharedServerSideProps';
import {
  fetchArticle,
  fetchArticles,
} from '../../../services/endpoints/articles';

import styles from '../../../styles/academy.module.scss';
import { MainLayout, PageLayout } from '../../../components/Layouts';
import ArticlePreview from '../../../components/Article/ArticlePreview';
import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { Amp } from '../../../services/amp';

/**
 * @param {import('next').GetServerSidePropsContext} context
 */
export const getServerSideProps = async (context) => {
  const category = param(context.params?.category);

  if (!category) {
    return { notFound: true };
  }

  return processArticleServerSideProps(context, category);
};

/**
 * @param {import('next').GetServerSidePropsContext} context
 * @param {string} category
 */
export const processArticleServerSideProps = async (
  { req, params, resolvedUrl },
  category,
) => {
  const slug = param(params?.slug);

  if (!slug) {
    return { notFound: true };
  }

  const [article, articlesResponse] = await Promise.all([
    fetchArticle(slug),
    category === 'info' ? null : fetchArticles({ limit: 3 }),
  ]);

  if (article.status === 'error') {
    return { notFound: true };
  }
  if (category === 'info' && resolvedUrl.startsWith('/blog/')) {
    return { notFound: true };
  }
  if (article.categories[0].slug !== category && category !== 'info') {
    return {
      redirect: {
        destination: `/blog/${article.categories[0].slug}/${slug}`,
        permanent: true,
      },
    };
  }

  if (article.articleStatus === ARTICLE_STATUS.Deleted) {
    return {
      redirect: {
        destination: article.redirectUrl || ARTICLE_DEFAULT_REDIRECT_URL,
        permanent: true,
      },
    };
  }

  const detectedComponents = detectComponents(article.content, resolvedUrl);

  let pageConstructorData = [];

  pageConstructorData = await Promise.all(
    detectedComponents.map(async (comp) => {
      switch (comp.name) {
        case 'article':
          return await fetchArticle(comp.props.slug);
        case 'reviews':
          return await Api.serverAPIRoute(
            `/reviews/testimonials?page=1&limit=${comp.props.count}`,
            null,
            getHeaders(req, resolvedUrl),
          );
        case 'castingcalls':
          /**
           * @type {Record<string, string>}
           */
          const params = {
            static: comp.props.query?.split('?')[0] || '',
          };

          /**
           * @type {string}
           */
          const queryString = comp.props.query?.split('?')[1] || '';
          const query = queryString?.replaceAll('amp;', '').split('&') || [];

          query.forEach((item) => {
            const [key, value] = item.split('=');

            params[key] = value;
          });

          const filter = JSON.stringify(params);
          const reqQuery = {
            query: {
              filter: filter,
            },
          };
          const formattedFilters = await formatFilters(
            false,
            reqQuery,
            req,
            resolvedUrl,
          );

          const ccData = {
            response: await Api.serverGateway(
              `/calls/search${formattedFilters.filterParameters}&limit=${comp.props.count}`,
              { ...req.headers, ...getGatewayHeaders(resolvedUrl) },
            ),
            filterParams: formattedFilters.filterParameters,
          };

          return await ccData;
        default:
          return {};
      }
    }),
  );

  const recommendedArticles =
    (articlesResponse?.status === 'ok' && articlesResponse.items) || [];

  return {
    props: {
      article,
      pageConstructorData,
      seoPage: await seoPageProps(resolvedUrl),
      resolvedUrl,
      recommendedArticles,
      category,
    },
  };
};

/**
 * @param {import('next').InferGetServerSidePropsType<typeof getServerSideProps>} props
 */
export const ArticlePage = (props) => {
  const {
    article,
    pageConstructorData,
    seoPage,
    resolvedUrl,
    recommendedArticles,
    category,
  } = props;
  const router = useRouter();

  useEffect(() => {
    if (article) {
      Amp.track(Amp.events.viewBlogArticle, {
        article_id: article.id,
        category,
      });
    }
  }, [article, category]);

  useEffect(() => {
    // ToDo: check out
    // nextjs redirect and Link bug workaround
    if (!article) {
      Sentry.captureException(new Error('Old academy links issue'));
      router.push('/blog');
    }
  }, [article]);

  if (!article) {
    // nextjs redirect and Link bug workaround
    return (
      <MainLayout
        isDefaultHeaderVisible
        isMobileMenuVisible
        isUserMenuVisible
        isSaleHeaderVisible
        isMobileSubscribeHeaderVisible
      >
        <PageLayout>
          <h1>Loading...</h1>
        </PageLayout>
      </MainLayout>
    );
  }

  const [parsedContent, headingImageRemoved] = parseContent(
    article.content,
    pageConstructorData,
    resolvedUrl,
  );
  const meta = {
    title: article.title,
    meta: article.meta,
    time: article.time,
    author: article.author?.name || 'Creator',
  };

  const crumbs =
    category === 'info' ? [] : [{ text: 'Allcasting Blog', href: '/blog' }];
  const seo = {
    ...seoPage,
    ogImageUrl: article.headlineImage?.reference.href,
    ogImageWidth: article.headlineImage?.reference.width,
    ogImageHeight: article.headlineImage?.reference.height,
  };

  return (
    <MainLayout
      isDefaultHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
      isSaleHeaderVisible
      isMobileSubscribeHeaderVisible
    >
      <PageLayout>
        <Head>
          {/* eslint-disable-next-line @next/next/no-css-tags */}
          <link rel="stylesheet" href="/styles/article-overrides.css" />
        </Head>
        <Article
          seoPage={seo}
          crumbs={crumbs}
          meta={meta}
          content={parsedContent}
          showCreationInfo={category !== 'info'}
          showHeadingImage={headingImageRemoved}
          article={article}
        />
      </PageLayout>
      {recommendedArticles.length > 0 && (
        <div className={styles['learn-more']}>
          <div className={styles['learn-more-content']}>
            <h2 className={styles['learn-more-title']}>Learn More</h2>
            <div className={styles.suggested}>
              {recommendedArticles.map((item, key) => (
                <ArticlePreview key={key} article={item} />
              ))}
            </div>
          </div>
        </div>
      )}
    </MainLayout>
  );
};

export default ArticlePage;
