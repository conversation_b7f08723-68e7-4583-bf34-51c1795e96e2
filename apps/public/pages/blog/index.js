import { ArticleList } from '../../components';
import seoPageProps from '../../utils/seoPageProps';
import { fetchArticles } from '../../services/endpoints/articles';
import { getPageParam } from '../../utils/sharedServerSideProps';
import { MainLayout, PageLayout } from '../../components/Layouts';
import { CookieService } from '../../services/cookieService';
import { getPromotions } from '../../services/endpoints/promotions';
import { ARTICLE_PAGE_LIMIT } from '../../constants/articles';
import { useEffect } from 'react';
import { Amp } from '../../services/amp';

export async function getBlogProps({ req, res, query, resolvedUrl }, category) {
  const pageParam = getPageParam(query);
  const destination = category ? `/blog/${category}` : `/blog`;

  if (pageParam === 0 || pageParam === 1) {
    return { redirect: { destination, permanent: true } };
  }

  const page = pageParam || 1;
  const limit = ARTICLE_PAGE_LIMIT;
  const isUserGuest = !CookieService.getAccountCookie(req, res);
  const isSaleHidden = !CookieService.getSaleCookie(req, res);
  const isAgent = CookieService.getUserTypeCookie(req, res) === 'agent';

  const promotionsPromise =
    (isUserGuest && getPromotions({ targets: ['guest'] })) ||
    // ToDo re-add when agent promotions are ready
    // (isAgent && getPromotions({ targets: ['agent'] })) ||
    null;

  const [listArticlesResponse, seoPage, promosResponse] = await Promise.all([
    fetchArticles({ page, limit, category }),
    seoPageProps(resolvedUrl),
    promotionsPromise,
  ]);

  if (listArticlesResponse.status === 'error') {
    return { notFound: true };
  }

  const maxPage = Math.ceil(listArticlesResponse.total / limit);

  if (page > maxPage && page > 1) {
    const url = `${destination}${maxPage > 1 ? `?page=${maxPage}` : ''}`;

    return { redirect: { destination: url } };
  }

  const promos = promosResponse?.data || [];

  return {
    props: {
      list: listArticlesResponse,
      seoPage,
      page,
      isUserGuest,
      isAgent,
      isSaleHidden,
      promos,
      category: category || '',
    },
  };
}

export const getServerSideProps = async (context) => {
  return getBlogProps(context);
};

const BlogIndex = ({
  list,
  seoPage,
  category,
  page,
  isUserGuest,
  isAgent,
  isSaleHidden,
  promos,
}) => {
  useEffect(() => {
    Amp.track(Amp.events.viewBlog, {
      page,
      category,
    });
  }, [page, category]);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isUserMenuVisible
      isSaleHeaderVisible
      isMobileSubscribeHeaderVisible
      isMobileMenuVisible
    >
      <PageLayout>
        <ArticleList
          seoPage={{
            ...seoPage,
            ogImageUrl: `${process.env.baseUrl}/assets/meta/blog.webp`,
          }}
          list={list}
          page={page}
          category={category}
          promos={promos}
          isSaleHidden={isSaleHidden}
          isUserGuest={isUserGuest}
          isAgent={isAgent}
        />
      </PageLayout>
    </MainLayout>
  );
};

export default BlogIndex;
