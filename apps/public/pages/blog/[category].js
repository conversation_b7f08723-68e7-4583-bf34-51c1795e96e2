import BlogIndex, { getBlogProps } from '.';

export const getServerSideProps = async (context) => {
  const category = context.params?.category;

  if (!category) {
    return { notFound: true };
  }

  return getBlogProps(context, category);
};

const BlogCategoryPage = ({
  list,
  seoPage,
  category,
  page,
  isUserGuest,
  isAgent,
  isSaleHidden,
  promos,
}) => {
  return (
    <BlogIndex
      seoPage={seoPage}
      list={list}
      page={page}
      category={category}
      isUserGuest={isUserGuest}
      isAgent={isAgent}
      isSaleHidden={isSaleHidden}
      promos={promos}
    />
  );
};

export default BlogCategoryPage;
