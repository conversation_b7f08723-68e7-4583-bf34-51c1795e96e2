import React, { memo } from 'react';
import CastingCallsSearchPage, { provideProps } from './index';

export async function getServerSideProps({ req, query, resolvedUrl, res }) {
  return provideProps(req, query, resolvedUrl, res);
}

const CastingCallsSearchPageWithStaticUrl = ({
  defaultFilters,
  selectedFilters,
  promos,
  title,
  seo,
  seoPage,
  seoCrossLinking,
  cities,
  castingCalls,
  activeCastingCallsTotal,
  activeRolesTotal,
  announcement,
  account,
  isTalent,
  page,
  experiments,
  registrationDate,
}) => {
  return (
    <CastingCallsSearchPage
      defaultFilters={defaultFilters}
      selectedFilters={selectedFilters}
      promos={promos}
      title={title}
      seo={seo}
      seoPage={seoPage}
      seoSuffix={page > 1 ? ` - page ${page}` : ``}
      seoCrossLinking={seoCrossLinking}
      cities={cities}
      castingCalls={castingCalls}
      activeCastingCallsTotal={activeCastingCallsTotal}
      activeRolesTotal={activeRolesTotal}
      account={account}
      announcement={announcement}
      isTalent={isTalent}
      showSeoLinking={page === 1}
      experiments={experiments}
      registrationDate={registrationDate}
      page={page}
    />
  );
};

export default memo(CastingCallsSearchPageWithStaticUrl);
