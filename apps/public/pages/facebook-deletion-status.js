import { MainLayout, PageLayout } from '../components/Layouts';
import styles from '../styles/facebook-deletion-status.module.scss';
import ApiNoCache from '../services/apiNoCache';
import { Seo } from '../components';
import { isUserAgentBot } from '../utils/isUserAgentBot';
import { useEffect } from 'react';
import { Amp } from '../services/amp';

export const getServerSideProps = async ({ req, res, query }) => {
  if (isUserAgentBot(req.headers['user-agent'])) {
    return {
      props: {
        isBot: true,
      },
    };
  }

  const { id, code } = query;

  if (!id) {
    return {
      redirect: {
        destination: '/',
        permanent: false,
      },
    };
  }

  const { deletion_status } =
    (
      await ApiNoCache.serverGateway(
        `/auth/social/facebook/${id}/status?code=${code}`,
        {
          method: 'GET',
        },
        req,
        res,
      )
    )?.data || {};

  return {
    props: {
      isComplete: deletion_status === 'complete',
    },
  };
};
export default function FacebookDeletionStatus({ isComplete, isBot }) {
  useEffect(() => {
    if (!isBot) {
      Amp.track(Amp.events.viewFacebookDeletionStatus);
    }
  }, [isBot]);

  if (isBot) {
    return (
      <div>
        <Seo allowIndexing={false} />
        Bot detected. No action taken.
      </div>
    );
  }

  return (
    <MainLayout
      isDefaultHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
      isSaleHeaderVisible
      isMobileSubscribeHeaderVisible
    >
      <Seo allowIndexing={false} />
      <PageLayout>
        <section className={styles['facebook-deletion-status-section']}>
          <div className={styles['facebook-deletion-status-section-header']}>
            <span className={styles['facebook-deletion-status-section-title']}>
              Facebook Data Deletion Status
            </span>
            {isComplete ? (
              <>
                <span
                  className={
                    styles['facebook-deletion-status-section-sub-title']
                  }
                >
                  Data deletion complete
                </span>
                <p>
                  Your data has been successfully deleted. All personal
                  information associated with your Facebook account and this app
                  has been removed from our systems.
                </p>
              </>
            ) : (
              <>
                <span
                  className={
                    styles['facebook-deletion-status-section-sub-title']
                  }
                >
                  Data deletion in progress
                </span>
                <p>
                  Your data deletion request has been received, but could not be
                  completed automatically. Our team has been notified and will
                  manually process your request as soon as possible.
                </p>
              </>
            )}
          </div>
        </section>
      </PageLayout>
    </MainLayout>
  );
}
