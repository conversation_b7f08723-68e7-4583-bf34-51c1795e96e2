import { MainLayout, PageLayout } from '../components/Layouts';
import { Seo } from '../components';
import styles from '../styles/refund-policy.module.scss';
import seoPageProps from '../utils/seoPageProps';
import Link from 'next/link';
import { sharedServerSideProps } from '../utils/sharedServerSideProps';
import { useEffect } from 'react';
import { Amp } from '../services/amp';

export const getServerSideProps = async ({ resolvedUrl, req, res }) => {
  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      seoPage: await seoPageProps(resolvedUrl),
    },
  };
};

export default function RefundPolicy({ seoPage }) {
  useEffect(() => {
    Amp.track(Amp.events.viewRefundPolicy);
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
      isSaleHeaderVisible
      isMobileSubscribeHeaderVisible
    >
      <PageLayout>
        <div className={styles.title}>
          <Seo seoPage={seoPage} />
        </div>
        <p>
          We understand entertainment industry is not for everyone and provide
          all new subscribers with a 10 days right to cancel the subscription
          for a full refund.
        </p>
        <h2 className={styles['sub-title']}>
          How to get a full refund for your subscription on allcasting.com?
        </h2>
        <p>
          To request a refund please email / mail refund request which states
          that you, the buyer, are canceling this agreement and would like a
          refund, or words of similar effect. This notice shall be sent to:{' '}
          <Link href="mailto:<EMAIL>" className={styles.link}>
            <EMAIL>
          </Link>{' '}
          or{' '}
          <Link href="https://allCasting.com" className={styles.link}>
            allCasting.com
          </Link>
          , 150 Post St, Suite 450, San Francisco, CA 94108. Please include your
          allCasting.com username and email address or your refund may be
          delayed. If you send or deliver the notice to cancel your subscription
          agreement within such a ten day period, we will refund the full amount
          of your subscription.
        </p>
        <h2>
          How long does it takes to get a refund for a subscription on
          allcasting.com?
        </h2>
        <p>
          Typically it takes 1-2 business days for the refund to post on your
          account.
        </p>
      </PageLayout>
    </MainLayout>
  );
}
