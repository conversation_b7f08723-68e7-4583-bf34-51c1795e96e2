import React, { useEffect } from 'react';
import styles from '../styles/404.module.scss';
import { Button } from '../components';
import cn from 'classnames';
import Link from 'next/link';
import { MainLayout } from '../components/Layouts';
import Image from 'next/image';
import { Amp } from '../services/amp';

export default function ErrorPage() {
  useEffect(() => {
    Amp.track(Amp.events.view404);
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
      isSaleHeaderVisible
      isMobileSubscribeHeaderVisible
    >
      <div className={styles['content']}>
        <header className={styles['header']}>
          <Link href="/" passHref className={styles['logo-link']}>
            <Image
              className={styles.logo}
              src="/assets/logo/logo-4.svg"
              alt="logo"
              width={93}
              height={21}
            />
          </Link>
        </header>
        <div className={styles['main-content']}>
          <section className={styles['main-block']}>
            <h1 className={styles['title']}>oops...</h1>
            <div className={styles['subtitle']}>Something went wrong</div>
            <div className={styles['main-text']}>Let&apos;s try again</div>
            <Link href="/">
              <Button
                type="button"
                kind="primary"
                color="blue"
                label="go to homepage"
                minWidth={'220px'}
                shadow={false}
                className={styles['back-btn']}
              />
            </Link>
            <div className={cn(styles['main-text'], styles['text-or'])}>
              or...
            </div>
          </section>
        </div>

        <div className={styles['body']}>
          <div className={styles['to-do-section']}>
            <div className={styles['to-do-box']}>
              <div className={styles['to-do-title']}>Explore Casting Calls</div>
              <div className={styles['to-do-text']}>
                Browse through our diverse base of casting calls and apply now
                to land an audition.
              </div>
              <Link href="/castingcalls">
                <Button
                  type="button"
                  kind="secondary"
                  color="blue"
                  label="Apply for Casting Calls"
                  shadow={false}
                  className={styles['to-do-btn']}
                />
              </Link>
            </div>
            <div className={styles['to-do-box']}>
              <div className={styles['to-do-title']}>Read Reviews</div>
              <div className={styles['to-do-text']}>
                Learn about our talent&apos;s success with allcasting and get
                inspired.
              </div>
              <Link href="/reviews">
                <Button
                  type="button"
                  kind="secondary"
                  color="blue"
                  label="Read Now"
                  shadow={false}
                  className={styles['to-do-btn']}
                />
              </Link>
            </div>
            <div className={styles['to-do-box']}>
              <div className={styles['to-do-title']}>Learn at Blog</div>
              <div className={styles['to-do-text']}>
                Our blog provides industry knowledge by professionals and latest
                news.
              </div>
              <Link href="/blog">
                <Button
                  type="button"
                  kind="secondary"
                  color="blue"
                  label="Learn More"
                  shadow={false}
                  className={styles['to-do-btn']}
                />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
