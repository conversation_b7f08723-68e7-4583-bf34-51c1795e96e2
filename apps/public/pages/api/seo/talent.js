export default function handler(req, res) {
  res.status(200).json({
    rules: {
      name: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'name',
        default: '',
      },
      country: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'country',
        default: '',
      },
      city: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'city',
        default: '',
      },
      longitude: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'longitude',
        default: '',
      },
      latitude: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'latitude',
        default: '',
      },
      location: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'location',
        default: '',
      },
      zipcode: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'zipcode',
        default: '',
      },
      radius: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'radius',
        default: '200',
      },
      gender: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'gender',
        default: 'bothgenders',
      },
      agemin: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'agemin',
        default: '18',
      },
      agemax: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'agemax',
        default: '150',
      },
      category: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'category',
        default: '',
      },
      ethnicity: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'ethnicity',
        default: '',
      },
      page: {
        type: 'static',
        prefix: '-page-',
        order: 3,
        values: [],
        slug: 'page',
        default: 1,
      },
    },
  });
}
