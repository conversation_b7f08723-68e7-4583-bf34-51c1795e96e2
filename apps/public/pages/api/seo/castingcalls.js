import { CITIES } from '../../../constants/siteMap';

export default function handler(req, res) {
  res.status(200).json({
    rules: {
      keyword: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'keyword',
        default: '',
      },
      near: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'near',
        default: '',
      },
      best: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'best',
        default: false,
      },
      viewed: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'viewed',
        default: '',
      },
      country: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'country',
        default: '',
      },
      city: {
        type: 'static',
        prefix: '-in-',
        order: 2,
        supported: ['', ...CITIES.map(({ key }) => key)],
        values: [],
        slug: 'city',
        default: '',
      },
      longitude: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'longitude',
        default: '',
      },
      latitude: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'latitude',
        default: '',
      },
      location: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'location',
        default: '',
      },
      zipcode: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'zipcode',
        default: '',
      },
      radius: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'radius',
        default: '200',
      },
      gender: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'gender',
        default: 'bothgenders',
      },
      agemin: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'agemin',
        default: '18',
      },
      agemax: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'agemax',
        default: '150',
      },
      category: {
        type: 'static',
        prefix: '',
        order: 1,
        values: [],
        slug: 'category',
        default: '',
        supported: [
          '',
          'acting',
          'modeling',
          'realitytv',
          'theater',
          'musicvideos',
          'singing',
          'dancing',
          'promotionalmodels',
          'voiceover',
          'extras',
          'commercials',
          'contentcreators',
          'influencers',
        ],
      },
      ethnicity: {
        type: 'query',
        prefix: '',
        order: 0,
        values: [],
        slug: 'ethnicity',
        default: '',
      },
      page: {
        type: 'static',
        prefix: '-page-',
        order: 3,
        values: [],
        slug: 'page',
        default: 1,
      },
    },
  });
}
