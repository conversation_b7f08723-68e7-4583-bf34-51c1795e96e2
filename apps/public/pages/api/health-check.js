export default async function handler(req, res) {
  const token = req.query.token;
  const healthCheckToken = process.env.HEALTH_CHECK_TOKEN;

  if (!healthCheckToken) {
    return res.status(500).json({
      status: 'error',
      reason: 'server_misconfigured',
    });
  }

  if (!token) {
    return res.status(400).json({
      status: 'error',
      reason: 'missing_secret',
    });
  }

  if (token !== healthCheckToken) {
    return res.status(403).json({
      status: 'error',
      reason: 'forbidden',
    });
  }

  return res.status(200).json({ status: 'ok' });
}
