import ApiNoCache from '../../../services/apiNoCache';

export default async function handler(req, res) {
  const { email, password, type, firstName, lastName, zip, phoneNumber } =
    JSON.parse(req.body);
  const body = new FormData();

  body.append('email', email);
  body.append('password', password);
  body.append('type', type);
  body.append('optout_advertising', 0);

  if (firstName) {
    body.append('firstname', firstName);
  }

  if (lastName) {
    body.append('lastname', lastName);
  }

  if (zip) {
    body.append('zip', zip);
  }

  if (phoneNumber) {
    body.append('phone', phoneNumber);
  }

  const response = await ApiNoCache.serverGateway(
    `/auth/register`,
    {
      method: 'POST',
      body,
    },
    req,
    res,
  );

  res.status(200).json(response.data);
}
