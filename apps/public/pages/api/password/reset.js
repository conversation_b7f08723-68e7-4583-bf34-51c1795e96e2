import ApiNoCache from '../../../services/apiNoCache';

export default async function handler(req, res) {
  const { token, password } = JSON.parse(req.body);
  const body = new FormData();

  body.append('token', token);
  body.append('password', password);

  const response = await ApiNoCache.serverGateway(
    `/auth/reset`,
    {
      method: 'POST',
      body,
    },
    req,
    res,
  );

  res.status(200).json(response.data);
}
