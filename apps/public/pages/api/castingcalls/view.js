// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import ApiNoCache from '../../../services/apiNoCache';

export default async function handler(req, res) {
  const body = new FormData();

  const response = await ApiNoCache.serverGateway(
    `/casting-calls/${req.query.slug}/checked`,
    {
      method: 'POST',
      body,
    },
    req,
    res,
  );

  res.status(200).json(response.data);
}
