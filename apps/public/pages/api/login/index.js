import ApiNoCache from '../../../services/apiNoCache';
import { CookieService } from '../../../services/cookieService';
import { extractExpiresFromAuthenticationToken } from '../../../utils/authHelper';

const expandableResources = ['level', 'settings', 'manager', 'profiles'];

export default async function handler(req, res) {
  const { email, password } = JSON.parse(req.body);
  const body = new FormData();

  body.append('email', email);
  body.append('password', password);
  body.append('expand', expandableResources.join(','));

  const response = await ApiNoCache.serverGateway(
    `/auth/login?expand=profiles,location,manager`,
    {
      method: 'POST',
      body,
    },
    req,
    res,
  );

  const authenticationToken = response.headers.get(
    CookieService.cookie.authentication,
  );
  const volatileToken = response.headers.get(CookieService.cookie.volatile);
  const tokenExpires =
    extractExpiresFromAuthenticationToken(authenticationToken);
  const data = response.data;

  data.authenticationToken = authenticationToken;
  data.volatileToken = volatileToken;
  data.tokenExpires = tokenExpires;
  data.userType = res.status(200).json({ ...data });
}
