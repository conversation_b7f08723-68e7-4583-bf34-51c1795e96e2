import { useEffect } from 'react';
import { LoginF<PERSON>, Seo } from '../components';
import { MainLayout, PageLayout } from '../components/Layouts';
import { useNotifications } from '../contexts/NotificationContext';
import styles from '../styles/login.module.scss';
import seoPageProps from '../utils/seoPageProps';
import { sharedServerSideProps } from '../utils/sharedServerSideProps';
import { Amp } from '../services/amp';

export const getServerSideProps = async ({ resolvedUrl, req, res }) => {
  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      seoPage: await seoPageProps(resolvedUrl),
    },
  };
};

export default function Login({ seoPage }) {
  const { setNotification } = useNotifications();

  useEffect(() => {
    if (!navigator.cookieEnabled) {
      setNotification({
        type: 'error',
        message: 'Please enable Cookies to use full website functionality',
      });
    }

    Amp.track(Amp.events.viewLogin);
  }, []);

  return (
    <MainLayout isDefaultHeaderVisible isMobileMenuVisible>
      <PageLayout>
        <section className={styles['login-section']}>
          <LoginForm>
            <Seo seoPage={seoPage} />
          </LoginForm>
        </section>
      </PageLayout>
    </MainLayout>
  );
}
