import { MainLayout } from '../components/Layouts';
import styles from '../styles/stop-email-submissions.module.scss';
import Link from 'next/link';
import { sharedServerSideProps } from '../utils/sharedServerSideProps';
import { ErrorMessage } from '../constants/form';
import ApiNoCache from '../services/apiNoCache';
import { getGatewayHeaders } from '../utils/headerHelper';
import { Button, Seo } from '../components';
import { isUserAgentBot } from '../utils/isUserAgentBot';
import { useEffect } from 'react';
import { Amp } from '../services/amp';

export const getServerSideProps = async ({ req, res, query, resolvedUrl }) => {
  if (isUserAgentBot(req.headers['user-agent'])) {
    return {
      props: {
        isBot: true,
      },
    };
  }

  const { token, cc_id } = query;

  if (!token || !cc_id) {
    return {
      redirect: {
        destination: '/',
        permanent: false,
      },
    };
  }

  const unsubscribeResponse =
    (
      await ApiNoCache.serverGateway(
        `/casting-calls/${cc_id}/stop-email-submissions`,
        {
          method: 'PATCH',
          body: new URLSearchParams({ token, stop_email_submissions: '1' }),
          ...getGatewayHeaders(resolvedUrl),
        },
        req,
        res,
      )
    )?.data || {};

  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      isError: unsubscribeResponse.status !== 'ok',
      castingCall:
        (
          await ApiNoCache.serverGateway(
            `/calls/get?id=${cc_id}`,
            getGatewayHeaders(resolvedUrl),
            req,
            res,
          )
        )?.data?.data || {},
    },
  };
};
export default function StopEmailSubmissionsPage({
  isError,
  castingCall: { title, agent_id },
  isBot,
}) {
  useEffect(() => {
    if (!isBot) {
      Amp.track(Amp.events.viewStopEmailSubmissions);
    }
  }, [isBot]);

  if (isBot) {
    return (
      <div>
        <Seo allowIndexing={false} />
        Bot detected. No action taken.
      </div>
    );
  }

  return (
    <MainLayout isLogoHeaderVisible isFooterVisible={false}>
      <Seo allowIndexing={false} />
      <section className={styles.section}>
        {isError ? (
          <div className={styles['error-container']}>
            <span className={styles['error-title']}>
              {ErrorMessage.Unexpected}
            </span>
            <span className={styles['error-description']}>
              To stop receiving email submissions for this casting call{' '}
              <Link className={styles.link} href="/contact">
                contact support
              </Link>
            </span>
          </div>
        ) : (
          <div className={styles.container}>
            <div className={styles.title}>
              <span>
                You have successfully stopped emails for the casting call
              </span>
              {title && <span>&quot;{title}&quot;</span>}
            </div>
            <p className={styles.description}>
              <span>
                If there&apos;s anything we can do to make your experience
                better, please let us know.
              </span>
              <span>
                Would you be willing to share why you chose to stop applications
                for your casting call?
              </span>
              <span>
                Your feedback will help us improve our platform for everyone.
              </span>
            </p>
            <Button
              type="link"
              href="https://docs.google.com/forms/d/e/1FAIpQLScFryVVWt9f-qFcMo5uQvKBqfUd9MRq7pZQ0Pp_JPTGrI93Fw/viewform?usp=sf_link"
              kind="secondary"
              label="Provide feedback"
              minWidth="220px"
              target="_blank"
            />
            <div className={styles.footer}>
              <div className={styles['footer-content']}>
                <div className={styles['footer-title']}>
                  <b>
                    {agent_id
                      ? 'Remember, you can always find the best talent for your roles with us!'
                      : 'Looking for top talent?'}
                  </b>
                  {!agent_id && (
                    <span>
                      Register with us today to access the best candidates for
                      your roles and stay updated on future casting calls.
                    </span>
                  )}
                </div>
                <Button
                  type="link"
                  href={agent_id ? '/login' : '/register/professional'}
                  label={agent_id ? 'Visit platform' : 'Register now'}
                  minWidth="220px"
                />
                <span>
                  {agent_id
                    ? 'Thank you for being a valued member of our community!'
                    : 'Thank you for considering joining our community!'}
                </span>
              </div>
            </div>
          </div>
        )}
      </section>
    </MainLayout>
  );
}
