import {
  ARTICLE_CATEGORY,
  ARTICLE_DEFAULT_REDIRECT_URL,
  ARTICLE_STATUS,
  mapCategorySlug,
} from '../../constants/articles';
import { param } from '../../utils/sharedServerSideProps';
import { fetchArticle } from '../../services/endpoints/articles';

/**
 * @param {import('next').GetServerSidePropsContext} context
 */
export const getServerSideProps = async ({ query }) => {
  // ToDo: Remove after observing if this ever gets hit
  // should not happen
  const slug = param(query.slug);

  if (!slug || slug.startsWith('page-')) {
    return { notFound: true };
  }

  const article = await fetchArticle(slug);

  if (article.status === 'error') {
    return { notFound: true };
  }
  if (article.categories[0].slug === ARTICLE_CATEGORY.info) {
    return { notFound: true };
  }
  if (article.articleStatus === ARTICLE_STATUS.Deleted) {
    return { redirect: { destination: ARTICLE_DEFAULT_REDIRECT_URL } };
  }

  return {
    redirect: {
      destination: `/blog/${mapCategorySlug(article.categories[0].slug)}/${slug}`,
    },
  };
};

const AcademyPage = ({}) => {
  return <div></div>;
};

export default AcademyPage;
