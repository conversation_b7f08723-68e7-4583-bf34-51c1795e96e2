import { Ab } from '../../../services/ab';
import { formatPage } from '../../../utils/abHelpers';

export const getServerSideProps = async ({ req, res }) => {
    const group = await Ab.groupSession(
      Ab.groupNameSession.landing_page_session_ab_test,
      req,
      res,
    );

    const page = await fetch(
      `${process.env.landingPageUrl}/ab/example-${
        group === 'test' ? 'b' : 'a'
      }`,
    ).then((response) => response.text());

    res.setHeader('Content-Type', 'text/html');
    res.write(formatPage(page));
    res.end();

    return {
        props: {},
    };
};

export default function Nil() {}
