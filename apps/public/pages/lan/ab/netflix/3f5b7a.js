import { Ab } from '../../../../services/ab'
import { formatPage } from '../../../../utils/abHelpers';

export const getServerSideProps = async ({ req, res }) => {
    const group = await Ab.groupSession(
      Ab.groupNameSession.landing_page_netflix_3f5b7a,
      req,
      res,
    );

    const page = await fetch(
      `${process.env.landingPageUrl}/ab/netflix/3f5b7a-${
        group === 'test' ? 'b' : 'a'
      }`,
    ).then((response) => response.text());

    res.setHeader('Content-Type', 'text/html');
    res.write(formatPage(page));
    res.end();

    return {
        props: {},
    };
};

export default function Nil() {}
