import React, { useEffect } from 'react';
import {
  MainBanner,
  Showcase,
  Classroom,
  Spotlight,
  Reviews,
} from '../components/Homepage';
import { FullLayout, MainLayout } from '../components/Layouts';
import seoPageProps from '../utils/seoPageProps';
import ApiNoCache from '../services/apiNoCache';
import { getGatewayHeaders } from '../utils/headerHelper';
import { getPromoImages } from '../utils/promoHelper';
import { Amp } from '../services/amp';

export const getStaticProps = async () => {
  const paths = [
    `/articles?limit=5&show_on_homepage=1`,
    `/promotions?types[]=Castingcalls&limit=5&targets[]=guest`,
  ];

  // ToDo: figure out if clientGateway is needed here
  const requests = await Promise.all(
    paths.map((path) => ApiNoCache.clientGateway(path, getGatewayHeaders('/'))),
  );

  const [articles, promos] = requests.map(({ data }) => data);

  const formattedPromos =
    promos?.data?.map(({ link, priority, images, url }) => ({
      id: Number(link.split('/').pop()),
      priority,
      link,
      url,
      ...getPromoImages(images),
    })) || [];

  const castingCallRequests = await Promise.all(
    formattedPromos.map(({ id }) =>
      ApiNoCache.clientGateway(`/calls/get?id=${id}`, getGatewayHeaders('/')),
    ),
  );

  return {
    props: {
      articles: articles?.items || [],
      promos: formattedPromos.map((promo) => ({
        ...promo,
        castingCall:
          castingCallRequests.find(
            (castingCall) => castingCall.data?.data?.id === promo.id,
          )?.data?.data || {},
      })),
      seoPage: {
        ...(await seoPageProps('/', false)), // due to static rendering
        ogImageUrl: `${process.env.baseUrl}/assets/meta/main.webp`,
      },
    },
  };
};

export default function Home({ articles, promos, seoPage }) {
  useEffect(() => {
    Amp.track(Amp.events.viewHome);
  }, []);

  return (
    <MainLayout isDefaultHeaderVisible isMobileMenuVisible>
      <FullLayout>
        <MainBanner seoPage={seoPage} />
        {promos.length > 0 && <Showcase promos={promos} />}
        {articles.length > 0 && <Classroom articles={articles} />}
        <Spotlight />
        <Reviews />
      </FullLayout>
    </MainLayout>
  );
}
