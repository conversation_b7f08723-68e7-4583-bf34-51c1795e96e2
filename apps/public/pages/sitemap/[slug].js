import { getServerSideSitemapLegacy } from 'next-sitemap';
import { SITEMAP_FILE_PATHS, SiteMapFilePath } from '../../constants/siteMap';
import {
  getBlogSiteMapPages,
  getAdditionalPages,
  getCastingCallSiteMapPages,
  getPrimarySiteMapPages,
  getBlogCategorySiteMapPages,
} from '../../utils/getSiteMapPages';
import ApiNoCache from '../../services/apiNoCache';
import { getGatewayHeaders } from '../../utils/headerHelper';
import { extractResultSiteMap } from '../../utils/extractPromiseResult';

export const getServerSideProps = async (ctx) => {
  const { params, req, res, resolvedUrl } = ctx;
  const { slug } = params;

  if (!slug || !SITEMAP_FILE_PATHS.includes(slug)) {
    return { notFound: true };
  }

  let fields = [];

  if (slug === SiteMapFilePath.Primary) {
    fields = getPrimarySiteMapPages();
  } else if (slug === SiteMapFilePath.CastingCalls) {
    const limit = 24;
    const castingCallsResponse = await ApiNoCache.serverGateway(
      `/calls/search?page=1&limit=${limit}`,
      getGatewayHeaders(resolvedUrl),
      req,
      res,
    );

    const pages = getAdditionalPages(
      castingCallsResponse.data?.data?.total || 0,
      limit,
    );

    const additionalCastingCallResponses = await Promise.allSettled(
      pages.map((page) =>
        ApiNoCache.serverGateway(
          `/calls/search?page=${page}&limit=${limit}`,
          getGatewayHeaders(resolvedUrl),
          req,
          res,
        ),
      ),
    );

    const additionalCastingCalls = additionalCastingCallResponses.flatMap(
      (result) =>
        result.status === 'fulfilled' && result.value?.data?.status === 'ok'
          ? result.value.data.data?.calls || []
          : [],
    );

    const allCastingCalls = [
      ...(castingCallsResponse.data?.data?.calls || []),
      ...additionalCastingCalls,
    ];

    fields = getCastingCallSiteMapPages(allCastingCalls);
  } else {
    const limit = 160;

    const articlesResponse = await ApiNoCache.serverGateway(
      `/articles?page=1&limit=${limit}`,
      getGatewayHeaders(resolvedUrl),
      req,
      res,
    );

    const pages = getAdditionalPages(articlesResponse.data?.total || 0, limit);

    const additionalArticleResponses = await Promise.allSettled(
      pages.map((page) =>
        ApiNoCache.serverGateway(
          `/articles?page=${page}&limit=${limit}`,
          getGatewayHeaders(resolvedUrl),
          req,
          res,
        ),
      ),
    );

    const additionalArticles = additionalArticleResponses.flatMap((result) =>
      extractResultSiteMap(result),
    );

    const allArticles = [
      ...(articlesResponse.data?.items || []),
      ...additionalArticles,
    ];

    fields = [
      ...getBlogCategorySiteMapPages(articlesResponse.data?.filters),
      ...getBlogSiteMapPages(allArticles),
    ];
  }

  if (fields.length === 0) {
    return { notFound: true };
  }

  return getServerSideSitemapLegacy(ctx, fields);
};

export default function SiteMapPage() {}
