import { memo } from 'react';
import {
  ArticlePage,
  processArticleServerSideProps,
} from '../blog/[category]/[slug]';

/**
 * @param {import('next').GetServerSidePropsContext} context
 */
export const getServerSideProps = async (context) => {
  return processArticleServerSideProps(context, 'info');
};

/**
 * @param {import('next').InferGetServerSidePropsType<typeof getServerSideProps>} props
 */
const Info = ({ article, pageConstructorData, seoPage, resolvedUrl }) => {
  return (
    <ArticlePage
      article={article}
      pageConstructorData={pageConstructorData}
      seoPage={seoPage}
      resolvedUrl={resolvedUrl}
      recommendedArticles={[]}
      category="info"
    />
  );
};

export default memo(Info);
