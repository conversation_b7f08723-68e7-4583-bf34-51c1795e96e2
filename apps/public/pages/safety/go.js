import { Seo } from '../../components';
import Link from 'next/link';
import styles from '../../styles/safety.module.scss';
import Image from 'next/image';
import cn from 'classnames';
import { useEffect } from 'react';
import { Amp } from '../../services/amp';

export const getServerSideProps = async ({ query }) => {
  const { url, origin } = query;

  if (!url || !origin) {
    return {
      redirect: {
        destination: '/',
        permanent: false,
      },
    };
  }

  return {
    props: {
      url,
      origin,
    },
  };
};

function Go({ url, origin }) {
  useEffect(() => {
    Amp.track(Amp.events.viewSafetyGo, {
      url,
    });
  }, [url]);

  return (
    <section className={styles.section}>
      <div className={styles.container}>
        <Image
          src="/assets/logo/logo-1.svg"
          width={93}
          height={21}
          alt="logo"
        />
        <Seo
          seoPage={{ h1: `You're leaving allcasting` }}
          allowIndexing={false}
        />
        <span>If you trust this link, select it to continue.</span>
        <Link className={styles.link} href={url}>
          {url}
        </Link>
        <Link className={cn(styles.link, styles['link-back'])} href={origin}>
          <Image
            src="/assets/icons/icon-arrow-2.svg"
            width={16}
            height={16}
            alt="icon"
          />
          <span>Go back</span>
        </Link>
      </div>
    </section>
  );
}

export default Go;
