import { Button, Carousel, Seo } from '../components/index.js';
import styles from '../styles/affiliates.module.scss';
import seoPageProps from '../utils/seoPageProps.js';
import EarnIcon from '../public/assets/affiliates/icon-earn.svg';
import LearnIcon from '../public/assets/affiliates/icon-learn.svg';
import ConnectIcon from '../public/assets/affiliates/icon-connect.svg';
import CreateIcon from '../public/assets/affiliates/icon-create.svg';
import { xTracking } from '../services/tracking.js';
import cn from 'classnames';
import { quotes } from '../constants/affiliates.js';
// import EntertechLogo from '../public/assets/affiliates/entertech.svg';
import { MainLayout } from '../components/Layouts/index.js';
import { sharedServerSideProps } from '../utils/sharedServerSideProps';
import { useEffect } from 'react';
import { Amp } from '../services/amp';
import Image from 'next/image';

export const getServerSideProps = async ({ resolvedUrl, req, res }) => {
  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      seoPage: {
        ...(await seoPageProps(resolvedUrl)),
      },
    },
  };
};

export default function Affiliates({ seoPage }) {
  const track = () => {
    const trackingParams = {
      utm_source: 'affiliate-page',
      utm_medium: 'unknown',
      utm_campaign: 'cta',
    };

    xTracking(trackingParams);
  };

  useEffect(() => {
    Amp.track(Amp.events.viewAffiliates);
  }, []);

  return (
    <MainLayout isDefaultHeaderVisible isMobileMenuVisible>
      <div className={styles['main-banner']} data-cy="affiliates-banner">
        <Seo seoPage={seoPage} />
        <div className={cn(styles.container, styles['main-banner-body'])}>
          <div className={styles['main-banner-welcome']}>
            <div className={styles['main-banner-text']}>AFFILIATE PROGRAM</div>
            <div className={styles['main-banner-title']} data-cy="main-title">
              Help your community
              <br />
              get allcasting
            </div>
            <Button
              type="link"
              href="https://forms.gle/xnNkhd1miTDFRsYU8"
              color="purple"
              label="APPLY TODAY"
              minWidth="220px"
              isSEO
              onClick={track}
            />
          </div>
        </div>
      </div>

      <div className={styles.container}>
        <div className={styles['intro-wrapper']}>
          <div className={styles['intro-block']}>
            <div className={styles['intro-image']}>
              <Image
                width="646"
                height="600"
                src="/assets/affiliates/affiliate.webp"
                alt="Meet the best job-searching affiliate program"
              />
            </div>
            <div className={styles['intro-description']}>
              <h2>Meet the best job-searching affiliate program</h2>
              <p>
                Entertainment is for everyone, including your audience.
                Allcasting helps thousands of actors, models, and performers
                kickstart their careers daily. Partner with us to make auditions
                more accessible to the people around you. Plus, you’ll earn $2+
                for every referral.
              </p>
              <Button
                type="link"
                kind="secondary"
                href="https://forms.gle/xnNkhd1miTDFRsYU8"
                color="purple"
                label="Become an affiliate"
                minWidth="220px"
                isSEO
                onClick={track}
              />
            </div>
          </div>
        </div>
      </div>

      <div className={styles.container}>
        <h2 className={styles['more-title']}>But wait, there&apos;s more...</h2>
        <div className={styles['more-block']}>
          <div className={styles['more-item']}>
            <div className={styles['more-icon']}>
              <EarnIcon />
            </div>
            <div className={styles['more-description']}>
              <span>Earn</span>
              <p>Earn $2+ for each person who signs up.</p>
            </div>
          </div>
          <div className={styles['more-item']}>
            <div className={styles['more-icon']}>
              <LearnIcon />
            </div>
            <div className={styles['more-description']}>
              <span>Learn</span>
              <p>
                Track your performance with full reports on earnings, conversion
                stats, and more.
              </p>
            </div>
          </div>
          <div className={styles['more-item']}>
            <div className={styles['more-icon']}>
              <CreateIcon />
            </div>
            <div className={styles['more-description']}>
              <span>Create</span>
              <p>
                Get access to professional, high-quality images and creative
                assets.
              </p>
            </div>
          </div>
          <div className={styles['more-item']}>
            <div className={styles['more-icon']}>
              <ConnectIcon />
            </div>
            <div className={styles['more-description']}>
              <span>Connect</span>
              <p>
                Get our entertainment affiliates newsletter with info,
                incentives, and top acting/modeling tips.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className={styles.container}>
        <div className={styles['how-it-works-block']}>
          <h2>Here&apos;s how it works</h2>
          <div className={styles['how-it-works-left']}>
            <div className={styles['how-it-works-image']}>
              <Image
                width={646}
                height={450}
                src="/assets/affiliates/sign-up.webp"
                alt="Sign up for free"
              />
            </div>
            <div className={styles['how-it-works-description']}>
              <h3>Sign up for free</h3>
              <p>
                Apply here with a link to your blog, social media account, or
                website to get started.
              </p>
            </div>
          </div>
          <div className={styles['how-it-works-right']}>
            <div className={styles['how-it-works-image']}>
              <Image
                width={646}
                height={450}
                src="/assets/affiliates/start-earning.webp"
                alt="Start earning cash"
              />
            </div>
            <div className={styles['how-it-works-description']}>
              <h3>Start earning cash</h3>
              <p>
                Set up terms and affiliate tracking links to earn commission on
                each successful signup.
              </p>
            </div>
          </div>
          <div className={styles['how-it-works-left']}>
            <div className={styles['how-it-works-image']}>
              <Image
                width={646}
                height={450}
                src="/assets/affiliates/spread-the-word.webp"
                alt="Spread the world"
              />
            </div>
            <div className={styles['how-it-works-description']}>
              <h3>Spread the world</h3>
              <p>
                Start bringing allcasting to your community, and we’ll support
                you every step of the way.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className={styles['stats-section']} data-cy="stats-section">
        <div
          className={cn(styles.container, styles['stats-section-container'])}
        >
          <div className={styles['stat-item']}>
            <div>10,000+</div>
            <p>CASTING OPPORTUNITIES</p>
          </div>
          <div className={styles['stat-item']}>
            <div>500,000</div>
            <p>MONTHLY SITE VISITS</p>
          </div>
          <div className={styles['stat-item']}>
            <div>2M+</div>
            <p>ACTIVE MEMBERS</p>
          </div>
        </div>
      </div>

      <div className={styles['company-section']} data-cy="company-section">
        <div
          className={cn(styles.container, styles['company-section-container'])}
        >
          <div className={styles['company-info']}>
            <div className={styles['carousel-quote-container']}>
              <Carousel
                className="carousel-quote"
                startIndex={0}
                delay={5000}
                loop
                playOnInit
                enablePagination
                enableArrowNavigation
                arrowType="round"
              >
                {quotes.map(({ text, author }, i) => (
                  <div className={styles['quote-box']} key={i}>
                    {/* <EntertechLogo className={styles['quote-icon']} /> */}
                    <div className={styles['quote-text']}>{text}</div>
                    <div>
                      <div className={styles['quote-author']}>{author}</div>
                    </div>
                  </div>
                ))}
              </Carousel>
            </div>
          </div>
        </div>
      </div>

      <div className={styles['cta-section']} data-cy="cta-section">
        <div className={cn(styles.container, styles['cta-section-container'])}>
          <div className={styles['cta-heading']}>
            Become an allcasting
            <br />
            affiliate today
          </div>
          <div className={styles['cta-btn-wrapper']}>
            <Button
              type="link"
              href="https://forms.gle/xnNkhd1miTDFRsYU8"
              color="purple"
              label="Start earning"
              minWidth="200px"
              isSEO
              onClick={track}
            />
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
