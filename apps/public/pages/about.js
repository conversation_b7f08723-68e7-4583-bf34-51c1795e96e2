import { Seo } from '../components';
import { MainLayout, PageLayout } from '../components/Layouts';
import styles from '../styles/about.module.scss';
import Image from 'next/image';
import seoPageProps from '../utils/seoPageProps';
import Link from 'next/link';
import { sharedServerSideProps } from '../utils/sharedServerSideProps';
import { useEffect } from 'react';
import { Amp } from '../services/amp';

export const getServerSideProps = async ({ resolvedUrl, req, res }) => {
  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      seoPage: await seoPageProps(resolvedUrl),
    },
  };
};

export default function About({ seoPage }) {
  useEffect(() => {
    Amp.track(Amp.events.viewAbout);
  }, []);

  return (
    <MainLayout
      isDefaultHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
      isSaleHeaderVisible
      isMobileSubscribeHeaderVisible
    >
      <PageLayout newMargins>
        <section className={styles['about-section']}>
          <Seo seoPage={seoPage} />
          <p>
            allcasting is a casting network that connects talent and casting
            professionals. The one-stop destination for actors, models and
            performers to find casting calls and for casting professionals to
            find the right talent for their projects.
          </p>
          <p>
            It&apos;s simple: We help performers get cast in the best projects
            for Movies, TV, and Fashion, and casting professionals to discover
            incredible talent with minimal effort!
          </p>
          <p>
            allcasting&apos;s mission is to create a product that empowers
            entertainment professionals globally to achieve their dreams with
            powerful and easy to use tools that are intuitive, functional,
            pleasing to use, and are perfectly matched to the user needs.
          </p>
          <p>
            allcasting was founded in 2004, and has been a legit part of the
            entertainment industry for nearly 20 years. We continue to grow
            rapidly and searching for new ways to introduce technology and
            innovative solutions that will change the entertainment industry
            forever. As of now, allcasting has nearly two million registered
            talents and hundreds of roles available.
          </p>
          <hr className={styles.separator} />
          <figure className={styles.quote}>
            <blockquote>
              Our vision is to disrupt and revolutionize the entertainment
              industry. We will succeed by anticipating and fulfilling industry
              needs beyond their expectations. By creating a new-age product
              that answers the unique needs of the entertainment industry
            </blockquote>
            <figcaption>
              <b>CEO of allcasting</b>
            </figcaption>
          </figure>
          <hr className={styles.separator} />
          <h2>Interesting facts</h2>
          <div className={styles['facts-container']}>
            <div className={styles['fact-card']}>
              <Image
                src="/assets/icons/icon-paid.svg"
                height={40}
                width={40}
                alt=""
              />
              <div>Most of our casting calls are paid</div>
            </div>
            <div className={styles['fact-card']}>
              <Image
                src="/assets/icons/icon-highest-pay.svg"
                height={40}
                width={40}
                alt=""
              />
              <div>
                One of the highest-paid jobs published on allcasting was paying{' '}
                <b>$15,500/project</b>
              </div>
            </div>
            <div className={styles['fact-card']}>
              <Image
                src="/assets/icons/icon-users.svg"
                height={40}
                width={40}
                alt=""
              />
              <div>
                Nearly <b>2 million</b> active talent
              </div>
            </div>
            <div className={styles['fact-card']}>
              <Image
                src="/assets/icons/icon-casting-call-1.svg"
                height={40}
                width={40}
                alt=""
              />
              <div>
                On average, we add <b>90</b> new casting calls <b>DAILY</b>
              </div>
            </div>
          </div>
          <hr className={styles.separator} />
          <h2>Other info</h2>
          <div className={styles['other-container']}>
            <Link
              href="/how-to-cancel-subscription"
              className={styles['other-card-link']}
            >
              <Image
                src="/assets/icons/icon-subscription-cancel.svg"
                height={40}
                width={40}
                alt=""
              />
              Subscription Cancellation
            </Link>
            <Link href="/refund-policy" className={styles['other-card-link']}>
              <Image
                src="/assets/icons/icon-refund.svg"
                height={40}
                width={40}
                alt=""
              />
              Refund Policy
            </Link>
            <Link href="/prices" className={styles['other-card-link']}>
              <Image
                src="/assets/icons/icon-price.svg"
                height={40}
                width={40}
                alt=""
              />
              Prices
            </Link>
          </div>
          <hr className={styles.separator} />
          <h2>Talent success stories</h2>
          <div className={styles['story-container']}>
            <div className={styles['talent-card']}>
              <div className={styles['image-container']}>
                <div className={styles['image-inner-container']}>
                  <Image
                    className={styles.image}
                    src="/assets/about/willie-wiggins-jr.svg"
                    width={64}
                    height={64}
                    alt=""
                  />
                </div>
              </div>
              <div className={styles['card-description']}>
                <div className={styles['description-header']}>
                  <div className={styles['image-container']}>
                    <div className={styles['image-inner-container']}>
                      <Image
                        className={styles.image}
                        src="/assets/about/willie-wiggins-jr.svg"
                        width={64}
                        height={64}
                        alt=""
                      />
                    </div>
                  </div>
                  <span className={styles.name}>Willie Wiggins Jr</span>
                </div>
                <div>
                  In two weeks of joining allcasting.com, I&apos;ve gotten six
                  invites for auditions and picked up two jobs so far. One in
                  which I just wrapped up with a LEAD-ROLE for a VH1-Tv true
                  story documentary and another LEAD-ROLE for a film company
                  that&apos;s flying me to Louisiana to start the filming of a
                  major film. I have two more auditions lined up for next week!
                  I should have joined allcasting.com years ago!
                </div>
              </div>
            </div>
            <div className={styles['talent-card']}>
              <div className={styles['image-container']}>
                <div className={styles['image-inner-container']}>
                  <Image
                    className={styles.image}
                    src="/assets/about/shenee-moore.svg"
                    width={64}
                    height={64}
                    alt=""
                  />
                </div>
              </div>
              <div className={styles['card-description']}>
                <div className={styles['description-header']}>
                  <div className={styles['image-container']}>
                    <div className={styles['image-inner-container']}>
                      <Image
                        className={styles.image}
                        src="/assets/about/shenee-moore.svg"
                        width={64}
                        height={64}
                        alt=""
                      />
                    </div>
                  </div>
                  <span className={styles.name}>Shenee Moore</span>
                </div>
                <div>
                  allcasting.com is the greatest…I am new to the industry and
                  now have a photo shoot set up and a potential role in a short
                  film. Thank you, allcasting.com, for offering a platform for
                  these opportunities!! My dreams are finally coming true 🤩
                </div>
              </div>
            </div>
            <div className={styles['talent-card']}>
              <div className={styles['image-container']}>
                <div className={styles['image-inner-container']}>
                  <Image
                    className={styles.image}
                    src="/assets/about/renee-sunshine.svg"
                    width={64}
                    height={64}
                    alt=""
                  />
                </div>
              </div>
              <div className={styles['card-description']}>
                <div className={styles['description-header']}>
                  <div className={styles['image-container']}>
                    <div className={styles['image-inner-container']}>
                      <Image
                        className={styles.image}
                        src="/assets/about/renee-sunshine.svg"
                        width={64}
                        height={64}
                        alt=""
                      />
                    </div>
                  </div>
                  <span className={styles.name}>Renee Sunshine</span>
                </div>
                <div>
                  I was cast as a regular stand in for &quot;Praise This&quot;,
                  a major film coming soon by Will Packer/NBC universal and I
                  have been offered multiple print modeling positions as well! I
                  am super excited to see what else is in store for me through
                  allcasting.com. I have received some scam offers too so please
                  be mindful when giving out your information. Most of the scam
                  ads have horrible spelling but some are ALMOST believable so
                  just do your research. Good luck and God bless!
                </div>
              </div>
            </div>
          </div>
          <h2>Casting directors success story</h2>
          <div className={styles['story-container']}>
            <div className={styles['agent-card']}>
              <div className={styles['image-container']}>
                <div className={styles['agent-image']}>
                  <Image
                    src="/assets/icons/icon-quote-3.svg"
                    width={22}
                    height={18}
                    alt=""
                  />
                </div>
              </div>
              <div className={styles['card-description']}>
                <div className={styles['description-header']}>
                  <div className={styles['agent-image']}>
                    <Image
                      src="/assets/icons/icon-quote-3.svg"
                      width={22}
                      height={18}
                      alt=""
                    />
                  </div>
                  <div className={styles['details-container']}>
                    <span className={styles.name}>Julia Samersova,</span>
                    <span className={styles.company}>Cast Inc</span>
                  </div>
                </div>
                <div>
                  allcasting has been so awesome in helping me find those
                  diamonds in the rough that don&apos;t yet have agency
                  representation! The team sends me very selective and
                  well-edited submissions that are always on point and they are
                  always on top of it!
                </div>
              </div>
            </div>
          </div>
          <h2>Step into the spotlight today</h2>
        </section>
      </PageLayout>
    </MainLayout>
  );
}
