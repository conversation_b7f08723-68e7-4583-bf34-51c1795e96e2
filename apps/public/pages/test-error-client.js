import React, { useState } from 'react';
import Image from 'next/image';

const TestErrorClient = () => {
  const array = [];
  const value = {};
  const [isException, setIsException] = useState(false);

  const onTypeErrorProperty = () => {
    console.log(value.secondValue.thirdValue);
  };

  const onTypeErrorPropertyFunctionCall = () => {
    value.getExampleError();
  };

  const onReferenceError = () => {
    getExampleError();
  };

  return (
    <div>
      <h1>Test client-side errors</h1>
      <button onClick={onTypeErrorProperty}>Type error: property</button>
      <button onClick={onTypeErrorPropertyFunctionCall}>
        Type error: property function call
      </button>
      <button onClick={onReferenceError}>Reference error</button>
      <button onClick={() => setIsException(true)}>Type error: template</button>
      {isException && <Image src={array[0].src} alt="example" />}
    </div>
  );
};

export default TestErrorClient;
